{"name": "justin<PERSON><PERSON>/json-schema", "type": "library", "description": "A library to validate a json schema.", "keywords": ["json", "schema"], "homepage": "https://github.com/jsonrainbow/json-schema", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0", "ext-json": "*", "marc-mabe/php-enum": "^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^8.5", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "marc-mabe/php-enum-phpstan": "^2.0"}, "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "autoload-dev": {"psr-4": {"JsonSchema\\Tests\\": "tests/"}}, "repositories": [{"type": "package", "package": {"name": "json-schema/json-schema-test-suite", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/json-schema/JSON-Schema-Test-Suite", "reference": "1.2.0"}}}], "bin": ["bin/validate-json"], "scripts": {"coverage": "phpunit --coverage-text", "style-check": "php-cs-fixer fix --dry-run --verbose --diff", "style-fix": "php-cs-fixer fix --verbose", "test": "phpunit", "testOnly": "phpunit --colors --filter", "phpstan": "@php phpstan", "phpstan-generate-baseline": "@php phpstan --generate-baseline"}}