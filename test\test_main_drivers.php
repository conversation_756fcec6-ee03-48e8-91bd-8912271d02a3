<?php
require_once __DIR__ . '/config.php';
require_once 'DatabaseInteraction.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Initialize database connection
$db = new DatabaseInteraction();
$conn = $db->connect();

// Test all domain categories
$domain_categories = $db->getAllDomainCategories();

echo "<h1>Testing Main Drivers by Domain Category</h1>";

foreach ($domain_categories as $category) {
    echo "<h2>Domain Category: " . htmlspecialchars($category) . "</h2>";
    
    $main_drivers = $db->getMainDriversByDomainCategory($category);
    
    if (empty($main_drivers)) {
        echo "<p>No main drivers found for this category.</p>";
    } else {
        echo "<ul>";
        foreach ($main_drivers as $driver) {
            echo "<li>" . htmlspecialchars($driver) . "</li>";
        }
        echo "</ul>";
    }
    
    echo "<hr>";
}
