-- Upload Scale Configurations Table
-- This table stores user-specified scale ranges for each data upload
CREATE TABLE upload_scale_configurations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data_id VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    
    -- CSAT Configuration (MANDATORY)
    csat_scale_type ENUM('1-5', '1-7', '1-10', 'custom') NOT NULL,
    csat_min_value INT NOT NULL,
    csat_max_value INT NOT NULL,
    csat_baseline DECIMAL(5,2) NOT NULL,
    
    -- NPS Configuration (MANDATORY)
    nps_scale_type ENUM('0-10', '1-10', '1-5', '1-7', 'custom') NOT NULL,
    nps_min_value INT NOT NULL,
    nps_max_value INT NOT NULL,
    nps_promoter_threshold INT NOT NULL,
    nps_detractor_threshold INT NOT NULL,
    
    -- Internal Score Configuration (MANDATORY - can be 'not_used')
    internal_score_scale_type ENUM('1-5', '1-10', '0-100', 'custom', 'not_used') NOT NULL DEFAULT 'not_used',
    internal_score_min_value INT NULL,
    internal_score_max_value INT NULL,
    internal_score_baseline DECIMAL(5,2) NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE KEY unique_data_upload (data_id, user_id),
    INDEX idx_data_id (data_id),
    INDEX idx_user_id (user_id),
    
    -- Validation constraints
    CHECK (csat_min_value < csat_max_value),
    CHECK (nps_min_value < nps_max_value),
    CHECK (internal_score_min_value IS NULL OR internal_score_max_value IS NULL OR internal_score_min_value < internal_score_max_value)
);
