<?php
// Test script to insert test data for LOB, Vendor, Location, and Partner sentiment charts

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../DatabaseInteraction.php';

// Initialize database connection
$db = new DatabaseInteraction();
$conn = $db->connect();

// User ID to use (usually 2 for the logged-in user)
$user_id = 2;
$data_id = 'test_data_' . date('Ymd_His');

// Sample data to insert
$test_data = [
    // LOB data
    [
        'lob' => 'Cable',
        'vendor' => 'BGO',
        'location' => 'New York',
        'partner' => 'Partner X',
        'sentiment' => 'Positive',
        'count' => 12
    ],
    [
        'lob' => 'Cable',
        'vendor' => 'Sitel',
        'location' => 'Chicago',
        'partner' => 'Partner Y',
        'sentiment' => 'Negative',
        'count' => 38
    ],
    [
        'lob' => 'Care',
        'vendor' => 'Intel',
        'location' => 'Los Angeles',
        'partner' => 'Partner Z',
        'sentiment' => 'Positive',
        'count' => 15
    ],
    [
        'lob' => 'Care',
        'vendor' => 'Intel',
        'location' => 'Boston',
        'partner' => 'Partner X',
        'sentiment' => 'Neutral',
        'count' => 3
    ],
    [
        'lob' => 'Care',
        'vendor' => 'Intel',
        'location' => 'Miami',
        'partner' => 'Partner Y',
        'sentiment' => 'Negative',
        'count' => 30
    ],
    [
        'lob' => 'Phone',
        'vendor' => 'BGO',
        'location' => 'Seattle',
        'partner' => 'Partner Z',
        'sentiment' => 'Neutral',
        'count' => 8
    ]
];

echo "Inserting test data with data_id: $data_id\n";

// Insert test data
$inserted_count = 0;
foreach ($test_data as $data) {
    $lob = $data['lob'];
    $vendor = $data['vendor'];
    $location = $data['location'];
    $partner = $data['partner'];
    $sentiment = $data['sentiment'];
    $count = $data['count'];

    // Insert multiple records based on count
    for ($i = 0; $i < $count; $i++) {
        $pid = 'pid_' . $lob . '_' . $vendor . '_' . $i;
        $comment = "Test comment for $lob from $vendor in $location with $partner (Record $i)";

        // Insert into analyzed_comments table
        $query = "INSERT INTO analyzed_comments (
            comment, main_driver, sub_driver, sentiment, user_id, data_id, csat, nps, pid,
            painpointscustomerfrustrations, detailedexplanationofthecomment, suggestionsforimprovement, domain_category,
            lob, vendor, location, partner, created_at
        ) VALUES (
            :comment, 'Test Driver', 'Test Sub-Driver', :sentiment, :user_id, :data_id, 5, 5, :pid,
            'Test pain points', 'Test explanation', 'Test suggestions', 'Test Domain',
            :lob, :vendor, :location, :partner, NOW()
        )";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':comment', $comment);
        $stmt->bindParam(':sentiment', $sentiment);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':pid', $pid);
        $stmt->bindParam(':lob', $lob);
        $stmt->bindParam(':vendor', $vendor);
        $stmt->bindParam(':location', $location);
        $stmt->bindParam(':partner', $partner);

        try {
            $stmt->execute();
            $inserted_count++;
        } catch (PDOException $e) {
            echo "Error inserting record: " . $e->getMessage() . "\n";
        }
    }
}

echo "Inserted $inserted_count records successfully.\n";
echo "Done.\n";
