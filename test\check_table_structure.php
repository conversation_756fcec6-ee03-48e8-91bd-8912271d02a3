<?php
// <PERSON><PERSON><PERSON> to check the structure of the analyzed_comments table

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database\n";
    
    // Get table structure
    $query = "SHOW CREATE TABLE analyzed_comments";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Table structure:\n";
    echo $result['Create Table'] . "\n\n";
    
    // Get indexes
    $query = "SHOW INDEX FROM analyzed_comments";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Indexes:\n";
    foreach ($indexes as $index) {
        echo "Index: " . $index['Key_name'] . ", Column: " . $index['Column_name'] . ", Non_unique: " . $index['Non_unique'] . "\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
