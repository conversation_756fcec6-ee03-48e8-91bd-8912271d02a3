<?php
// Direct database connection test

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Testing direct database connection...\n";

// Database credentials - REPLACE WITH YOUR ACTUAL CREDENTIALS
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    // Create PDO connection
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected successfully to database: $dbname on host: $host\n";
    
    // Test SELECT on feedback_data
    $testQuery = "SELECT COUNT(*) as count FROM feedback_data WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    echo "SELECT test on feedback_data: Found " . $testResult['count'] . " records for data_id 682b54974bab6\n";
    
    // Test SELECT on analyzed_comments
    $testQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    echo "SELECT test on analyzed_comments: Found " . $testResult['count'] . " records for data_id 682b54974bab6\n";
    
    // Test INSERT on comment_queue with a transaction (will be rolled back)
    $conn->beginTransaction();
    try {
        $testInsertQuery = "INSERT INTO comment_queue (comment, data_id, user_id, csat, nps, pid, status) 
                           VALUES ('TEST_PERMISSION_CHECK', '682b54974bab6', 1, 0, 0, '0', 'pending')";
        $testInsertStmt = $conn->prepare($testInsertQuery);
        $testInsertResult = $testInsertStmt->execute();
        
        if ($testInsertResult) {
            echo "INSERT test on comment_queue: Successful\n";
            
            // Verify the record was inserted
            $verifyQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE comment = 'TEST_PERMISSION_CHECK'";
            $verifyStmt = $conn->prepare($verifyQuery);
            $verifyStmt->execute();
            $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            echo "Verification: Found " . $verifyResult['count'] . " test records in comment_queue\n";
        } else {
            echo "INSERT test on comment_queue failed: " . json_encode($testInsertStmt->errorInfo()) . "\n";
        }
        
        // Always rollback the test insert
        $conn->rollBack();
        echo "Test transaction rolled back successfully\n";
    } catch (PDOException $e) {
        $conn->rollBack();
        echo "INSERT test on comment_queue failed: " . $e->getMessage() . "\n";
    }
    
    echo "Database test completed successfully\n";
    
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}
