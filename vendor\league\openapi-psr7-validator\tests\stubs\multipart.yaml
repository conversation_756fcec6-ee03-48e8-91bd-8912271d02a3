openapi: 3.0.2
info:
  title: Multipart API
  version: 0.0.1
  contact:
    name: <PERSON>
    url: https://github.com/lezhnev74/openapi-psr7-validator
    email: <EMAIL>
paths:
  /multipart:
    post:
      summary: Post multipart body
      operationId: post-multipart-body
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - id
                - address
                - profileImage
              properties:
                id:            # Part 1 (string value)
                  type: string
                  format: uuid
                address:       # Part2 (object)
                  type: object
                  properties:
                    street:
                      type: string
                    city:
                      type: string
                profileImage:  # Part 3 (an image)
                  type: string
                  format: binary

      responses:
        204:
          description: good post
  /multipart/files:
    post:
      summary: ---
      operationId: post-multipart-files
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                filename:
                  type: array
                  items:
                    type: string
                    format: binary

      responses:
        204:
          description: good post
  /multipart/encoding:
    post:
      summary: ---
      operationId: post-multipart-encoding
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - image
              properties:
                image:
                  type: string
                  format: binary
                description:
                  type: string
            encoding:
              image:
                contentType: specific/type
              description:
                contentType: text/plain
      responses:
        204:
          description: good post
  /multipart/encoding/multiple:
    post:
      summary: ---
      operationId: post-multipart-encoding-multiple
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                data:
                  type: string
            encoding:
              data:
                contentType: application/xml; charset="utf-8", application/json, text/*
      responses:
        204:
          description: good post
  /multipart/encoding/wildcard:
    post:
      summary: Post multipart body with wildcard encoding of one part
      operationId: post-multipart-encoding-wildcard
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary
            encoding:
              image:
                contentType: image/*
      responses:
        204:
          description: good post
  /multipart/headers:
    post:
      summary: ---
      operationId: post-multipart-headers
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary
            encoding:
              image:
                contentType: specific/type
                headers:
                  X-Custom-Header:
                    description: This is a custom header
                    schema:
                      type: string
                  X-Numeric-Header:
                    description: This header must be numeric
                    schema:
                      type: number
      responses:
        204:
          description: good post
  /multipart-deserialization:
    post:
      summary: Post multipart body
      operationId: post-multipart-body-deserializtion
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - id
                - secure
                - code
              properties:
                id:            # Part 1 (numeric value)
                  type: number
                secure:        # Part 2 (bool value)
                  type: boolean
                code:          # Part 3 (int value)
                  type: integer

      responses:
        204:
          description: good post
