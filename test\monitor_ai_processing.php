<?php
/**
 * <PERSON><PERSON><PERSON> to monitor the AI processing system
 * This script checks if records are being properly processed from comment_queue to analyzed_comments
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to monitor
$data_id = $argv[1] ?? '682b54974bab6';

// Log file
$log_file = 'ai_monitor.log';

// Function to log messages
function log_message($message) {
    global $log_file;

    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;

    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);

    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting AI processing monitor for data_id: $data_id");

    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    log_message("Connected to database");

    // Get initial counts
    $countQuery = "
        SELECT
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id) as queue_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id AND status = 'pending') as pending_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id AND status = 'processing') as processing_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id AND status = 'processed') as processed_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id AND status = 'failed') as failed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);

    log_message("Initial counts:");
    log_message("- Feedback data: " . $counts['feedback_count']);
    log_message("- Analyzed comments: " . $counts['analyzed_count']);
    log_message("- Total in queue: " . $counts['queue_count']);
    log_message("- Pending in queue: " . $counts['pending_count']);
    log_message("- Processing in queue: " . $counts['processing_count']);
    log_message("- Processed in queue: " . $counts['processed_count']);
    log_message("- Failed in queue: " . $counts['failed_count']);

    // Check for any active cron jobs or processes
    log_message("Checking for active AI processing jobs...");

    // Check if updated_at column exists in comment_queue table
    $checkColumnQuery = "
        SELECT COUNT(*) as count
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = :dbname
        AND TABLE_NAME = 'comment_queue'
        AND COLUMN_NAME = 'updated_at'
    ";
    $checkColumnStmt = $conn->prepare($checkColumnQuery);
    $checkColumnStmt->bindParam(':dbname', $dbname);
    $checkColumnStmt->execute();
    $columnExists = $checkColumnStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

    // This is a simple check - you might need to adjust this based on your actual system
    if ($columnExists) {
        $activeJobsQuery = "
            SELECT COUNT(*) as count
            FROM comment_queue
            WHERE status = 'processing'
            AND updated_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ";
    } else {
        // Fallback if updated_at column doesn't exist
        $activeJobsQuery = "
            SELECT COUNT(*) as count
            FROM comment_queue
            WHERE status = 'processing'
        ";
    }

    $activeJobsStmt = $conn->prepare($activeJobsQuery);
    $activeJobsStmt->execute();
    $activeJobs = $activeJobsStmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($activeJobs > 0) {
        log_message("Found $activeJobs active AI processing jobs");
    } else {
        log_message("No active AI processing jobs found");
    }

    // Check for recently processed records
    log_message("Checking for recently processed records...");

    // Check if created_at column exists in analyzed_comments table
    $checkColumnQuery = "
        SELECT COUNT(*) as count
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = :dbname
        AND TABLE_NAME = 'analyzed_comments'
        AND COLUMN_NAME = 'created_at'
    ";
    $checkColumnStmt = $conn->prepare($checkColumnQuery);
    $checkColumnStmt->bindParam(':dbname', $dbname);
    $checkColumnStmt->execute();
    $columnExists = $checkColumnStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

    if ($columnExists) {
        $recentlyProcessedQuery = "
            SELECT COUNT(*) as count
            FROM analyzed_comments
            WHERE data_id = :data_id
            AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ";
    } else {
        // Fallback if created_at column doesn't exist
        $recentlyProcessedQuery = "
            SELECT COUNT(*) as count
            FROM analyzed_comments
            WHERE data_id = :data_id
        ";
        log_message("Note: created_at column not found in analyzed_comments table. Cannot determine recently processed records.");
    }

    $recentlyProcessedStmt = $conn->prepare($recentlyProcessedQuery);
    $recentlyProcessedStmt->bindParam(':data_id', $data_id);
    $recentlyProcessedStmt->execute();
    $recentlyProcessed = $recentlyProcessedStmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($recentlyProcessed > 0) {
        log_message("Found $recentlyProcessed recently processed records");
    } else {
        log_message("No recently processed records found");
    }

    // Check for records that have been in the queue for a long time
    log_message("Checking for stalled records in the queue...");

    // Check if created_at column exists in comment_queue table
    $checkColumnQuery = "
        SELECT COUNT(*) as count
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = :dbname
        AND TABLE_NAME = 'comment_queue'
        AND COLUMN_NAME = 'created_at'
    ";
    $checkColumnStmt = $conn->prepare($checkColumnQuery);
    $checkColumnStmt->bindParam(':dbname', $dbname);
    $checkColumnStmt->execute();
    $columnExists = $checkColumnStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

    if ($columnExists) {
        $stalledQuery = "
            SELECT COUNT(*) as count
            FROM comment_queue
            WHERE data_id = :data_id
            AND status = 'pending'
            AND created_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        ";
    } else {
        // Fallback if created_at column doesn't exist
        $stalledQuery = "
            SELECT COUNT(*) as count
            FROM comment_queue
            WHERE data_id = :data_id
            AND status = 'pending'
        ";
        log_message("Note: created_at column not found in comment_queue table. Cannot determine stalled records.");
    }

    $stalledStmt = $conn->prepare($stalledQuery);
    $stalledStmt->bindParam(':data_id', $data_id);
    $stalledStmt->execute();
    $stalled = $stalledStmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($stalled > 0) {
        log_message("Found $stalled stalled records in the queue (pending for more than 30 minutes)");
    } else {
        log_message("No stalled records found in the queue");
    }

    // Check for any errors in the AI processing
    log_message("Checking for errors in AI processing...");

    $errorQuery = "
        SELECT COUNT(*) as count, error
        FROM comment_queue
        WHERE data_id = :data_id
        AND status = 'failed'
        GROUP BY error
        ORDER BY COUNT(*) DESC
        LIMIT 5
    ";
    $errorStmt = $conn->prepare($errorQuery);
    $errorStmt->bindParam(':data_id', $data_id);
    $errorStmt->execute();
    $errors = $errorStmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($errors) > 0) {
        log_message("Found " . count($errors) . " types of errors in AI processing");
        foreach ($errors as $error) {
            log_message("- " . $error['count'] . " records failed with error: " . $error['error']);
        }
    } else {
        log_message("No errors found in AI processing");
    }

    // Conclusion
    log_message("\nMonitoring Summary:");

    if ($counts['pending_count'] > 0 && $activeJobs == 0 && $recentlyProcessed == 0) {
        log_message("There are " . $counts['pending_count'] . " pending records in the queue, but no active AI processing jobs and no recently processed records.");
        log_message("This suggests that the AI processing system is not running or is not picking up records from the queue.");
        log_message("You should check if the cron job or process that runs the AI processing is active.");
    } else if ($counts['pending_count'] == 0 && $counts['queue_count'] == 0 && $counts['analyzed_count'] < $counts['feedback_count']) {
        log_message("The queue is empty, but there are still " . ($counts['feedback_count'] - $counts['analyzed_count']) . " records that haven't been processed.");
        log_message("This suggests that records are being removed from the queue without being properly processed.");
        log_message("You should check if there's a process that's removing records from the queue without processing them.");
    } else if ($activeJobs > 0 && $recentlyProcessed == 0) {
        log_message("There are active AI processing jobs, but no recently processed records.");
        log_message("This suggests that the AI processing system is running but is failing to process records.");
        log_message("You should check the AI processing system for errors.");
    } else if ($stalled > 0) {
        log_message("There are $stalled records that have been in the queue for more than 30 minutes.");
        log_message("This suggests that the AI processing system is not processing all records in a timely manner.");
        log_message("You should check if the AI processing system is running at full capacity.");
    } else if ($counts['pending_count'] > 0 && $activeJobs > 0 && $recentlyProcessed > 0) {
        log_message("The AI processing system appears to be working correctly.");
        log_message("There are " . $counts['pending_count'] . " pending records in the queue, $activeJobs active jobs, and $recentlyProcessed recently processed records.");
    } else {
        log_message("The status of the AI processing system is unclear.");
        log_message("You should check the AI processing system logs for more information.");
    }

} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
