openapi: 3.1.0
info:
  title: Weather API
  version: 0.0.1
  contact:
    name: <PERSON>
    url: https://github.com/thephpleague/openapi-psr7-validator
    email: <EMAIL>
paths:
  /read/{param1}/from/{param2}:
    parameters:
      - in: path
        name: param1
        required: true
        schema:
          type: string
      - in: path
        name: param2
        required: true
        schema:
          type: integer
  /read:
    parameters:
      - name: offset
        in: query
        schema:
          type: integer
        required: true
    get:
      summary: Read data
      operationId: read
      parameters:
        - name: filter
          in: query
          schema:
            type: string
        - name: limit
          in: query
          required: true
          schema:
            type: integer
      responses:
        200:
          description: good read
          content:
            text/plain:
              schema:
                type: string

  /request-body:
    post:
      summary: Request post with body payload
      requestBody:
        description: Request body
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
          text/plain:
            schema:
              type: string
      responses:
        200:
          description: Created
          content:
            text/plain:
              schema:
                type: string
  /cookies:
    post:
      summary: Make a post operation
      operationId: post1
      parameters:
        - in: cookie
          name: session_id
          schema:
            type: string
          required: true
        - in: cookie
          name: debug
          schema:
            type: integer
      responses:
        200:
          description: posted
          headers:
            Set-Cookie:
              required: true
              schema:
                type: string
          content:
            text/plain:
              schema:
                type: string
  /path1:
    get:
      parameters:
        - $ref: 'schemas.yaml#/components/parameters/HeaderA'
        - $ref: 'schemas.yaml#/components/parameters/QueryArgumentA'
      description: Get Path1
      responses:
        200:
          description: fake endpoint
          headers:
            Header-B:
              required: true
              schema:
                type: string
                enum:
                  - good value
                  - another good value
            Header-C:
              schema:
                type: string
                enum:
                  - good value
                  - another good value
          content:
            application/json:
              schema:
                $ref: 'schemas.yaml#/components/schemas/SchemaA'
            image/jpeg:
              schema:
                type: string
                format: binary
  /empty:
    post:
      description: Get empty response
      responses:
        204:
          description: No content
    patch:
      summary: Empty response body for all response statuses
      responses:
        default:
          description: No content
  /ref:
    post:
      responses:
        200:
          description: body contains schema with references
          content:
            application/json:
              schema:
                allOf:
                  - $ref: 'schemas.yaml#/components/schemas/SchemaB'
                  - type: object
                    properties:
                      name:
                        type: string
                    required:
                      - name
  /callback:
    post:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                url:
                  type: string
      callbacks:
        somethingHappened:
          '{\$request.body#/url}':
            post:
              requestBody:
                content:
                  application/json:
                    schema:
                      required:
                        - status
                      properties:
                        status:
                          type: string
              responses:
                '200':
                  description: Callback received the request.
                  content:
                    application/json:
                      schema:
                        required:
                          - success
                        properties:
                          success:
                            type: boolean
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                properties:
                  result:
                    type: string
  /deserialize-headers:
    get:
      parameters:
        - in: header
          name: num
          schema:
            type: number
        - in: header
          name: bool
          schema:
            type: boolean
        - in: header
          name: int
          schema:
            type: integer
      description: Get with numeric/bool header
      responses:
        200:
          description: fake endpoint
          content:
            application/json:
              schema: {}
  /deserialize-cookies:
    get:
      description: Get with numeric/bool cookies
      parameters:
        - in: cookie
          name: num
          schema:
            type: number
        - in: cookie
          name: bool
          schema:
            type: boolean
        - in: cookie
          name: int
          schema:
            type: integer
      responses:
        200:
          description: fake endpoint
          content:
            application/json:
              schema: {}
