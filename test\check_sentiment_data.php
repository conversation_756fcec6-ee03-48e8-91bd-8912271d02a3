<?php
// Test script to check sentiment data in the database

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../DatabaseInteraction.php';

// Initialize database connection
$db = new DatabaseInteraction();
$conn = $db->connect();

// User ID to check (usually 2 for the logged-in user)
$user_id = 2;

// Check LOB data
echo "Checking LOB data...\n";
$lob_query = "SELECT lob, COUNT(*) as count FROM analyzed_comments 
              WHERE user_id = ? AND lob IS NOT NULL AND lob != '' 
              GROUP BY lob";
$lob_stmt = $conn->prepare($lob_query);
$lob_stmt->execute([$user_id]);
$lob_results = $lob_stmt->fetchAll(PDO::FETCH_ASSOC);

echo "LOB data in database:\n";
if (empty($lob_results)) {
    echo "No LOB data found for user_id $user_id\n";
} else {
    foreach ($lob_results as $row) {
        echo "- {$row['lob']}: {$row['count']} records\n";
    }
}

// Check Vendor data
echo "\nChecking Vendor data...\n";
$vendor_query = "SELECT vendor, COUNT(*) as count FROM analyzed_comments 
                WHERE user_id = ? AND vendor IS NOT NULL AND vendor != '' 
                GROUP BY vendor";
$vendor_stmt = $conn->prepare($vendor_query);
$vendor_stmt->execute([$user_id]);
$vendor_results = $vendor_stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Vendor data in database:\n";
if (empty($vendor_results)) {
    echo "No Vendor data found for user_id $user_id\n";
} else {
    foreach ($vendor_results as $row) {
        echo "- {$row['vendor']}: {$row['count']} records\n";
    }
}

// Check Location data
echo "\nChecking Location data...\n";
$location_query = "SELECT location, COUNT(*) as count FROM analyzed_comments 
                  WHERE user_id = ? AND location IS NOT NULL AND location != '' 
                  GROUP BY location";
$location_stmt = $conn->prepare($location_query);
$location_stmt->execute([$user_id]);
$location_results = $location_stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Location data in database:\n";
if (empty($location_results)) {
    echo "No Location data found for user_id $user_id\n";
} else {
    foreach ($location_results as $row) {
        echo "- {$row['location']}: {$row['count']} records\n";
    }
}

// Check Partner data
echo "\nChecking Partner data...\n";
$partner_query = "SELECT partner, COUNT(*) as count FROM analyzed_comments 
                 WHERE user_id = ? AND partner IS NOT NULL AND partner != '' 
                 GROUP BY partner";
$partner_stmt = $conn->prepare($partner_query);
$partner_stmt->execute([$user_id]);
$partner_results = $partner_stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Partner data in database:\n";
if (empty($partner_results)) {
    echo "No Partner data found for user_id $user_id\n";
} else {
    foreach ($partner_results as $row) {
        echo "- {$row['partner']}: {$row['count']} records\n";
    }
}

// Check if there's any data with sentiment breakdown
echo "\nChecking sentiment breakdown for LOB...\n";
$lob_sentiment_query = "SELECT lob, sentiment, COUNT(*) as count FROM analyzed_comments 
                       WHERE user_id = ? AND lob IS NOT NULL AND lob != '' 
                       GROUP BY lob, sentiment";
$lob_sentiment_stmt = $conn->prepare($lob_sentiment_query);
$lob_sentiment_stmt->execute([$user_id]);
$lob_sentiment_results = $lob_sentiment_stmt->fetchAll(PDO::FETCH_ASSOC);

echo "LOB sentiment breakdown in database:\n";
if (empty($lob_sentiment_results)) {
    echo "No LOB sentiment breakdown found for user_id $user_id\n";
} else {
    foreach ($lob_sentiment_results as $row) {
        echo "- {$row['lob']} ({$row['sentiment']}): {$row['count']} records\n";
    }
}

// Test the getLobSentiment method directly
echo "\nTesting getLobSentiment method...\n";
$lob_sentiment_data = $db->getLobSentiment('all', $user_id, null, null);
echo "getLobSentiment result: " . json_encode($lob_sentiment_data, JSON_PRETTY_PRINT) . "\n";

// Test the getVendorSentiment method directly
echo "\nTesting getVendorSentiment method...\n";
$vendor_sentiment_data = $db->getVendorSentiment('all', $user_id, null, null);
echo "getVendorSentiment result: " . json_encode($vendor_sentiment_data, JSON_PRETTY_PRINT) . "\n";

// Test the getLocationSentiment method directly
echo "\nTesting getLocationSentiment method...\n";
$location_sentiment_data = $db->getLocationSentiment('all', $user_id, null, null);
echo "getLocationSentiment result: " . json_encode($location_sentiment_data, JSON_PRETTY_PRINT) . "\n";

// Test the getPartnerSentiment method directly
echo "\nTesting getPartnerSentiment method...\n";
$partner_sentiment_data = $db->getPartnerSentiment('all', $user_id, null, null);
echo "getPartnerSentiment result: " . json_encode($partner_sentiment_data, JSON_PRETTY_PRINT) . "\n";

echo "\nTest completed.\n";
