#!/usr/bin/env python3
import sys
import json
import pandas as pd
import statsmodels.formula.api as smf
import mysql.connector
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from io import BytesIO
import base64
import os
from datetime import datetime
import openai
import re

# Database configuration - using same environment variables as PHP
DB_CONFIG = {
    "host": os.getenv('DB_HOST_ALT', 'localhost'),
    "user": os.getenv('DB_USER_ALT', 'mk_hr_india'),
    "password": os.getenv('DB_PASS_ALT', 'mkindiaggn'),
    "database": os.getenv('DB_NAME_ALT', 'feedback_final_10')
}

def validate_date_format(date_str):
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def build_filtered_query(params, metric='csat', user_id=None):
    base_query = f"""
        SELECT {metric}, sub_driver, main_driver, feedback_submit_date, data_id 
        FROM analyzed_comments 
        WHERE {metric} IS NOT NULL
    """
    
    if user_id:
        base_query += f" AND user_id = {user_id}"
    
    filters = []
    filter_values = []
    
    filter_mappings = {
        'start_date': 'DATE(feedback_submit_date) >= %s',
        'end_date': 'DATE(feedback_submit_date) <= %s',
        'product_type': 'partner = %s',
        'channel_type': 'lob = %s',
        'team': 'dummy_1 = %s',
        'resolution_status': 'dummy_5 = %s',
        'domain_category': 'domain_category = %s',
        'data_id': 'data_id = %s',
        'sentiment': 'sentiment = %s'
    }
    
    for param, condition in filter_mappings.items():
        if params.get(param) and params[param] != 'all':
            filters.append(condition)
            filter_values.append(params[param])
    
    if filters:
        base_query += " AND " + " AND ".join(filters)
    
    return base_query, filter_values

def calculate_delta(df, metric, groupby_cols=None):
    if groupby_cols:
        sort_cols = groupby_cols + ['feedback_submit_date']
        df = df.sort_values(sort_cols)
        df[f'd_{metric}'] = df.groupby(groupby_cols)[metric].diff().fillna(0)
    else:
        df = df.sort_values('feedback_submit_date')
        df[f'd_{metric}'] = df[metric].diff().fillna(0)
    return df

def run_regression_analysis(df, target='d_csat'):
    df = df.dropna(subset=[target, 'sub_driver'])
    if df.empty or df['sub_driver'].nunique() < 2:
        return None
    
    try:
        model = smf.ols(f'{target} ~ C(sub_driver)', data=df).fit()
        params = model.params.drop('Intercept')
        pvalues = model.pvalues.drop('Intercept')
        conf = model.conf_int().loc[params.index]
        
        significant_mask = pvalues < 0.05
        sig_params = params[significant_mask]
        sig_conf = conf[significant_mask]
        
        sig_params.index = sig_params.index.str.replace(r'^C\(sub_driver\)\[T\.', '', regex=True).str.rstrip(']')
        sig_conf.index = sig_conf.index.str.replace(r'^C\(sub_driver\)\[T\.', '', regex=True).str.rstrip(']')
        
        if sig_params.empty:
            sig_params = params
            sig_conf = conf
            sig_params.index = sig_params.index.str.replace(r'^C\(sub_driver\)\[T\.', '', regex=True).str.rstrip(']')
            sig_conf.index = sig_conf.index.str.replace(r'^C\(sub_driver\)\[T\.', '', regex=True).str.rstrip(']')
            sig_params = sig_params.sort_values()
            sig_conf = sig_conf.loc[sig_params.index]
        
        top_pos = sig_params[sig_params > 0].sort_values(ascending=False).head(5)
        top_neg = sig_params[sig_params < 0].sort_values().head(5)
        
        top_params = pd.concat([top_pos, top_neg])
        top_params = top_params[::-1]
        top_conf = sig_conf.loc[top_params.index]
        
        # Round coefficients to 2 decimal places for JSON output
        pos_json = top_pos.reset_index().rename(columns={'index': 'driver', 0: 'coef'})
        pos_json['coef'] = pos_json['coef'].round(2)
        pos_json = pos_json.to_dict(orient='records')
        
        neg_json = top_neg.reset_index().rename(columns={'index': 'driver', 0: 'coef'})
        neg_json['coef'] = neg_json['coef'].round(2)
        neg_json = neg_json.to_dict(orient='records')
        
        return {
            'positive': pos_json,
            'negative': neg_json,
            'plot_data': {
                'drivers': top_params.index.tolist(),
                'coefficients': top_params.values.tolist(),
                'colors': ['lightgreen' if x > 0 else 'lightpink' for x in top_params.values]
            }
        }
    except Exception as e:
        return None


def generate_ai_summary(positive, negative, metric='CSAT'):
    client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def format_driver_list(drivers):
        if not drivers:
            return '- None.'
        return '\n'.join([f"- **{d['driver']}** ({d['coef']:+.2f})" for d in drivers])

    pos_md = format_driver_list(positive)
    neg_md = format_driver_list(negative)

    prompt = f"""
You are an analytics assistant. Generate a concise summary in this exact HTML format for {metric} analysis. Always adapt to the number of drivers and coefficients provided. Do not hardcode any driver names or numbers. If a section is empty, state 'None.'

Generate the response in clean HTML format using these tags:
- <h4> for section headers
- <ul> and <li> for lists
- <ol> and <li> for Action Items (numbered)
- <strong> for emphasis
- <p> for paragraphs

Here are the results:

**{metric} Top Positive Movers:**
{pos_md}

**{metric} Top Negative Drags:**
{neg_md}

Format your response exactly like this structure:

<h4><strong>Interpretation:</strong></h4>
<p>Write 2-4 sentences explaining what the results mean for the business in terms of {metric} impact.</p>

<h4><strong>Action Items:</strong></h4>
<ol>
<li>Write a recommendation only if it relates to a negative driver.</li>
<li>Do not include more items than there are negative drivers.</li>
</ol>

Important: Make sure "Interpretation:" and "Action Items:" headers are bold using <strong> tags.
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=600,
            temperature=0.25,
        )
        raw_output = response.choices[0].message.content.strip()

        # Remove wrapping ```html ... ``` or ``` if present
        cleaned_output = re.sub(r"^```(?:html)?\n(.*?)\n```$", r"\1", raw_output, flags=re.DOTALL)

        return cleaned_output
    except Exception as e:
        return f"<p>AI summary generation failed: {e}</p>"

def generate_plot(plot_data, metric='CSAT'):
    # Create figure with larger size for better readability
    plt.figure(figsize=(18, 12))
    
    # Create horizontal bar chart
    bars = plt.barh(plot_data['drivers'], plot_data['coefficients'], color=plot_data['colors'])
    
    # Set title and labels with larger font sizes
    plt.title(f'Top 5 Positive and Top 5 Negative L2 Sub-Drivers - {metric} Analysis', 
              fontsize=20, fontweight='bold', pad=20)
    plt.xlabel('Coefficient', fontsize=20, fontweight='bold')
    plt.ylabel('Sub-Drivers', fontsize=20, fontweight='bold')
    
    # Adjust y-axis labels (driver names) font size
    plt.yticks(fontsize=24, fontweight='bold')
    plt.xticks(fontsize=24, fontweight='bold')
    
    # Add value labels on bars with improved positioning and font size
    for bar in bars:
        width = bar.get_width()
        # Position text slightly inside the bar for better visibility
        if width > 0:
            xpos = width - (abs(width) * 0.1)  # 10% inside from the end
            align = 'right'
        else:
            xpos = width + (abs(width) * 0.1)  # 10% inside from the end
            align = 'left'
        
        plt.text(xpos, bar.get_y() + bar.get_height()/2, f'{width:.2f}', 
                va='center', ha=align, color='black', fontweight='bold', fontsize=20)
    
    # Add vertical line at x=0
    plt.axvline(0, color='black', linewidth=1)
    
    # Adjust layout to prevent label cutoff
    plt.tight_layout()
    
    # Save to buffer
    buffer = BytesIO()
    plt.savefig(buffer, format='png', dpi=160, bbox_inches='tight', facecolor='white')
    buffer.seek(0)
    plt.close()
    
    return base64.b64encode(buffer.getvalue()).decode('utf-8')

def main():
    if len(sys.argv) < 2:
        print(json.dumps({"error": "No action specified"}))
        return
    
    action = sys.argv[1]
    
    try:
        if action == 'analyze':
            # Read parameters from stdin
            params = json.loads(sys.stdin.read())
            metric = params.get('metric', 'csat')
            user_id = params.get('user_id')
            
            # Validate dates
            if params.get('start_date') and not validate_date_format(params['start_date']):
                print(json.dumps({"error": "Invalid start_date format. Use YYYY-MM-DD"}))
                return
            
            if params.get('end_date') and not validate_date_format(params['end_date']):
                print(json.dumps({"error": "Invalid end_date format. Use YYYY-MM-DD"}))
                return
            
            # Connect to database
            conn = mysql.connector.connect(**DB_CONFIG)
            query, filter_values = build_filtered_query(params, metric, user_id)
            df = pd.read_sql(query, conn, params=filter_values)
            conn.close()
            
            if df.empty:
                print(json.dumps({"message": "No data found for the selected filters."}))
                return
            
            # Calculate delta
            df = calculate_delta(df, metric, groupby_cols=['data_id'])
            
            # Run regression
            result = run_regression_analysis(df, f'd_{metric}')
            if result is None:
                print(json.dumps({"message": "No significant drivers found."}))
                return
            
            # Generate AI summary
            summary = generate_ai_summary(result['positive'], result['negative'], metric.upper())
            
            # Generate plot
            plot_base64 = generate_plot(result['plot_data'], metric.upper())
            
            output = {
                "positive": result['positive'],
                "negative": result['negative'],
                "summary": summary,
                "plot": plot_base64
            }
            
            print(json.dumps(output))
            
        else:
            print(json.dumps({"error": "Invalid action"}))
            
    except Exception as e:
        print(json.dumps({"error": str(e)}))

if __name__ == "__main__":
    main()