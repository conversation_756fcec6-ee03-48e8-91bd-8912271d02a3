<?php
/**
 * Debug version of record monitor
 * This script will run with detailed debugging information
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = '682b54974bab6';

// Log file
$log_file = 'debug_monitor.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    
    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting debug record monitor for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database: $dbname on host: $host");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    if ($missing_count <= 0) {
        log_message("No missing records for data_id: $data_id");
        exit(0);
    }
    
    // Get all feedback data records
    log_message("Retrieving all feedback data records...");
    $allFeedbackQuery = "SELECT id, feedback_data FROM feedback_data WHERE data_id = :data_id";
    $allFeedbackStmt = $conn->prepare($allFeedbackQuery);
    $allFeedbackStmt->bindParam(':data_id', $data_id);
    $allFeedbackStmt->execute();
    $allFeedback = $allFeedbackStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Retrieved " . count($allFeedback) . " feedback records");
    
    // Get all analyzed comments
    log_message("Retrieving all analyzed comments...");
    $allAnalyzedQuery = "SELECT comment FROM analyzed_comments WHERE data_id = :data_id";
    $allAnalyzedStmt = $conn->prepare($allAnalyzedQuery);
    $allAnalyzedStmt->bindParam(':data_id', $data_id);
    $allAnalyzedStmt->execute();
    $analyzedComments = $allAnalyzedStmt->fetchAll(PDO::FETCH_COLUMN);
    
    log_message("Retrieved " . count($analyzedComments) . " analyzed comments");
    
    // Find missing records
    log_message("Identifying missing records...");
    $missingRecords = [];
    
    foreach ($allFeedback as $record) {
        if (!in_array($record['feedback_data'], $analyzedComments)) {
            $missingRecords[] = $record['id'];
        }
    }
    
    log_message("Found " . count($missingRecords) . " missing records");
    
    if (count($missingRecords) > 0) {
        log_message("Missing record IDs: " . implode(', ', $missingRecords));
        
        // Get details for the first 5 missing records
        $sampleSize = min(5, count($missingRecords));
        $sampleIds = array_slice($missingRecords, 0, $sampleSize);
        
        log_message("Examining " . count($sampleIds) . " sample records...");
        
        foreach ($sampleIds as $recordId) {
            log_message("\nExamining record ID: $recordId");
            
            // Get the record details
            $recordQuery = "SELECT * FROM feedback_data WHERE id = :id";
            $recordStmt = $conn->prepare($recordQuery);
            $recordStmt->bindParam(':id', $recordId);
            $recordStmt->execute();
            $record = $recordStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$record) {
                log_message("Record ID: $recordId not found");
                continue;
            }
            
            // Log record details
            log_message("Record details:");
            log_message("- data_id: " . $record['data_id']);
            log_message("- user_id: " . $record['user_id']);
            log_message("- feedback_data: " . $record['feedback_data']);
            log_message("- feedback_data length: " . strlen($record['feedback_data']));
            
            // Try to insert the record into analyzed_comments
            try {
                log_message("Attempting to insert record ID: $recordId into analyzed_comments...");
                
                $insertQuery = "INSERT INTO analyzed_comments (
                    comment, data_id, user_id, csat, nps, pid,
                    main_driver, sub_driver, sentiment
                ) VALUES (
                    :comment, :data_id, :user_id, :csat, :nps, :pid,
                    'Auto-Generated', 'Auto-Generated', 'Neutral'
                )";
                
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bindParam(':comment', $record['feedback_data']);
                $insertStmt->bindParam(':data_id', $record['data_id']);
                $insertStmt->bindParam(':user_id', $record['user_id']);
                $insertStmt->bindParam(':csat', $record['csat']);
                $insertStmt->bindParam(':nps', $record['nps']);
                $insertStmt->bindParam(':pid', $record['pid']);
                
                $result = $insertStmt->execute();
                
                if ($result) {
                    log_message("Successfully inserted record ID: $recordId into analyzed_comments");
                    
                    // Verify the record was inserted
                    $verifyQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id AND comment = :comment";
                    $verifyStmt = $conn->prepare($verifyQuery);
                    $verifyStmt->bindParam(':data_id', $record['data_id']);
                    $verifyStmt->bindParam(':comment', $record['feedback_data']);
                    $verifyStmt->execute();
                    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($verifyResult['count'] > 0) {
                        log_message("Verified record ID: $recordId was successfully inserted into analyzed_comments");
                    } else {
                        log_message("WARNING: Record ID: $recordId was not found in analyzed_comments after insertion");
                    }
                } else {
                    log_message("Failed to insert record ID: $recordId - Error: " . json_encode($insertStmt->errorInfo()));
                }
            } catch (PDOException $e) {
                log_message("ERROR: Failed to insert record ID: $recordId - " . $e->getMessage());
            }
        }
    }
    
    log_message("\nDebug record monitor completed");
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
