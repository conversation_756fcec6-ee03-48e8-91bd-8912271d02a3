<?php
// Minimal test for tabular summary
session_start();
require_once 'config.php';
require_once 'DatabaseInteraction.php';

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    // Check if user is authenticated
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'error' => 'User not authenticated.']);
        exit;
    }

    $user_id = $_SESSION['user_id'];

    // Get database connection
    $db = new DatabaseInteraction();
    $pdo = $db->connect();

    // Simple tabular summary query without complex filters
    $sql = "SELECT 
      main_driver,
      sub_driver,
      sentiment,
      domain_category,
      lob,
      vendor,
      location,
      partner,
      dummy_1 as team,
      dummy_5 as resolution_status,
      painpointscustomerfrustrations as pain_points,
      suggestionsforimprovement as suggestions,
      COUNT(*) as total_comments,
      ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as avg_csat,
      ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as avg_nps
    FROM analyzed_comments
    WHERE user_id = :user_id
    GROUP BY main_driver, sub_driver, sentiment, domain_category, lob, vendor, location, partner, dummy_1, dummy_5, painpointscustomerfrustrations, suggestionsforimprovement
    ORDER BY total_comments DESC
    LIMIT 20";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([':user_id' => $user_id]);
    $tabularSummary = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Overall statistics
    $overallSql = "SELECT
        COUNT(*) as total_records,
        ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as overall_avg_csat,
        ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as overall_avg_nps,
        COUNT(DISTINCT main_driver) as unique_drivers,
        COUNT(DISTINCT vendor) as unique_vendors,
        COUNT(DISTINCT location) as unique_locations,
        COUNT(CASE WHEN sentiment = 'Positive' THEN 1 END) as total_positive,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as total_negative,
        COUNT(CASE WHEN sentiment = 'Neutral' THEN 1 END) as total_neutral,
        ROUND((COUNT(CASE WHEN sentiment = 'Positive' THEN 1 END) * 100.0 / COUNT(*)), 2) as positive_percentage,
        ROUND((COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) * 100.0 / COUNT(*)), 2) as negative_percentage
    FROM analyzed_comments
    WHERE user_id = :user_id";

    $overallStmt = $pdo->prepare($overallSql);
    $overallStmt->execute([':user_id' => $user_id]);
    $overallStats = $overallStmt->fetch(PDO::FETCH_ASSOC);

    // Top issues by frequency
    $topIssuesSql = "SELECT
        main_driver,
        sub_driver,
        COUNT(*) as frequency,
        ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as avg_csat,
        ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as avg_nps,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as negative_count
    FROM analyzed_comments
    WHERE user_id = :user_id
    GROUP BY main_driver, sub_driver
    ORDER BY frequency DESC
    LIMIT 10";

    $topIssuesStmt = $pdo->prepare($topIssuesSql);
    $topIssuesStmt->execute([':user_id' => $user_id]);
    $topIssues = $topIssuesStmt->fetchAll(PDO::FETCH_ASSOC);

    // Performance by vendor
    $vendorPerformanceSql = "SELECT
        vendor,
        COUNT(*) as total_comments,
        ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as avg_csat,
        ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as avg_nps,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as negative_feedback,
        ROUND((COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) * 100.0 / COUNT(*)), 2) as negative_percentage
    FROM analyzed_comments
    WHERE user_id = :user_id
    GROUP BY vendor
    ORDER BY avg_csat ASC";

    $vendorStmt = $pdo->prepare($vendorPerformanceSql);
    $vendorStmt->execute([':user_id' => $user_id]);
    $vendorPerformance = $vendorStmt->fetchAll(PDO::FETCH_ASSOC);

    $enhancedSummary = [
        'detailed_summary' => $tabularSummary,
        'overall_statistics' => $overallStats,
        'top_issues' => $topIssues,
        'vendor_performance' => $vendorPerformance
    ];

    echo json_encode(['success' => true, 'data' => $enhancedSummary]);

} catch (Throwable $e) {
    // Log the error for debugging
    file_put_contents('php_errors.log', "[MinimalTabularSummary][ERROR] " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    // Always return valid JSON
    echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
    exit;
} 
?>
