{"name": "league/openapi-psr7-validator", "description": "Validate PSR-7 messages against OpenAPI (3.0.2) specifications \nexpressed in YAML or JSON", "keywords": ["OpenAPI", "psr7", "http", "validation"], "homepage": "https://github.com/thephpleague/openapi-psr7-validator", "license": "MIT", "type": "library", "autoload": {"psr-4": {"League\\OpenAPIValidation\\": "src/"}}, "autoload-dev": {"psr-4": {"League\\OpenAPIValidation\\Tests\\": "tests/"}}, "require": {"php": ">=7.2", "ext-json": "*", "devizzent/cebe-php-openapi": "^1.0", "league/uri": "^6.3 || ^7.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0", "respect/validation": "^1.1.3 || ^2.0", "riverline/multipart-parser": "^2.0.3", "symfony/polyfill-php80": "^1.27", "webmozart/assert": "^1.4"}, "require-dev": {"doctrine/coding-standard": "^8.0", "guzzlehttp/psr7": "^2.0", "hansott/psr7-cookies": "^3.0.2 || ^4.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-webmozart-assert": "^1", "phpunit/phpunit": "^7 || ^8 || ^9", "symfony/cache": "^5.1"}, "config": {"sort-packages": true, "allow-plugins": true}, "prefer-stable": true}