openapi: 3.0.2
info:
  title: Multipart API
  version: 0.0.1
  contact:
    name: <PERSON>
    url: https://github.com/thephpleague/openapi-psr7-validator
    email: <EMAIL>
paths:
  /urlencoded/scalar-types:
    post:
      summary: Post form-url-encoded body
      operationId: post-form-url-encoded-body
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                address:
                  type: string
                phones:
                  type: array
              required:
                - id
                - address
                - phones

      responses:
        204:
          description: good post
  /urlencoded/scalar-deserialization:
    post:
      summary: Post form-url-encoded body
      operationId: post-form-url-encoded-body-with-deserialization
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                id:
                  type: number
                secure:
                  type: boolean
                code:
                  type: integer
              required:
                - id
                - secure
                - code

      responses:
        204:
          description: good post
