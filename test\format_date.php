<?php
/**
 * Format a MySQL datetime string to a user-friendly format
 * 
 * @param string $mysql_date The MySQL datetime string (YYYY-MM-DD HH:MM:SS)
 * @return string The formatted date string (MM/DD/YYYY HH:MM:SS AM/PM)
 */
function format_display_date($mysql_date) {
    if (empty($mysql_date) || $mysql_date == '0000-00-00 00:00:00') {
        return '';
    }
    
    $date_obj = DateTime::createFromFormat('Y-m-d H:i:s', $mysql_date);
    if (!$date_obj) {
        return $mysql_date; // Return original if parsing fails
    }
    
    return $date_obj->format('m/d/Y h:i:s A');
}
