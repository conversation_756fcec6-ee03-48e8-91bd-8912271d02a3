<?php
// Test AI Summary data retrieval and ACPT categorization
require_once 'config.php';
require_once 'DatabaseInteraction.php';

try {
    $db = new DatabaseInteraction();
    $conn = $db->connect();
    echo "Database connection successful!\n";
    
    // Test the same query used by data_tabular_summary.php
    $user_id = 1; // Assuming user_id 1 for testing
    
    // Test overall statistics query
    $overallSql = "SELECT
        COUNT(*) as total_records,
        ROUND(AVG(csat), 2) as overall_avg_csat,
        ROUND(AVG(nps), 2) as overall_avg_nps,
        COUNT(DISTINCT main_driver) as unique_drivers,
        COUNT(DISTINCT vendor) as unique_vendors,
        COUNT(DISTINCT location) as unique_locations,
        COUNT(CASE WHEN sentiment = 'Positive' THEN 1 END) as total_positive,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as total_negative,
        COUNT(CASE WHEN sentiment = 'Neutral' THEN 1 END) as total_neutral
    FROM analyzed_comments
    WHERE user_id = ?";
    
    $stmt = $conn->prepare($overallSql);
    $stmt->execute([$user_id]);
    $overallStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "\nOverall Statistics:\n";
    echo "- Total Records: " . $overallStats['total_records'] . "\n";
    echo "- Average CSAT: " . $overallStats['overall_avg_csat'] . "\n";
    echo "- Average NPS: " . $overallStats['overall_avg_nps'] . "\n";
    echo "- Unique Drivers: " . $overallStats['unique_drivers'] . "\n";
    echo "- Unique Vendors: " . $overallStats['unique_vendors'] . "\n";
    echo "- Unique Locations: " . $overallStats['unique_locations'] . "\n";
    echo "- Sentiment Distribution: " . $overallStats['total_positive'] . " Positive, " . $overallStats['total_negative'] . " Negative, " . $overallStats['total_neutral'] . " Neutral\n";
    
    // Test detailed summary query (same as data_tabular_summary.php)
    $sql = "SELECT
      main_driver,
      sub_driver,
      sentiment,
      domain_category,
      lob,
      vendor,
      location,
      partner,
      dummy_1 as team,
      dummy_5 as resolution_status,
      painpointscustomerfrustrations as pain_points,
      suggestionsforimprovement as suggestions,
      COUNT(*) as total_comments,
      ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as avg_csat,
      ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as avg_nps
    FROM analyzed_comments
    WHERE user_id = ?
    GROUP BY main_driver, sub_driver, sentiment, domain_category, lob, vendor, location, partner, dummy_1, dummy_5, painpointscustomerfrustrations, suggestionsforimprovement
    ORDER BY total_comments DESC
    LIMIT 10";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$user_id]);
    $tabularSummary = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nTop 10 Detailed Summary Records:\n";
    $totalFromSample = 0;
    foreach ($tabularSummary as $row) {
        echo "- {$row['main_driver']}/{$row['sub_driver']} ({$row['sentiment']}): {$row['total_comments']} comments, CSAT {$row['avg_csat']}, Vendor: {$row['vendor']}, LOB: {$row['lob']}, Team: {$row['team']}\n";
        $totalFromSample += $row['total_comments'];
    }
    
    echo "\nTotal comments from top 10 records: $totalFromSample\n";
    echo "This should help identify if there's a discrepancy in record counting.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
