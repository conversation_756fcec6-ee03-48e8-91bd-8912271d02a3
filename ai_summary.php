<?php
// Disable direct error display
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Enable error logging to file
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// CORS support (adjust Access-Control-Allow-Origin in production)
header('Access-Control-Allow-Origin: *'); // Change to your domain for security
header('Content-Type: application/json');

// Load dependencies
require_once 'vendor/autoload.php';
require_once 'DatabaseInteraction.php';

try {
    session_start();

    // 🛑 Authentication check
    if (!isset($_SESSION['username'])) {
        echo json_encode(['status' => 'error', 'message' => 'Not logged in']);
        exit();
    }

    // ✅ Environment Variables
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();

    // ✅ Database connection (if needed later)
    $db = new DatabaseInteraction();
    $conn = $db->connect();

    // ✅ Request method check
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method. Only POST is allowed.');
    }

    // ✅ Input handling
    $input = json_decode(file_get_contents('php://input'), true);
    if (!isset($input['prompt']) || trim($input['prompt']) === '') {
        throw new Exception('No prompt provided.');
    }

    $prompt = trim($input['prompt']);

    // ✅ Prompt size safeguard (increased limit for detailed prompts)
    if (strlen($prompt) > 25000) {
        throw new Exception('Prompt too large. Please reduce feedback volume.');
    }

    // Log prompt statistics for debugging
    error_log("Prompt length: " . strlen($prompt));
    error_log("Prompt preview: " . substr($prompt, 0, 300));

    // ✅ Load OpenAI API key
    $apiKey = $_ENV['OPENAI_API_KEY'] ?? null;
    if (!$apiKey) {
        throw new Exception('OpenAI API key not set in environment.');
    }

    // ✅ Call OpenAI with improved parameters
    $openai = OpenAI::client($apiKey);

    try {
        $result = $openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'system', 
                    'content' => 'You are an expert customer feedback analyst. Always follow the provided structure exactly. Output complete HTML formatted responses as requested. Never summarize or skip sections.'
                ],
                [
                    'role' => 'user', 
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.2, // Reduced for more consistent output
            'max_tokens' => 8000, // Increased for complete enhanced summaries
            'top_p' => 0.9,
            'frequency_penalty' => 0.1,
            'presence_penalty' => 0.1
        ]);
    } catch (Exception $e) {
        throw new Exception('OpenAI API error: ' . $e->getMessage());
    }

    $summary = $result['choices'][0]['message']['content'] ?? '';
    
    // Log response for debugging
    error_log("AI Response length: " . strlen($summary));
    error_log("AI Response preview: " . substr($summary, 0, 200));
    
    // Validate that we got a proper response
    if (empty($summary)) {
        throw new Exception('Empty response from AI service.');
    }
    
    // Check if the response contains both required parts
    if (!strpos($summary, 'Part 1: Executive Summary') || !strpos($summary, 'Part 2: Diagnostic Report')) {
        error_log("Warning: AI response may not contain both required parts");
    }

    echo json_encode([
        'status' => 'success', 
        'summary_html' => $summary,
        'response_length' => strlen($summary)
    ]);

} catch (Exception $e) {
    error_log('AI Summary Error: ' . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>