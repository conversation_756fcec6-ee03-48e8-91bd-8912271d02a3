<?php
/**
 * test/test_python_accessibility.php
 * Comprehensive test script to check Python accessibility and environment configuration
 * Run this from your browser: http://localhost/feedback-10/test/test_python_accessibility.php
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Load the same configuration as your main app
require_once __DIR__ . '/../config.php';

echo "<h1>Python Accessibility Test</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test 1: Check if Python command is available
echo "<div class='test-section'>";
echo "<h2>Test 1: Python Command Availability</h2>";

$python_commands = ['python', 'python3', 'py'];
$python_found = false;
$working_command = '';

foreach ($python_commands as $cmd) {
    $output = [];
    $return_var = 0;
    exec("$cmd --version 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<div class='success'>✅ Found: $cmd</div>";
        echo "<div class='info'>Version: " . implode("\n", $output) . "</div>";
        $python_found = true;
        $working_command = $cmd;
        break;
    } else {
        echo "<div class='error'>❌ Not found: $cmd</div>";
    }
}

if (!$python_found) {
    echo "<div class='error'>❌ No Python command found in PATH</div>";
}
echo "</div>";

// Test 2: Check Python path
echo "<div class='test-section'>";
echo "<h2>Test 2: Python Path Detection</h2>";

if ($python_found) {
    $output = [];
    exec("where $working_command 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<div class='success'>✅ Python path found:</div>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    } else {
        echo "<div class='error'>❌ Could not determine Python path</div>";
    }
} else {
    echo "<div class='warning'>⚠️ Skipping path test - Python not found</div>";
}
echo "</div>";

// Test 3: Check if Python script exists and is readable
echo "<div class='test-section'>";
echo "<h2>Test 3: Python Script File Check</h2>";

$script_path = __DIR__ . '/../python/ols_analysis.py';
if (file_exists($script_path)) {
    echo "<div class='success'>✅ Script exists: $script_path</div>";
    
    if (is_readable($script_path)) {
        echo "<div class='success'>✅ Script is readable</div>";
    } else {
        echo "<div class='error'>❌ Script is not readable</div>";
    }
    
    $file_size = filesize($script_path);
    echo "<div class='info'>File size: " . number_format($file_size) . " bytes</div>";
} else {
    echo "<div class='error'>❌ Script not found: $script_path</div>";
}
echo "</div>";

// Test 4: Test Python script execution
echo "<div class='test-section'>";
echo "<h2>Test 4: Python Script Execution Test</h2>";

if ($python_found && file_exists($script_path)) {
    $descriptors = [
        0 => ['pipe', 'r'],  // stdin
        1 => ['pipe', 'w'],  // stdout
        2 => ['pipe', 'w']   // stderr
    ];
    
    $process = proc_open("$working_command \"$script_path\" analyze", $descriptors, $pipes);
    
    if (is_resource($process)) {
        echo "<div class='success'>✅ Python process started successfully</div>";
        
        // Send test input
        $test_input = json_encode([
            'metric' => 'csat',
            'user_id' => 2,
            'data_id' => '686cf72157df2'
        ]);
        
        fwrite($pipes[0], $test_input);
        fclose($pipes[0]);
        
        // Read output
        $output = stream_get_contents($pipes[1]);
        $error = stream_get_contents($pipes[2]);
        
        fclose($pipes[1]);
        fclose($pipes[2]);
        
        $return_code = proc_close($process);
        
        echo "<div class='info'>Return code: $return_code</div>";
        
        if ($output) {
            echo "<div class='success'>✅ Script output:</div>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
        }
        
        if ($error) {
            echo "<div class='error'>❌ Script error:</div>";
            echo "<pre>" . htmlspecialchars($error) . "</pre>";
        }
        
        if ($return_code === 0) {
            echo "<div class='success'>✅ Script executed successfully</div>";
        } else {
            echo "<div class='error'>❌ Script failed with return code: $return_code</div>";
        }
    } else {
        echo "<div class='error'>❌ Failed to start Python process</div>";
    }
} else {
    echo "<div class='warning'>⚠️ Skipping execution test - Python or script not available</div>";
}
echo "</div>";

// Test 5: Check Python dependencies
echo "<div class='test-section'>";
echo "<h2>Test 5: Python Dependencies Check</h2>";

if ($python_found) {
    $required_modules = ['pandas', 'statsmodels', 'mysql.connector', 'matplotlib', 'openai'];
    
    foreach ($required_modules as $module) {
        $output = [];
        $return_var = 0;
        exec("$working_command -c \"import $module; print('OK')\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<div class='success'>✅ $module - Available</div>";
        } else {
            echo "<div class='error'>❌ $module - Not available</div>";
            echo "<div class='info'>Error: " . implode("\n", $output) . "</div>";
        }
    }
} else {
    echo "<div class='warning'>⚠️ Skipping dependency test - Python not found</div>";
}
echo "</div>";

// Test 6: Environment Variables Check
echo "<div class='test-section'>";
echo "<h2>Test 6: Environment Variables Check</h2>";

$env_vars = [
    'DB_HOST_ALT' => 'Database host',
    'DB_NAME_ALT' => 'Database name', 
    'DB_USER_ALT' => 'Database user',
    'DB_PASS_ALT' => 'Database password',
    'OPENAI_API_KEY' => 'OpenAI API key'
];

$all_env_set = true;

foreach ($env_vars as $var => $description) {
    $value = $_ENV[$var] ?? getenv($var);
    if ($value) {
        if (strpos($var, 'PASS') !== false || strpos($var, 'KEY') !== false) {
            echo "<div class='success'>✅ $description: " . str_repeat('*', strlen($value)) . "</div>";
        } else {
            echo "<div class='success'>✅ $description: $value</div>";
        }
    } else {
        echo "<div class='error'>❌ $description: Not set</div>";
        $all_env_set = false;
    }
}

if ($all_env_set) {
    echo "<div class='success'>✅ All environment variables are properly set!</div>";
} else {
    echo "<div class='error'>❌ Some environment variables are missing</div>";
}
echo "</div>";

// Test 7: Database Connection Test
echo "<div class='test-section'>";
echo "<h2>Test 7: Database Connection Test</h2>";

try {
    $host = $_ENV['DB_HOST_ALT'] ?? getenv('DB_HOST_ALT');
    $dbname = $_ENV['DB_NAME_ALT'] ?? getenv('DB_NAME_ALT');
    $username = $_ENV['DB_USER_ALT'] ?? getenv('DB_USER_ALT');
    $password = $_ENV['DB_PASS_ALT'] ?? getenv('DB_PASS_ALT');
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ Database connection successful!</div>";
    echo "<div class='info'>Connected to: $host/$dbname</div>";
    
    // Test a simple query
    $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<div class='info'>Total records in analyzed_comments: " . $result['count'] . "</div>";
    
    // Test specific data for OLS analysis
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = ? AND data_id = ?");
    $stmt->execute([2, '686cf72157df2']);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<div class='info'>Records for user_id=2, data_id=686cf72157df2: " . $result['count'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 8: Python Environment Variables Test
echo "<div class='test-section'>";
echo "<h2>Test 8: Python Environment Variables Test</h2>";

if ($python_found) {
    // Test if Python can access the environment variables
    $python_test = "import os; print('DB_HOST_ALT:', os.getenv('DB_HOST_ALT', 'Not set')); print('DB_NAME_ALT:', os.getenv('DB_NAME_ALT', 'Not set')); print('OPENAI_API_KEY:', 'Set' if os.getenv('OPENAI_API_KEY') else 'Not set')";

    $output = [];
    $return_var = 0;
    exec("$working_command -c \"$python_test\" 2>&1", $output, $return_var);

    if ($return_var === 0) {
        echo "<div class='success'>✅ Python can access environment variables:</div>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    } else {
        echo "<div class='error'>❌ Python cannot access environment variables:</div>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
} else {
    echo "<div class='warning'>⚠️ Skipping Python environment test - Python not found</div>";
}
echo "</div>";

// Test 9: Web Server Information
echo "<div class='test-section'>";
echo "<h2>Test 9: Web Server Information</h2>";

echo "<div class='info'>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</div>";
echo "<div class='info'>PHP Version: " . PHP_VERSION . "</div>";
echo "<div class='info'>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</div>";
echo "<div class='info'>Script Path: " . __FILE__ . "</div>";
echo "<div class='info'>Current Working Directory: " . getcwd() . "</div>";
echo "</div>";

// Summary
echo "<div class='test-section'>";
echo "<h2>Summary</h2>";

$overall_success = $python_found && file_exists($script_path) && $all_env_set;

if ($overall_success) {
    echo "<div class='success'>✅ Python environment is fully configured and ready for OLS analysis!</div>";
    echo "<div class='info'>Working Python command: $working_command</div>";
} else {
    echo "<div class='error'>❌ Python environment needs configuration</div>";
    echo "<div class='info'>Issues found:</div>";
    if (!$python_found) {
        echo "<div class='error'>- Python command not found in PATH</div>";
    }
    if (!file_exists($script_path)) {
        echo "<div class='error'>- Python script not found</div>";
    }
    if (!$all_env_set) {
        echo "<div class='error'>- Environment variables not properly configured</div>";
    }
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Recommendations</h2>";
echo "<ul>";
if (!$python_found) {
    echo "<li>Install Python and add it to your system PATH</li>";
    echo "<li>On Windows, you may need to disable 'App Execution Aliases' for Python</li>";
}
if (!file_exists($script_path)) {
    echo "<li>Check if the ols_analysis.py file exists in the python directory</li>";
}
if (!$all_env_set) {
    echo "<li>Create a .env file with the required environment variables</li>";
    echo "<li>Make sure config.php is loading the .env file correctly</li>";
}
echo "<li>Make sure all required Python packages are installed: pip install -r python/requirements.txt</li>";
echo "<li>Fix session permissions if you see session-related errors</li>";
echo "<li>Test the OLS API directly: python/api/ols_api.php?action=csat&user_id=2&data_id=686cf72157df2</li>";
echo "</ul>";
echo "</div>";
?> 