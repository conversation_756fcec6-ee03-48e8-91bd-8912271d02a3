class OLSAnalysis {
    constructor() {
        this.isLoading = false;
    }

    async runCSATAnalysis(filters = {}, isDarkMode = false) {
        return await this.runAnalysis('csat', filters, isDarkMode);
    }

    async runNPSAnalysis(filters = {}, isDarkMode = false) {
        return await this.runAnalysis('nps', filters, isDarkMode);
    }

    async runAnalysis(metric, filters, isDarkMode) {
        if (this.isLoading) {
            throw new Error('Analysis already in progress');
        }

        this.isLoading = true;
        
        try {
            const params = new URLSearchParams(filters);
            params.append('dark_mode', isDarkMode ? 'true' : 'false'); // Pass dark mode status
            
            const response = await fetch(`python/api/ols_api.php?action=${metric}&${params.toString()}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data;
        } catch (error) {
            console.error(`OLS Analysis Error (${metric}):`, error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    displayResults(containerId, data, metric) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (data.message) {
            container.innerHTML = `
                <div class="text-center p-4 text-gray-600">
                    <i class="fas fa-info-circle mr-2"></i>
                    ${data.message}
                </div>
            `;
            return;
        }

        let html = '';

        // Display plot if available
        if (data.plot) {
            html += `
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">${metric} Analysis Visualization</h4>
                    <div class="bg-white rounded-lg shadow-sm p-4">
                        <img src="data:image/png;base64,${data.plot}" 
                             class="w-full max-h-96 object-contain" 
                             alt="${metric} Analysis Plot">
                    </div>
                </div>
            `;
        }

        // Display results summary
        html += `
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h4 class="text-lg font-semibold mb-4 text-gray-800">${metric} Analysis Results</h4>
        `;

        if (data.positive && data.positive.length > 0) {
            html += `
                <div class="mb-4">
                    <h5 class="font-medium text-green-700 mb-2">
                        <i class="fas fa-arrow-up mr-2"></i>Top Positive Movers:
                    </h5>
                    <ul class="list-disc list-inside space-y-1">
                        ${data.positive.map(item => 
                            `<li><strong>${item.driver}</strong> (+${item.coef})</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
        }

        if (data.negative && data.negative.length > 0) {
            html += `
                <div class="mb-4">
                    <h5 class="font-medium text-red-700 mb-2">
                        <i class="fas fa-arrow-down mr-2"></i>Top Negative Drags:
                    </h5>
                    <ul class="list-disc list-inside space-y-1">
                        ${data.negative.map(item => 
                            `<li><strong>${item.driver}</strong> (${item.coef})</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
        }

        if (data.summary) {
            html += `
                <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <h5 class="font-medium text-gray-800 dark:text-white mb-3">AI Summary:</h5>
                    <div class="prose prose-sm max-w-none text-gray-700 dark:text-gray-300 text-base leading-relaxed">
                        ${data.summary}
                    </div>
                </div>
            `;
        }

        html += '</div>';
        container.innerHTML = html;
    }

    showLoading(containerId, message = 'Analyzing data...') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="text-center p-8">
                    <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        ${message}
                    </div>
                </div>
            `;
        }
    }

    showError(containerId, error) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="text-center p-4">
                    <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        ${error}
                    </div>
                </div>
            `;
        }
    }
}

// Global instance
window.olsAnalysis = new OLSAnalysis();