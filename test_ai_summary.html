<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Summary</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-data { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .result { background: #e8f5e9; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI Summary Test</h1>
        
        <div class="test-data">
            <h3>Test Data:</h3>
            <pre id="testData"></pre>
        </div>
        
        <button id="testBtn" onclick="testAISummary()">Test AI Summary Generation</button>
        
        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        // Sample test data that matches the expected structure
        const sampleData = [
            {
                main_driver: "Service Quality",
                sub_driver: "Response Time",
                sentiment: "Negative",
                domain_category: "Customer Support",
                lob: "Retail",
                vendor: "Vendor A",
                location: "New York",
                partner: "Partner 1",
                team: "Team Alpha",
                resolution_status: "Pending",
                painpointscustomerfrustrations: "Long wait times",
                detailedexplanationofthecomment: "Customer waited 30 minutes for support",
                suggestionsforimprovement: "Reduce response time",
                total_comments: 25,
                avg_csat: 2.1,
                avg_nps: 1.5
            },
            {
                main_driver: "Product Quality",
                sub_driver: "Functionality",
                sentiment: "Positive",
                domain_category: "Product",
                lob: "Technology",
                vendor: "Vendor B",
                location: "California",
                partner: "Partner 2",
                team: "Team Beta",
                resolution_status: "Resolved",
                painpointscustomerfrustrations: "Feature request",
                detailedexplanationofthecomment: "Customer loves the new features",
                suggestionsforimprovement: "Add more features",
                total_comments: 15,
                avg_csat: 4.2,
                avg_nps: 4.0
            },
            {
                main_driver: "Billing",
                sub_driver: "Pricing",
                sentiment: "Negative",
                domain_category: "Finance",
                lob: "Billing",
                vendor: "Vendor A",
                location: "Texas",
                partner: "Partner 1",
                team: "Team Gamma",
                resolution_status: "Pending",
                painpointscustomerfrustrations: "High costs",
                detailedexplanationofthecomment: "Customer complains about pricing",
                suggestionsforimprovement: "Review pricing structure",
                total_comments: 30,
                avg_csat: 1.8,
                avg_nps: 1.2
            }
        ];

        // Display test data
        document.getElementById('testData').textContent = JSON.stringify(sampleData, null, 2);

        // Copy the functions from dashboard.php for testing
        function generateAdvancedAnalytics(tableData, totalComments, avgCSAT, avgNPS) {
            const analytics = {
                vendors: {},
                painPoints: {},
                domains: {},
                resolutions: {}
            };

            tableData.forEach((row, index) => {
                
                const vendor = row.vendor || 'Unknown';
                const pain = row.painpointscustomerfrustrations || 'Unknown';
                const domain = row.domain_category || 'Unknown';
                const resolution = row.resolution_status || 'Unknown';

                // Vendor analytics
                if (!analytics.vendors[vendor]) analytics.vendors[vendor] = { comments: 0, csatSum: 0, delta: 0 };
                analytics.vendors[vendor].comments += row.total_comments || 0;
                analytics.vendors[vendor].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

                // Pain points analytics
                if (!analytics.painPoints[pain]) analytics.painPoints[pain] = { freq: 0, csatSum: 0, severity: 0 };
                analytics.painPoints[pain].freq += row.total_comments || 0;
                analytics.painPoints[pain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

                // Domain analytics
                if (!analytics.domains[domain]) analytics.domains[domain] = { comments: 0, csatSum: 0, resolved: 0, total: 0 };
                analytics.domains[domain].comments += row.total_comments || 0;
                analytics.domains[domain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);
                analytics.domains[domain].total += row.total_comments || 0;
                if ((row.resolution_status || '').toLowerCase() === 'resolved') {
                    analytics.domains[domain].resolved += row.total_comments || 0;
                }

                // Resolution analytics
                if (!analytics.resolutions[resolution]) analytics.resolutions[resolution] = { comments: 0 };
                analytics.resolutions[resolution].comments += row.total_comments || 0;
            });

            // Calculate vendor deltas
            for (let v in analytics.vendors) {
                const avg = analytics.vendors[v].csatSum / analytics.vendors[v].comments;
                analytics.vendors[v].delta = parseFloat((avg - avgCSAT).toFixed(2));
            }

            // Calculate pain point severity
            for (let p in analytics.painPoints) {
                const data = analytics.painPoints[p];
                const avg = data.csatSum / data.freq;
                data.severity = parseFloat((data.freq * (5 - avg)).toFixed(2));
            }

            return analytics;
        }

        function buildRecordInfo(tableData, totalComments, avgCSAT, avgNPS) {
            const vendors = new Set(tableData.map(r => r.vendor || 'Unknown')).size;
            const locations = new Set(tableData.map(r => r.location || 'Unknown')).size;
            return {
                totalComments,
                avgCSAT: avgCSAT.toFixed(1),
                avgNPS: avgNPS.toFixed(1),
                vendors,
                locations
            };
        }

        function buildEnhancedPrompt(analytics, info, avgCSAT, avgNPS) {
            // Build vendor performance data
            const vendorData = Object.entries(analytics.vendors)
                .map(([vendor, data]) => ({
                    vendor,
                    comments: data.comments,
                    avgCsat: (data.csatSum / data.comments).toFixed(1),
                    delta: data.delta
                }))
                .sort((a, b) => b.delta - a.delta);

            // Build pain points data
            const painPointsData = Object.entries(analytics.painPoints)
                .map(([pain, data]) => ({
                    pain,
                    frequency: data.freq,
                    avgCsat: (data.csatSum / data.freq).toFixed(1),
                    severity: data.severity
                }))
                .sort((a, b) => b.severity - a.severity);

            // Build domains data
            const domainsData = Object.entries(analytics.domains)
                .map(([domain, data]) => ({
                    domain,
                    comments: data.comments,
                    avgCsat: (data.csatSum / data.comments).toFixed(1),
                    resolutionRate: ((data.resolved / data.total) * 100).toFixed(1)
                }))
                .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat));

            return `You are an expert AI feedback analyst. Generate a comprehensive analysis in TWO distinct parts with exact HTML formatting. Follow the structure EXACTLY as specified below.

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections
4. Use the provided data structure
5. Maintain professional tone throughout

DATASET OVERVIEW:
- Total Comments: ${info.totalComments}
- Average CSAT: ${info.avgCSAT} (Scale: 1-5)
- Average NPS: ${info.avgNPS} (Scale: 1-5)
- Vendors Analyzed: ${info.vendors}
- Locations Covered: ${info.locations}

VENDOR PERFORMANCE DATA:
${vendorData.map(v => `- ${v.vendor}: ${v.comments} comments, CSAT ${v.avgCsat}, Delta ${v.delta}`).join('\n')}

PAIN POINTS DATA:
${painPointsData.map(p => `- ${p.pain}: ${p.frequency} mentions, CSAT ${p.avgCsat}, Severity ${p.severity}`).join('\n')}

DOMAINS DATA:
${domainsData.map(d => `- ${d.domain}: ${d.comments} comments, CSAT ${d.avgCsat}, Resolution Rate ${d.resolutionRate}%`).join('\n')}

---

<h3><strong>Part 1: Executive Summary</strong></h3>

<h4><strong>Executive Overview</strong></h4>
<p>Analyzed ${info.totalComments} customer feedback entries with an average CSAT of ${info.avgCSAT} ${parseFloat(info.avgCSAT) >= 4 ? '<span style="color:green;">▲</span>' : '<span style="color:red;">▼</span>'} and NPS of ${info.avgNPS}. [Provide 2-3 sentences of high-level insights about overall customer satisfaction trends]</p>

<h4><strong>Top Pain Points</strong></h4>
<ul>
<li><strong>${painPointsData[0]?.pain || 'Data Processing'}:</strong> ${painPointsData[0]?.frequency || 0} mentions (Severity: ${painPointsData[0]?.severity || 0})</li>
<li><strong>${painPointsData[1]?.pain || 'Service Quality'}:</strong> ${painPointsData[1]?.frequency || 0} mentions (Severity: ${painPointsData[1]?.severity || 0})</li>
<li><strong>${painPointsData[2]?.pain || 'Response Time'}:</strong> ${painPointsData[2]?.frequency || 0} mentions (Severity: ${painPointsData[2]?.severity || 0})</li>
</ul>

<h4><strong>Performance Insights</strong></h4>
<p><strong>Best Performing Vendor:</strong> ${vendorData[0]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[0]?.delta || 0} <span style="color:green;">▲</span>)</p>
<p><strong>Needs Improvement:</strong> ${vendorData[vendorData.length - 1]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[vendorData.length - 1]?.delta || 0} <span style="color:red;">▼</span>)</p>

<h4><strong>Actionable Recommendations</strong></h4>
<ul>
<li><strong>Priority 1:</strong> Address "${painPointsData[0]?.pain || 'top pain point'}" immediately - highest severity score</li>
<li><strong>Priority 2:</strong> Investigate underperforming vendor: ${vendorData[vendorData.length - 1]?.vendor || 'N/A'}</li>
<li><strong>Priority 3:</strong> Implement feedback loop for domains with CSAT below 3.0</li>
</ul>

<h4><strong>Success Patterns</strong></h4>
<p>Vendor "${vendorData[0]?.vendor || 'N/A'}" demonstrates superior performance with ${vendorData[0]?.delta || 0} points above average CSAT. [Analyze what makes this vendor successful]</p>

<h4><strong>Root Cause Hypothesis</strong></h4>
<p>[Provide 2-3 sentences hypothesizing the underlying causes of the top pain points based on the data patterns]</p>

<h4><strong>Trend Watch</strong></h4>
<p>[Highlight 2-3 emerging patterns or risks based on the data distribution and performance metrics]</p>

---

<h3><strong>Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
<li>Total Comments: ${info.totalComments}</li>
<li>Average CSAT: ${info.avgCSAT}</li>
<li>Average NPS: ${info.avgNPS}</li>
<li>Vendors: ${info.vendors}</li>
<li>Locations: ${info.locations}</li>
</ul>

<h4><strong>🔥 Top Issues or Praises</strong></h4>
<p>Most frequently mentioned themes:</p>
<ul>
<li>${painPointsData[0]?.pain || 'Service Issues'} (${painPointsData[0]?.frequency || 0} mentions)</li>
<li>${painPointsData[1]?.pain || 'Process Issues'} (${painPointsData[1]?.frequency || 0} mentions)</li>
<li>${painPointsData[2]?.pain || 'Product Issues'} (${painPointsData[2]?.frequency || 0} mentions)</li>
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
<tr><td>Agent</td><td>Service delays, communication gaps</td><td>[Calculate from data]</td><td>"No callback after escalation"</td></tr>
<tr><td>Customer</td><td>Account issues, verification problems</td><td>[Calculate from data]</td><td>"Couldn't verify with OTP"</td></tr>
<tr><td>Process</td><td>Workflow inefficiencies, delays</td><td>[Calculate from data]</td><td>"Refund process too slow"</td></tr>
<tr><td>Technology</td><td>System errors, technical failures</td><td>[Calculate from data]</td><td>"App keeps crashing"</td></tr>
</table>

<h4><strong>🔍 ACPT Sub-driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT</th><th>L1 Category</th><th>Sub-driver</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
<tr><td>Agent</td><td>Response Time</td><td>Delayed Follow-up</td><td>Negative</td><td>[Calculate]</td><td>"Waiting 3 days for response"</td></tr>
<tr><td>Process</td><td>Refund Process</td><td>Resolution Gaps</td><td>Negative</td><td>[Calculate]</td><td>"Still waiting for refund"</td></tr>
<tr><td>Technology</td><td>App Performance</td><td>System Stability</td><td>Negative</td><td>[Calculate]</td><td>"App crashes frequently"</td></tr>
</table>

<h4><strong>✅ Data Verification</strong></h4>
<ul>
<li>Sum of ACPT Table = ${info.totalComments}? [Verify and confirm]</li>
<li>Sum of Sub-driver Table = ${info.totalComments}? [Verify and confirm]</li>
<li>All feedback categorized? [Confirm 100% coverage]</li>
</ul>

<h4><strong>🎯 Insight Anchors</strong></h4>
<p><strong>Counterintuitive Findings:</strong></p>
<ul>
<li>[Identify unexpected pattern from vendor performance data]</li>
<li>[Highlight surprising correlation between pain points and CSAT]</li>
<li>[Point out domain-specific trends that contradict expectations]</li>
</ul>

REMEMBER: Output BOTH Part 1 AND Part 2 completely. Use provided data to fill in specific numbers where placeholders exist. Maintain HTML formatting exactly as shown.`;
        }

        async function testAISummary() {
            const btn = document.getElementById('testBtn');
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            resultDiv.style.display = 'block';
            resultContent.innerHTML = '<p>Generating AI summary...</p>';
            
            try {
                const totalComments = sampleData.reduce((sum, row) => sum + (row.total_comments || 0), 0);
                const avgCSAT = sampleData.reduce((sum, row) => sum + ((row.avg_csat || 0) * (row.total_comments || 0)), 0) / totalComments;
                const avgNPS = sampleData.reduce((sum, row) => sum + ((row.avg_nps || 0) * (row.total_comments || 0)), 0) / totalComments;

                console.log('Calculated:', { totalComments, avgCSAT, avgNPS });

                const analytics = generateAdvancedAnalytics(sampleData, totalComments, avgCSAT, avgNPS);
                const recordInfo = buildRecordInfo(sampleData, totalComments, avgCSAT, avgNPS);
                const prompt = buildEnhancedPrompt(analytics, recordInfo, avgCSAT, avgNPS);

                console.log('Generated prompt length:', prompt.length);

                // Call the AI summary API
                const response = await fetch('ai_summary.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();
                console.log('AI response:', data);

                if (data.status === 'success') {
                    resultContent.innerHTML = data.summary_html;
                } else {
                    resultContent.innerHTML = `<p class="error">Error: ${data.message}</p>`;
                }
            } catch (error) {
                console.error('Test error:', error);
                resultContent.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test AI Summary Generation';
            }
        }
    </script>
</body>
</html>
