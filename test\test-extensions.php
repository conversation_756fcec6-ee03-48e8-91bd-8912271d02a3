<?php
// Display all loaded PHP extensions
echo "<h1>PHP Extensions</h1>";
echo "<pre>";
print_r(get_loaded_extensions());
echo "</pre>";

// Check for specific extensions
$requiredExtensions = [
    'zip',
    'xml',
    'gd',
    'mbstring',
    'intl',
    'mysqli',
    'curl',
    'PDO',
    'json',
    'fileinfo',
    'dom',
    'SimpleXML',
    'xmlreader',
    'xmlwriter',
    'iconv',
    'ctype'
];

echo "<h2>Required Extensions Status</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Extension</th><th>Status</th></tr>";

foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? "✅ Loaded" : "❌ Not Loaded";
    $color = $loaded ? "green" : "red";
    
    echo "<tr><td>$ext</td><td style='color: $color;'>$status</td></tr>";
}

echo "</table>";

// Test ZipArchive specifically
echo "<h2>ZipArchive Test</h2>";
if (class_exists('ZipArchive')) {
    echo "<p style='color: green;'>ZipArchive class is available ✅</p>";
    
    // Try to create a test zip file
    try {
        $zip = new ZipArchive();
        $filename = "test.zip";
        
        if ($zip->open($filename, ZipArchive::CREATE) !== TRUE) {
            echo "<p style='color: red;'>Cannot create zip file</p>";
        } else {
            $zip->addFromString("testfile.txt", "Test content");
            $zip->close();
            echo "<p style='color: green;'>Successfully created test zip file ✅</p>";
            
            // Clean up
            unlink($filename);
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error testing ZipArchive: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>ZipArchive class is NOT available ❌</p>";
    echo "<p>This is the class that's causing your error. The PHP zip extension needs to be installed.</p>";
}

// Display PHP info
echo "<h2>PHP Info</h2>";
phpinfo();
?>
