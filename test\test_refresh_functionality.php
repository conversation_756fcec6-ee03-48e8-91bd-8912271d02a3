<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Refresh Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .filter-container { margin: 10px 0; }
        .filter-container select, .filter-container input { margin: 5px; padding: 5px; }
        .refresh-btn { background: #fbbf24; color: black; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 10px 0; }
        .refresh-btn:hover { background: #f59e0b; }
        .test-btn { background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-btn:hover { background: #2563eb; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Refresh Functionality Test</h1>
    
    <div class="test-section info">
        <h2>Filter Coordinator Test Environment</h2>
        <p>This page simulates the dashboard filter environment to test the refresh functionality.</p>
        
        <div class="filter-container">
            <label>Domain Category:</label>
            <select id="domainCategoryDropdown">
                <option value="all">All Domain Categories</option>
                <option value="Finance & Banking">Finance & Banking</option>
                <option value="Technology">Technology</option>
                <option value="Healthcare">Healthcare</option>
            </select>
        </div>
        
        <div class="filter-container">
            <label>Data ID:</label>
            <select id="dataIdDropdown">
                <option value="all">All Data IDs</option>
                <option value="682b54974bab6">682b54974bab6</option>
                <option value="test123">test123</option>
            </select>
        </div>
        
        <div class="filter-container">
            <label>Sentiment:</label>
            <select id="sentimentDropdown">
                <option value="all">All Sentiments</option>
                <option value="Positive">Positive</option>
                <option value="Neutral">Neutral</option>
                <option value="Negative">Negative</option>
            </select>
        </div>
        
        <div class="filter-container">
            <label>Start Date:</label>
            <input type="date" id="startDateFilter">
        </div>
        
        <div class="filter-container">
            <label>End Date:</label>
            <input type="date" id="endDateFilter">
        </div>
        
        <div class="filter-container">
            <label>Product Type:</label>
            <select id="productTypeDropdown">
                <option value="all">All Product Types</option>
                <option value="Product A">Product A</option>
                <option value="Product B">Product B</option>
            </select>
        </div>
        
        <div class="filter-container">
            <label>Channel Type:</label>
            <select id="channelTypeDropdown">
                <option value="all">All Channel Types</option>
                <option value="Online">Online</option>
                <option value="Offline">Offline</option>
            </select>
        </div>
        
        <div class="filter-container">
            <label>Teams:</label>
            <select id="teamDropdown">
                <option value="all">All Teams</option>
                <option value="Team A">Team A</option>
                <option value="Team B">Team B</option>
            </select>
        </div>
        
        <div class="filter-container">
            <label>Resolution Status:</label>
            <select id="resolutionStatusDropdown">
                <option value="all">All Resolution Status</option>
                <option value="Resolved">Resolved</option>
                <option value="Pending">Pending</option>
            </select>
        </div>
        
        <button id="refreshButton" class="refresh-btn">
            🔄 Refresh
        </button>
    </div>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button class="test-btn" onclick="setRandomFilters()">Set Random Filter Values</button>
        <button class="test-btn" onclick="checkFilterStates()">Check Current Filter States</button>
        <button class="test-btn" onclick="testRefreshButton()">Test Refresh Button</button>
        <button class="test-btn" onclick="clearTestResults()">Clear Test Results</button>
    </div>
    
    <div id="testResults" class="test-section">
        <h2>Test Results</h2>
        <div id="statusContainer"></div>
    </div>
    
    <!-- Include the filter coordinator -->
    <script src="../assets/js/filter-coordinator.js"></script>
    
    <script>
        // Mock functions that the filter coordinator expects
        window.fetchData = function() {
            console.log('Mock fetchData called');
            addStatus('fetchData() called successfully', 'success');
        };
        
        window.debouncedFetchData = function() {
            console.log('Mock debouncedFetchData called');
            addStatus('debouncedFetchData() called successfully', 'success');
        };
        
        window.fetchMainDrivers = function() {
            console.log('Mock fetchMainDrivers called');
            addStatus('fetchMainDrivers() called successfully', 'success');
        };
        
        // Test functions
        function addStatus(message, type = 'info') {
            const container = document.getElementById('statusContainer');
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(status);
            container.scrollTop = container.scrollHeight;
        }
        
        function setRandomFilters() {
            const filters = [
                { id: 'domainCategoryDropdown', values: ['all', 'Finance & Banking', 'Technology', 'Healthcare'] },
                { id: 'dataIdDropdown', values: ['all', '682b54974bab6', 'test123'] },
                { id: 'sentimentDropdown', values: ['all', 'Positive', 'Neutral', 'Negative'] },
                { id: 'productTypeDropdown', values: ['all', 'Product A', 'Product B'] },
                { id: 'channelTypeDropdown', values: ['all', 'Online', 'Offline'] },
                { id: 'teamDropdown', values: ['all', 'Team A', 'Team B'] },
                { id: 'resolutionStatusDropdown', values: ['all', 'Resolved', 'Pending'] }
            ];
            
            filters.forEach(filter => {
                const element = document.getElementById(filter.id);
                const randomValue = filter.values[Math.floor(Math.random() * filter.values.length)];
                element.value = randomValue;
            });
            
            // Set random dates
            const today = new Date();
            const pastDate = new Date(today.getTime() - (Math.random() * 30 * 24 * 60 * 60 * 1000));
            document.getElementById('startDateFilter').value = pastDate.toISOString().split('T')[0];
            document.getElementById('endDateFilter').value = today.toISOString().split('T')[0];
            
            addStatus('Random filter values set', 'success');
        }
        
        function checkFilterStates() {
            const filterElements = [
                'domainCategoryDropdown', 'dataIdDropdown', 'sentimentDropdown',
                'startDateFilter', 'endDateFilter', 'productTypeDropdown',
                'channelTypeDropdown', 'teamDropdown', 'resolutionStatusDropdown'
            ];
            
            let allDefault = true;
            const states = [];
            
            filterElements.forEach(id => {
                const element = document.getElementById(id);
                const value = element.value;
                const isDefault = (value === 'all' || value === '');
                
                if (!isDefault) allDefault = false;
                
                states.push(`${id}: ${value} ${isDefault ? '(default)' : '(custom)'}`);
            });
            
            addStatus(`Filter states checked: ${allDefault ? 'All filters are at default values' : 'Some filters have custom values'}`, allDefault ? 'success' : 'error');
            states.forEach(state => addStatus(state, 'info'));
            
            // Check filter coordinator state if available
            if (window.filterCoordinator) {
                const coordinatorState = window.filterCoordinator.getCurrentFilters();
                addStatus(`Filter coordinator state: ${JSON.stringify(coordinatorState)}`, 'info');
            }
        }
        
        function testRefreshButton() {
            addStatus('Testing refresh button...', 'info');
            
            // First set some non-default values
            setRandomFilters();
            
            setTimeout(() => {
                addStatus('Clicking refresh button...', 'info');
                document.getElementById('refreshButton').click();
                
                // Check results after a delay
                setTimeout(() => {
                    checkFilterStates();
                    addStatus('Refresh test completed', 'success');
                }, 1000);
            }, 500);
        }
        
        function clearTestResults() {
            document.getElementById('statusContainer').innerHTML = '';
            addStatus('Test results cleared', 'info');
        }
        
        // Add refresh button functionality (simulating dashboard behavior)
        document.getElementById('refreshButton').addEventListener('click', async function() {
            addStatus('Refresh button clicked', 'info');
            
            try {
                if (window.filterCoordinator) {
                    addStatus('Using filter coordinator to clear filters', 'info');
                    await window.filterCoordinator.clearAllFilters();
                    addStatus('Filter coordinator clearAllFilters() completed', 'success');
                } else {
                    addStatus('Filter coordinator not available, using manual reset', 'error');
                    
                    // Manual reset
                    document.getElementById('domainCategoryDropdown').value = 'all';
                    document.getElementById('dataIdDropdown').value = 'all';
                    document.getElementById('sentimentDropdown').value = 'all';
                    document.getElementById('startDateFilter').value = '';
                    document.getElementById('endDateFilter').value = '';
                    document.getElementById('productTypeDropdown').value = 'all';
                    document.getElementById('channelTypeDropdown').value = 'all';
                    document.getElementById('teamDropdown').value = 'all';
                    document.getElementById('resolutionStatusDropdown').value = 'all';
                    
                    addStatus('Manual filter reset completed', 'success');
                }
                
                // Simulate data refresh
                if (typeof window.debouncedFetchData === 'function') {
                    window.debouncedFetchData();
                }
                
            } catch (error) {
                addStatus(`Error during refresh: ${error.message}`, 'error');
            }
        });
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            addStatus('Test page loaded', 'success');
            
            setTimeout(() => {
                if (window.filterCoordinator) {
                    addStatus('Filter coordinator initialized successfully', 'success');
                } else {
                    addStatus('Filter coordinator not found', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
