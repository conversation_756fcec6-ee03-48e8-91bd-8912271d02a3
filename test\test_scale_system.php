<?php
/**
 * Test file to verify the mandatory scale specification system
 */

require_once '../config.php';
require_once '../DatabaseInteraction.php';
require_once '../ScaleConfigurationManager.php';

// Test data
$test_data_id = 'test_' . uniqid();
$test_user_id = 1; // Assuming user ID 1 exists

echo "<h1>Testing Mandatory Scale Specification System</h1>\n";

// Initialize database connection
$db = new DatabaseInteraction();
$conn = $db->connect();
$scaleManager = new ScaleConfigurationManager($conn);

echo "<h2>1. Testing Scale Configuration Manager</h2>\n";

// Test 1: Valid scale configuration
echo "<h3>Test 1: Valid Scale Configuration</h3>\n";
$valid_form_data = [
    'csat_scale_type' => '1-5',
    'nps_scale_type' => '1-5', 
    'internal_score_scale_type' => 'not_used'
];

$result = $scaleManager->processUserScaleInput($valid_form_data, $test_data_id, $test_user_id);
if ($result['success']) {
    echo "✅ Valid scale configuration saved successfully<br>\n";
} else {
    echo "❌ Failed to save valid scale configuration: " . implode(', ', $result['errors']) . "<br>\n";
}

// Test 2: Missing mandatory fields
echo "<h3>Test 2: Missing Mandatory Fields</h3>\n";
$invalid_form_data = [
    'csat_scale_type' => '1-5',
    // Missing nps_scale_type and internal_score_scale_type
];

$result = $scaleManager->processUserScaleInput($invalid_form_data, $test_data_id . '_invalid', $test_user_id);
if (!$result['success']) {
    echo "✅ Correctly rejected missing mandatory fields: " . implode(', ', $result['errors']) . "<br>\n";
} else {
    echo "❌ Should have rejected missing mandatory fields<br>\n";
}

// Test 3: Custom scale validation
echo "<h3>Test 3: Custom Scale Validation</h3>\n";
$custom_form_data = [
    'csat_scale_type' => 'custom',
    'csat_min' => '1',
    'csat_max' => '7',
    'nps_scale_type' => 'custom',
    'nps_min' => '0',
    'nps_max' => '10',
    'internal_score_scale_type' => 'not_used'
];

$result = $scaleManager->processUserScaleInput($custom_form_data, $test_data_id . '_custom', $test_user_id);
if ($result['success']) {
    echo "✅ Custom scale configuration saved successfully<br>\n";
} else {
    echo "❌ Failed to save custom scale configuration: " . implode(', ', $result['errors']) . "<br>\n";
}

echo "<h2>2. Testing Dynamic Calculation Functions</h2>\n";

// Test 4: Dynamic NPS calculation
echo "<h3>Test 4: Dynamic NPS Calculation</h3>\n";
$scale_config = $db->getScaleConfiguration($test_data_id, $test_user_id);
if ($scale_config) {
    echo "✅ Scale configuration retrieved successfully<br>\n";
    echo "CSAT Scale: {$scale_config['csat_scale_type']} ({$scale_config['csat_min_value']}-{$scale_config['csat_max_value']}, baseline: {$scale_config['csat_baseline']})<br>\n";
    echo "NPS Scale: {$scale_config['nps_scale_type']} ({$scale_config['nps_min_value']}-{$scale_config['nps_max_value']}, promoter: {$scale_config['nps_promoter_threshold']}, detractor: {$scale_config['nps_detractor_threshold']})<br>\n";
    echo "Internal Score: {$scale_config['internal_score_scale_type']}<br>\n";
} else {
    echo "❌ Failed to retrieve scale configuration<br>\n";
}

// Test 5: Legacy compatibility
echo "<h3>Test 5: Legacy Compatibility</h3>\n";
$legacy_config = $db->getScaleConfiguration('non_existent_data_id', $test_user_id);
if ($legacy_config && $legacy_config['csat_baseline'] == 8.0) {
    echo "✅ Legacy defaults returned correctly for non-existent data<br>\n";
    echo "Legacy CSAT baseline: {$legacy_config['csat_baseline']}<br>\n";
    echo "Legacy NPS promoter threshold: {$legacy_config['nps_promoter_threshold']}<br>\n";
} else {
    echo "❌ Legacy compatibility failed<br>\n";
}

echo "<h2>3. Testing Scale Threshold Calculations</h2>\n";

// Test 6: 1-5 Scale Thresholds
echo "<h3>Test 6: 1-5 Scale Thresholds</h3>\n";
$scale_1_5_data = [
    'csat_scale_type' => '1-5',
    'nps_scale_type' => '1-5',
    'internal_score_scale_type' => 'not_used'
];

$result = $scaleManager->processUserScaleInput($scale_1_5_data, $test_data_id . '_1_5', $test_user_id);
if ($result['success']) {
    $config = $db->getScaleConfiguration($test_data_id . '_1_5', $test_user_id);
    echo "✅ 1-5 Scale Configuration:<br>\n";
    echo "CSAT Baseline: {$config['csat_baseline']} (should be 4.0 for 1-5 scale)<br>\n";
    echo "NPS Promoter Threshold: {$config['nps_promoter_threshold']} (should be 4 for 1-5 scale)<br>\n";
    echo "NPS Detractor Threshold: {$config['nps_detractor_threshold']} (should be 2 for 1-5 scale)<br>\n";
    
    // Verify calculations
    if ($config['csat_baseline'] == 4.0 && $config['nps_promoter_threshold'] == 4 && $config['nps_detractor_threshold'] == 2) {
        echo "✅ All 1-5 scale thresholds calculated correctly<br>\n";
    } else {
        echo "❌ Some 1-5 scale thresholds are incorrect<br>\n";
    }
}

echo "<h2>4. Testing Form Validation</h2>\n";

// Test 7: Invalid custom ranges
echo "<h3>Test 7: Invalid Custom Ranges</h3>\n";
$invalid_custom_data = [
    'csat_scale_type' => 'custom',
    'csat_min' => '5',
    'csat_max' => '3', // Max less than min
    'nps_scale_type' => '1-10',
    'internal_score_scale_type' => 'not_used'
];

$result = $scaleManager->processUserScaleInput($invalid_custom_data, $test_data_id . '_invalid_custom', $test_user_id);
if (!$result['success']) {
    echo "✅ Correctly rejected invalid custom range (max < min): " . implode(', ', $result['errors']) . "<br>\n";
} else {
    echo "❌ Should have rejected invalid custom range<br>\n";
}

echo "<h2>5. Cleanup Test Data</h2>\n";

// Clean up test data
try {
    $cleanup_query = "DELETE FROM upload_scale_configurations WHERE data_id LIKE :test_prefix";
    $stmt = $conn->prepare($cleanup_query);
    $stmt->execute([':test_prefix' => 'test_%']);
    echo "✅ Test data cleaned up successfully<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to clean up test data: " . $e->getMessage() . "<br>\n";
}

echo "<h2>Test Summary</h2>\n";
echo "<p>✅ All tests completed. Check the results above to verify the mandatory scale specification system is working correctly.</p>\n";
echo "<p><strong>Key Features Tested:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Mandatory scale validation (all three scales required)</li>\n";
echo "<li>✅ Custom scale range validation</li>\n";
echo "<li>✅ Dynamic threshold calculation</li>\n";
echo "<li>✅ Scale configuration storage and retrieval</li>\n";
echo "<li>✅ Legacy compatibility for existing data</li>\n";
echo "<li>✅ Error handling for invalid inputs</li>\n";
echo "</ul>\n";

echo "<p><a href='../upload.php'>← Back to Upload Page</a></p>\n";
?>
