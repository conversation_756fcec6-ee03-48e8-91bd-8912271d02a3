<?php
/**
 * Direct Record Monitor - A self-healing system for ensuring all records are processed
 * This version uses direct database connection instead of DatabaseInteraction.php
 */

// Set script execution time to a high value to allow for processing large datasets
ini_set('max_execution_time', 1800); // 30 minutes
ini_set('memory_limit', '512M');

// Enable error logging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials - REPLACE WITH YOUR ACTUAL CREDENTIALS
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Function to log messages
function logMessage($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
    // Force output buffer flush
    if (ob_get_level() > 0) {
        ob_flush();
    }
    flush();
}

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    logMessage("Connected successfully to database: $dbname on host: $host");
} catch (PDOException $e) {
    logMessage("Connection failed: " . $e->getMessage());
    exit(1);
}

// Test database connection
try {
    $testQuery = "SELECT COUNT(*) as count FROM feedback_data WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    logMessage("Database connection test successful. Found " . $testResult['count'] . " records for data_id 682b54974bab6");
    
    $testQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    logMessage("SELECT test on analyzed_comments successful. Found " . $testResult['count'] . " records for data_id 682b54974bab6");
} catch (PDOException $e) {
    logMessage("Database test failed: " . $e->getMessage());
    exit(1);
}

// Get the specific data_id from command line argument
$data_id = '682b54974bab6'; // Default
if (isset($argv[1]) && !empty($argv[1])) {
    $data_id = $argv[1];
}

logMessage("Processing data_id: $data_id");

// Get counts for feedback_data and analyzed_comments
try {
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    logMessage("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    if ($missing_count <= 0) {
        logMessage("No missing records for data_id: $data_id");
        exit(0);
    }
    
    // Get all feedback data records
    $allFeedbackQuery = "SELECT * FROM feedback_data WHERE data_id = :data_id ORDER BY id";
    $allFeedbackStmt = $conn->prepare($allFeedbackQuery);
    $allFeedbackStmt->bindParam(':data_id', $data_id);
    $allFeedbackStmt->execute();
    $allFeedback = $allFeedbackStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all analyzed comments
    $allAnalyzedQuery = "SELECT comment FROM analyzed_comments WHERE data_id = :data_id";
    $allAnalyzedStmt = $conn->prepare($allAnalyzedQuery);
    $allAnalyzedStmt->bindParam(':data_id', $data_id);
    $allAnalyzedStmt->execute();
    $allAnalyzed = $allAnalyzedStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Find the difference
    $missingRecords = [];
    foreach ($allFeedback as $record) {
        if (!in_array($record['feedback_data'], $allAnalyzed)) {
            $missingRecords[] = $record;
        }
    }
    
    logMessage("Found " . count($missingRecords) . " missing records using direct comparison");
    
    // Process missing records
    $processed = 0;
    foreach ($missingRecords as $record) {
        // Check if record is already in queue
        $checkQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = :data_id AND comment = :comment";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bindParam(':data_id', $data_id);
        $checkStmt->bindParam(':comment', $record['feedback_data']);
        $checkStmt->execute();
        $checkResult = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($checkResult['count'] > 0) {
            logMessage("Record ID: " . $record['id'] . " already in queue for data_id: $data_id");
            continue;
        }
        
        // Insert into comment_queue
        try {
            $insertQuery = "INSERT INTO comment_queue (
                comment, data_id, user_id, csat, nps, pid, status
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending'
            )";
            
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindParam(':comment', $record['feedback_data']);
            $insertStmt->bindParam(':data_id', $data_id);
            $insertStmt->bindParam(':user_id', $record['user_id']);
            $insertStmt->bindParam(':csat', $record['csat']);
            $insertStmt->bindParam(':nps', $record['nps']);
            $insertStmt->bindParam(':pid', $record['pid']);
            
            $result = $insertStmt->execute();
            
            if ($result) {
                $processed++;
                logMessage("Enqueued record ID: " . $record['id'] . " for data_id: $data_id");
                
                // Verify the record was inserted
                $verifyQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = :data_id AND comment = :comment";
                $verifyStmt = $conn->prepare($verifyQuery);
                $verifyStmt->bindParam(':data_id', $data_id);
                $verifyStmt->bindParam(':comment', $record['feedback_data']);
                $verifyStmt->execute();
                $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                
                if ($verifyResult['count'] > 0) {
                    logMessage("Verified record ID: " . $record['id'] . " was successfully inserted into comment_queue");
                } else {
                    logMessage("WARNING: Record ID: " . $record['id'] . " was not found in comment_queue after insertion");
                }
            } else {
                logMessage("Failed to enqueue record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
            }
        } catch (PDOException $e) {
            logMessage("ERROR: Failed to enqueue record ID: " . $record['id'] . " - " . $e->getMessage());
        }
        
        // Sleep briefly to avoid overloading the database
        usleep(100000); // 0.1 seconds
    }
    
    logMessage("Completed processing for data_id: $data_id - Enqueued $processed records");
    
} catch (PDOException $e) {
    logMessage("ERROR: " . $e->getMessage());
    exit(1);
}
