<?php
// Simple script to test database connection and permissions

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Import required files
require_once 'DatabaseInteraction.php';

echo "Starting database test...\n";

try {
    // Initialize database connection
    $db = new DatabaseInteraction();
    $conn = $db->connect();
    
    if (!$conn) {
        echo "ERROR: Failed to connect to database\n";
        exit(1);
    }
    
    echo "Database connection successful\n";
    
    // Test SELECT on feedback_data
    $testQuery = "SELECT COUNT(*) as count FROM feedback_data WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    echo "SELECT test on feedback_data: Found " . $testResult['count'] . " records for data_id 682b54974bab6\n";
    
    // Test SELECT on analyzed_comments
    $testQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    echo "SELECT test on analyzed_comments: Found " . $testResult['count'] . " records for data_id 682b54974bab6\n";
    
    // Test INSERT on comment_queue with a transaction (will be rolled back)
    $conn->beginTransaction();
    try {
        $testInsertQuery = "INSERT INTO comment_queue (comment, data_id, user_id, csat, nps, pid, status) 
                           VALUES ('TEST_PERMISSION_CHECK', '682b54974bab6', 1, 0, 0, '0', 'pending')";
        $testInsertStmt = $conn->prepare($testInsertQuery);
        $testInsertResult = $testInsertStmt->execute();
        
        if ($testInsertResult) {
            echo "INSERT test on comment_queue: Successful\n";
            
            // Verify the record was inserted
            $verifyQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE comment = 'TEST_PERMISSION_CHECK'";
            $verifyStmt = $conn->prepare($verifyQuery);
            $verifyStmt->execute();
            $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            echo "Verification: Found " . $verifyResult['count'] . " test records in comment_queue\n";
        } else {
            echo "INSERT test on comment_queue failed: " . json_encode($testInsertStmt->errorInfo()) . "\n";
        }
        
        // Always rollback the test insert
        $conn->rollBack();
        echo "Test transaction rolled back successfully\n";
    } catch (PDOException $e) {
        $conn->rollBack();
        echo "INSERT test on comment_queue failed: " . $e->getMessage() . "\n";
    }
    
    // Test INSERT on analyzed_comments with a transaction (will be rolled back)
    $conn->beginTransaction();
    try {
        $testInsertQuery = "INSERT INTO analyzed_comments (comment, data_id, user_id, csat, nps, pid, main_driver, sub_driver, sentiment) 
                           VALUES ('TEST_PERMISSION_CHECK', '682b54974bab6', 1, 0, 0, '0', 'Test', 'Test', 'Neutral')";
        $testInsertStmt = $conn->prepare($testInsertQuery);
        $testInsertResult = $testInsertStmt->execute();
        
        if ($testInsertResult) {
            echo "INSERT test on analyzed_comments: Successful\n";
            
            // Verify the record was inserted
            $verifyQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE comment = 'TEST_PERMISSION_CHECK'";
            $verifyStmt = $conn->prepare($verifyQuery);
            $verifyStmt->execute();
            $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            echo "Verification: Found " . $verifyResult['count'] . " test records in analyzed_comments\n";
        } else {
            echo "INSERT test on analyzed_comments failed: " . json_encode($testInsertStmt->errorInfo()) . "\n";
        }
        
        // Always rollback the test insert
        $conn->rollBack();
        echo "Test transaction rolled back successfully\n";
    } catch (PDOException $e) {
        $conn->rollBack();
        echo "INSERT test on analyzed_comments failed: " . $e->getMessage() . "\n";
    }
    
    echo "Database test completed successfully\n";
    
} catch (Exception $e) {
    echo "CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Error trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
