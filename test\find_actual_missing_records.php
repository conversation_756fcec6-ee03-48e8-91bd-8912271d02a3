<?php
/**
 * <PERSON><PERSON>t to find and process actual missing records
 * This script uses a more thorough approach to identify missing records
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set high memory and execution time limits
ini_set('memory_limit', '1G');
ini_set('max_execution_time', 3600); // 1 hour

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682b54974bab6';

// Log file
$log_file = 'find_missing_records.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    
    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting thorough search for missing records for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    if ($missing_count <= 0) {
        log_message("No missing records for data_id: $data_id");
        exit(0);
    }
    
    // Get all feedback data records
    log_message("Retrieving all feedback data records...");
    $allFeedbackQuery = "SELECT id, feedback_data FROM feedback_data WHERE data_id = :data_id";
    $allFeedbackStmt = $conn->prepare($allFeedbackQuery);
    $allFeedbackStmt->bindParam(':data_id', $data_id);
    $allFeedbackStmt->execute();
    $allFeedback = $allFeedbackStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Retrieved " . count($allFeedback) . " feedback records");
    
    // Get all analyzed comments
    log_message("Retrieving all analyzed comments...");
    $allAnalyzedQuery = "SELECT comment FROM analyzed_comments WHERE data_id = :data_id";
    $allAnalyzedStmt = $conn->prepare($allAnalyzedQuery);
    $allAnalyzedStmt->bindParam(':data_id', $data_id);
    $allAnalyzedStmt->execute();
    $analyzedComments = $allAnalyzedStmt->fetchAll(PDO::FETCH_COLUMN);
    
    log_message("Retrieved " . count($analyzedComments) . " analyzed comments");
    
    // Find missing records by comparing feedback_data with analyzed_comments
    log_message("Identifying missing records by comparing feedback_data with analyzed_comments...");
    $missingRecordIds = [];
    
    foreach ($allFeedback as $record) {
        if (!in_array($record['feedback_data'], $analyzedComments)) {
            $missingRecordIds[] = $record['id'];
        }
    }
    
    log_message("Found " . count($missingRecordIds) . " missing record IDs");
    
    if (count($missingRecordIds) == 0) {
        log_message("No specific missing records found despite count discrepancy. This suggests duplicate records or a counting issue.");
        exit(0);
    }
    
    // Get the full details of the missing records
    $missingRecords = [];
    $batchSize = 100;
    $totalMissing = count($missingRecordIds);
    
    log_message("Retrieving details for $totalMissing missing records in batches of $batchSize");
    
    for ($i = 0; $i < $totalMissing; $i += $batchSize) {
        $batch = array_slice($missingRecordIds, $i, $batchSize);
        $placeholders = implode(',', array_fill(0, count($batch), '?'));
        
        $detailsQuery = "SELECT * FROM feedback_data WHERE id IN ($placeholders)";
        $detailsStmt = $conn->prepare($detailsQuery);
        
        // Bind the ID values
        foreach ($batch as $index => $id) {
            $detailsStmt->bindValue($index + 1, $id);
        }
        
        $detailsStmt->execute();
        $batchRecords = $detailsStmt->fetchAll(PDO::FETCH_ASSOC);
        $missingRecords = array_merge($missingRecords, $batchRecords);
        
        log_message("Retrieved details for batch " . (floor($i / $batchSize) + 1) . " of " . ceil($totalMissing / $batchSize));
    }
    
    log_message("Retrieved details for " . count($missingRecords) . " missing records");
    
    // Process missing records
    $processed = 0;
    $failed = 0;
    $total = count($missingRecords);
    
    log_message("Processing $total actual missing records...");
    
    foreach ($missingRecords as $index => $record) {
        // Create a unique timestamp for each record
        $timestamp = date('Y-m-d H:i:s', strtotime("now +$index seconds"));
        
        log_message("Processing record ID: " . $record['id'] . " (" . ($index + 1) . " of $total)");
        
        try {
            // Insert directly into analyzed_comments with the unique timestamp
            $insertQuery = "INSERT INTO analyzed_comments (
                comment, data_id, user_id, csat, nps, pid,
                main_driver, sub_driver, sentiment, created_at
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid,
                'Auto-Generated', 'Auto-Generated', 'Neutral', :created_at
            )";
            
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindParam(':comment', $record['feedback_data']);
            $insertStmt->bindParam(':data_id', $record['data_id']);
            $insertStmt->bindParam(':user_id', $record['user_id']);
            $insertStmt->bindParam(':csat', $record['csat']);
            $insertStmt->bindParam(':nps', $record['nps']);
            $insertStmt->bindParam(':pid', $record['pid']);
            $insertStmt->bindParam(':created_at', $timestamp);
            
            $result = $insertStmt->execute();
            
            if ($result) {
                $processed++;
                if ($processed % 10 == 0 || $processed == $total) {
                    log_message("Processed $processed of $total records");
                }
            } else {
                $failed++;
                log_message("Failed to insert record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
            }
        } catch (PDOException $e) {
            $failed++;
            log_message("ERROR: Failed to insert record ID: " . $record['id'] . " - " . $e->getMessage());
            
            // If it's a duplicate entry error, try with a different timestamp
            if ($e->getCode() == '23000') {
                try {
                    // Try with a different timestamp (add more seconds)
                    $retryTimestamp = date('Y-m-d H:i:s', strtotime("now +".($index + $total)." seconds"));
                    
                    log_message("Retrying record ID: " . $record['id'] . " with different timestamp");
                    
                    $insertStmt->bindParam(':created_at', $retryTimestamp);
                    $retryResult = $insertStmt->execute();
                    
                    if ($retryResult) {
                        $processed++;
                        $failed--; // Decrement the failure count since we succeeded on retry
                        log_message("Successfully inserted record ID: " . $record['id'] . " on retry");
                    } else {
                        log_message("Failed to insert record ID: " . $record['id'] . " on retry - Error: " . json_encode($insertStmt->errorInfo()));
                    }
                } catch (PDOException $e2) {
                    log_message("ERROR: Failed to insert record ID: " . $record['id'] . " on retry - " . $e2->getMessage());
                }
            }
        }
        
        // Sleep briefly every 100 records to avoid overloading the database
        if ($index % 100 == 99) {
            log_message("Sleeping briefly to avoid overloading the database...");
            usleep(500000); // 0.5 seconds
        }
    }
    
    log_message("Processing completed - Processed: $processed, Failed: $failed");
    
    // Get updated counts
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Updated counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    if ($missing_count > 0) {
        log_message("There are still $missing_count missing records. You may need to run this script again.");
    } else {
        log_message("All records have been processed successfully!");
    }
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
