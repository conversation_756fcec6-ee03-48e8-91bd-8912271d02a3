<?php
/**
 * Test script for record_monitor.php in local XAMPP environment
 */

// Set script execution time to a high value to allow for processing large datasets
ini_set('max_execution_time', 300); // 5 minutes
ini_set('memory_limit', '256M');

// Enable error logging
ini_set('display_errors', 1);
error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

// Import required files
require_once 'DatabaseInteraction.php';
require_once 'vendor/autoload.php';

// Load environment variables
try {
    if (class_exists('Dotenv\\Dotenv') && file_exists(__DIR__ . '/.env')) {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
        $dotenv->load();
    }
} catch (Exception $e) {
    echo "Warning: Could not load .env file: " . $e->getMessage() . "\n";
}

// Configuration options for local testing
$config = [
    'batch_size' => 20,                // Smaller batch size for testing
    'sleep_between_batches' => 2,      // Shorter sleep time for testing
    'rate_limit_per_minute' => 60,     // Adjusted rate limit for testing
    'log_file' => 'monitor_local.log', // Local log file
    'days_to_look_back' => 90,         // Look back further for testing
    'min_record_threshold' => 5,       // Lower threshold for testing
    'max_data_ids_per_run' => 5,       // Fewer data_ids for testing
];

echo "Starting record monitor test...\n";

// Initialize database connection
$db = new DatabaseInteraction();
$conn = $db->connect();

if (!$conn) {
    echo "ERROR: Failed to connect to database\n";
    exit(1);
}

// Function to log messages to console and log file
function logMessage($message) {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    
    // Output to console
    echo $logMessage;
    
    // Write to log file
    file_put_contents($config['log_file'], $logMessage, FILE_APPEND);
}

// Function to get data_ids with potential missing records
function getDataIdsWithMissingRecords($conn, $config) {
    try {
        // Calculate the date threshold for looking back
        $dateThreshold = date('Y-m-d H:i:s', strtotime("-{$config['days_to_look_back']} days"));
        
        // Query to find data_ids with potential missing records
        $query = "
            SELECT 
                fd.data_id,
                COUNT(fd.id) AS feedback_count,
                COUNT(ac.id) AS analyzed_count
            FROM 
                (SELECT DISTINCT data_id FROM feedback_data WHERE created_at >= :date_threshold) AS data_ids
            JOIN 
                feedback_data fd ON fd.data_id = data_ids.data_id
            LEFT JOIN 
                analyzed_comments ac ON ac.data_id = fd.data_id AND ac.comment = fd.feedback_data
            GROUP BY 
                fd.data_id
            HAVING 
                COUNT(fd.id) > :min_threshold AND COUNT(fd.id) > COUNT(ac.id)
            ORDER BY 
                (COUNT(fd.id) - COUNT(ac.id)) DESC
            LIMIT :max_data_ids
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':date_threshold', $dateThreshold);
        $stmt->bindParam(':min_threshold', $config['min_record_threshold'], PDO::PARAM_INT);
        $stmt->bindParam(':max_data_ids', $config['max_data_ids_per_run'], PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        logMessage("ERROR: Failed to get data_ids with missing records: " . $e->getMessage());
        return [];
    }
}

// Function to get missing records for a specific data_id
function getMissingRecords($conn, $data_id) {
    try {
        $query = "
            SELECT fd.*
            FROM feedback_data fd
            LEFT JOIN analyzed_comments ac ON ac.data_id = fd.data_id AND ac.comment = fd.feedback_data
            WHERE fd.data_id = :data_id AND ac.id IS NULL
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        logMessage("ERROR: Failed to get missing records for data_id $data_id: " . $e->getMessage());
        return [];
    }
}

// Function to check if a record is already in the comment queue
function isRecordInQueue($conn, $data_id, $comment) {
    try {
        $query = "
            SELECT id FROM comment_queue 
            WHERE data_id = :data_id AND comment = :comment
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':comment', $comment);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        logMessage("ERROR: Failed to check if record is in queue: " . $e->getMessage());
        return false;
    }
}

// Main function to process missing records
function processMissingRecords($db, $conn, $config) {
    logMessage("Starting record monitor process");
    
    // Get data_ids with missing records
    $dataIds = getDataIdsWithMissingRecords($conn, $config);
    
    if (empty($dataIds)) {
        logMessage("No data_ids with missing records found");
        return;
    }
    
    logMessage("Found " . count($dataIds) . " data_id(s) with missing records");
    
    // Process each data_id
    foreach ($dataIds as $dataIdInfo) {
        $data_id = $dataIdInfo['data_id'];
        $feedback_count = $dataIdInfo['feedback_count'];
        $analyzed_count = $dataIdInfo['analyzed_count'];
        $missing_count = $feedback_count - $analyzed_count;
        
        logMessage("Processing data_id: $data_id - Missing records: $missing_count ($analyzed_count of $feedback_count processed)");
        
        // Get missing records for this data_id
        $missingRecords = getMissingRecords($conn, $data_id);
        
        if (empty($missingRecords)) {
            logMessage("No missing records found for data_id: $data_id");
            continue;
        }
        
        logMessage("Found " . count($missingRecords) . " missing records for data_id: $data_id");
        
        // In test mode, just report the first 5 records but don't actually enqueue them
        $sample_records = array_slice($missingRecords, 0, 5);
        logMessage("Sample of missing records (first 5):");
        foreach ($sample_records as $record) {
            logMessage("  Record ID: " . $record['id'] . " - Comment: " . substr($record['feedback_data'], 0, 50) . "...");
        }
        
        // Ask user if they want to enqueue these records
        echo "\nDo you want to enqueue these missing records for processing? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($line) != 'y') {
            logMessage("Skipping enqueuing records for data_id: $data_id");
            continue;
        }
        
        // Process missing records in batches with rate limiting
        $processed = 0;
        $start_time = time();
        $records_this_minute = 0;
        
        foreach (array_chunk($missingRecords, $config['batch_size']) as $batch) {
            // Apply rate limiting
            if ($records_this_minute >= $config['rate_limit_per_minute']) {
                $elapsed = time() - $start_time;
                if ($elapsed < 60) {
                    $sleep_time = 60 - $elapsed;
                    logMessage("Rate limit reached. Sleeping for $sleep_time seconds");
                    sleep($sleep_time);
                }
                $start_time = time();
                $records_this_minute = 0;
            }
            
            logMessage("Processing batch of " . count($batch) . " records for data_id: $data_id");
            
            foreach ($batch as $record) {
                // Check if record is already in queue
                if (isRecordInQueue($conn, $data_id, $record['feedback_data'])) {
                    logMessage("Record ID: " . $record['id'] . " already in queue for data_id: $data_id");
                    continue;
                }
                
                try {
                    // Enqueue the record
                    $db->enqueueComment(
                        $record['feedback_data'],
                        $data_id,
                        $record['user_id'],
                        $record['csat'],
                        $record['nps'],
                        $record['pid']
                    );
                    
                    $processed++;
                    $records_this_minute++;
                    
                    logMessage("Enqueued record ID: " . $record['id'] . " for data_id: $data_id");
                } catch (Exception $e) {
                    logMessage("ERROR: Failed to enqueue record ID: " . $record['id'] . " - " . $e->getMessage());
                }
            }
            
            // Sleep between batches to reduce server load
            if ($config['sleep_between_batches'] > 0) {
                sleep($config['sleep_between_batches']);
            }
        }
        
        logMessage("Completed processing for data_id: $data_id - Enqueued $processed records");
    }
    
    logMessage("Record monitor process completed");
}

// Run the main function
try {
    processMissingRecords($db, $conn, $config);
} catch (Exception $e) {
    logMessage("CRITICAL ERROR: " . $e->getMessage());
    exit(1);
}
