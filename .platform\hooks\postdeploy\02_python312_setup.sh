#!/bin/bash
set -euxo pipefail

# Define the log file path
LOG_FILE="/var/log/eb-python-postdeploy.log"

# --- Ensure log file and directory exist and are writable ---
# This block runs unconditionally at the very start to guarantee logging infrastructure.
# 'mkdir -p' creates directory if it doesn't exist.
# 'touch' creates the file if it doesn't exist.
# 'chmod 666' ensures the file is writable by all for debugging purposes.
# '|| true' prevents script from exiting if these commands fail (e.g., if directory already exists).
mkdir -p "$(dirname "${LOG_FILE}")" || true
touch "${LOG_FILE}" || true
chmod 666 "${LOG_FILE}" || true
# -----------------------------------------------------------

# Redirect all subsequent output (stdout and stderr) of this script to the log file.
# This approach is more direct than using 'tee' and aims to ensure all output is captured.
exec >> "${LOG_FILE}" 2>&1

echo "[PYTHON POSTDEPLOY] Script execution started at $(date)"

echo "[PYTHON POSTDEPLOY] Starting Python 3.12 environment setup..."

# Install build dependencies, including sqlite-devel
# sqlite-devel is crucial for Python's built-in sqlite3 module to be compiled correctly.
# This resolves the "No module named '_sqlite3'" error.
# It's already installed, but keeping this line ensures it's checked/installed on new instances.
dnf install -y gcc gcc-c++ make wget bzip2-devel libffi-devel zlib-devel \
    xz-devel openssl-devel sqlite-devel

# Define Python executable path
PYTHON_BIN="/usr/local/bin/python3.12"

# Define a temporary file for Python import check output
# Using /var/log/ for consistency, ensuring it's in a known writable location.
SQLITE_CHECK_OUTPUT="/var/log/sqlite_check_output.txt"

# Robust check: Check if Python 3.12 is installed AND has _sqlite3 module
# This conditional ensures Python is only re-compiled if necessary (e.g., if _sqlite3 is missing).
# It's more robust by checking for a specific success string from the Python import.
SHOULD_COMPILE="false" # Default to false
if ! command -v "${PYTHON_BIN}" &>/dev/null; then
    # Python 3.12 not found, definitely need to compile
    SHOULD_COMPILE="true"
    echo "[PYTHON POSTDEPLOY] Python 3.12 not found. Proceeding with compilation."
else
    # Python 3.12 found, now check for _sqlite3 module
    echo "[PYTHON POSTDEPLOY] Checking if Python 3.12 has _sqlite3 module..."
    # Run a Python command to import _sqlite3 and print a unique success string
    # Ensure this command's output goes to the temp file.
    # '|| true' is added to the Python command to prevent 'set -e' from exiting the script
    # if the import fails, allowing us to capture the error output.
    "${PYTHON_BIN}" -c "import _sqlite3; print('SQLITE3_MODULE_OK')" > "${SQLITE_CHECK_OUTPUT}" 2>&1 || true
    
    # Check if the success string is in the output
    if grep -q "SQLITE3_MODULE_OK" "${SQLITE_CHECK_OUTPUT}"; then
        SHOULD_COMPILE="false"
        echo "[PYTHON POSTDEPLOY] Python 3.12 with _sqlite3 module already found. Skipping compilation."
    else
        SHOULD_COMPILE="true"
        echo "[PYTHON POSTDEPLOY] Python 3.12 found, but _sqlite3 module is missing or import failed. Proceeding with compilation."
        echo "--- Python _sqlite3 check output ---"
        cat "${SQLITE_CHECK_OUTPUT}"
        echo "-------------------------------------"
    fi
    # Clean up the temporary file
    rm -f "${SQLITE_CHECK_OUTPUT}"
fi

# Perform compilation if SHOULD_COMPILE is true
if [ "${SHOULD_COMPILE}" = "true" ]; then
    cd /usr/src
    # Clean up any previous partial downloads or extractions
    rm -rf Python-3.12.3 Python-3.12.3.tgz
    
    wget https://www.python.org/ftp/python/3.12.3/Python-3.12.3.tgz
    tar xzf Python-3.12.3.tgz
    cd Python-3.12.3

    # Clean previous build artifacts before configuring and compiling
    make clean || true # '|| true' prevents script from exiting if clean fails on first run

    # Configure Python with optimizations. This step checks for available libraries like sqlite-devel.
    ./configure --enable-optimizations

    # Compile Python using all available CPU cores for speed
    make -j "$(nproc)"

    # Install Python. 'altinstall' is used to prevent overwriting default system python.
    make altinstall
    echo "[PYTHON POSTDEPLOY] Python 3.12 compilation and installation complete."
else
    echo "[PYTHON POSTDEPLOY] Skipping Python 3.12 compilation as it's already correctly installed."
fi

# Create a symbolic link for convenience so 'python3.12' can be called from /usr/bin.
# This is done unconditionally to ensure the symlink is always correct.
ln -sf "${PYTHON_BIN}" /usr/bin/python3.12

echo "[PYTHON POSTDEPLOY] Python 3.12 setup and symlinking complete."

# Locate requirements.txt file in various possible application subdirectories.
# This ensures flexibility in where the requirements file is placed within your bundle.
REQ_PATH=""
if [ -f /var/app/current/MASTER/requirements.txt ]; then
    REQ_PATH="/var/app/current/MASTER/requirements.txt"
elif [ -f /var/app/current/Master/requirements.txt ]; then
    REQ_PATH="/var/app/current/Master/requirements.txt"
elif [ -f /var/app/current/master/requirements.txt ]; then
    REQ_PATH="/var/app/current/master/requirements.txt"
elif [ -f /var/app/current/python/requirements.txt ]; then
    REQ_PATH="/var/app/current/python/requirements.txt"
elif [ -f /var/app/current/master/python/requirements.txt ]; then
    REQ_PATH="/var/app/current/master/python/requirements.txt"
elif [ -f /var/app/current/requirements.txt ]; then
    REQ_PATH="/var/app/current/requirements.txt"
else
    echo "[WARN] requirements.txt not found in known locations. Skipping pip install..."
    REQ_PATH=""
fi

# Ensure pip is installed and up-to-date for the newly installed Python 3.12.
# This is done unconditionally as pip might need updates even if Python itself isn't recompiled.
"${PYTHON_BIN}" -m ensurepip --upgrade

# Install Python dependencies listed in requirements.txt.
# Output is redirected to a separate log file for easier debugging of pip issues.
if [ -n "$REQ_PATH" ]; then
    echo "[PYTHON POSTDEPLOY] Installing dependencies from $REQ_PATH"
    "${PYTHON_BIN}" -m pip install --upgrade pip
    "${PYTHON_BIN}" -m pip install -r "$REQ_PATH" >> /var/log/pip-install-postdeploy.log 2>&1 || {
        echo "[ERROR] pip install failed. Check /var/log/pip-install-postdeploy.log" >&2
        tail -n 20 /var/log/pip-install-postdeploy.log # Show last 20 lines of pip log on error
        exit 1 # Exit with an error code if pip install fails
    }
    echo "[PYTHON POSTDEPLOY] Python dependencies installed successfully."
else
    echo "[PYTHON POSTDEPLOY] No requirements.txt path found. Skipping dependency install."
fi

# Fix shebang and make ols_analysis.py executable.
# This ensures the script can be run directly and uses the correct Python interpreter.
PY_FILES=(
    /var/app/current/python/ols_analysis.py
    /var/app/current/master/python/ols_analysis.py
    /var/app/current/MASTER/ols_analysis.py
)

for PY_FILE in "${PY_FILES[@]}"; do
    if [ -f "$PY_FILE" ]; then
        # Inject correct shebang (#!/usr/local/bin/python3.12) if not already present.
        # This tells the system which interpreter to use when the script is executed.
        if ! grep -q "^#!${PYTHON_BIN}" "$PY_FILE"; then
            sed -i "1s|^|#!${PYTHON_BIN}\n|" "$PY_FILE"
        fi
        # Make the script executable.
        chmod +x "$PY_FILE"
        echo "[INFO] Patched and made executable: $PY_FILE"
    fi
done

echo "[PYTHON POSTDEPLOY] Python environment setup completed successfully."