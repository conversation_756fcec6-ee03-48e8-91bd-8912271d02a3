<!DOCTYPE html>
<html>
<head>
    <title>Test ACPT Categorization</title>
    <script src="assets/js/enhanced_ai_summary_generator.js"></script>
</head>
<body>
    <h1>ACPT Categorization Test</h1>
    <div id="results"></div>

    <script>
        // Test data similar to what comes from the database
        const testData = [
            {
                main_driver: "Service Experience & Interaction",
                sub_driver: "Resolution Related",
                sentiment: "Negative",
                total_comments: 123,
                avg_csat: 2.5,
                avg_nps: 2.0,
                vendor: "Vendor A",
                location: "Location 1",
                lob: "Banking",
                partner: "Partner 1",
                team: "Team A",
                painpointscustomerfrustrations: "Agent was not helpful and didn't resolve my issue"
            },
            {
                main_driver: "Billing & Payment",
                sub_driver: "Payment Issues",
                sentiment: "Negative",
                total_comments: 73,
                avg_csat: 2.2,
                avg_nps: 1.8,
                vendor: "Vendor B",
                location: "Location 2",
                lob: "Insurance",
                partner: "Partner 2",
                team: "Team B",
                painpointscustomerfrustrations: "Payment process is too complicated and slow"
            },
            {
                main_driver: "Tools & Technology",
                sub_driver: "Technical Failures",
                sentiment: "Negative",
                total_comments: 45,
                avg_csat: 2.8,
                avg_nps: 2.5,
                vendor: "Vendor C",
                location: "Location 3",
                lob: "Telecom",
                partner: "Partner 3",
                team: "Team C",
                painpointscustomerfrustrations: "App keeps crashing and website is slow"
            },
            {
                main_driver: "Account Access Activities",
                sub_driver: "Verification Issues",
                sentiment: "Negative",
                total_comments: 29,
                avg_csat: 3.1,
                avg_nps: 2.9,
                vendor: "Vendor D",
                location: "Location 4",
                lob: "Retail",
                partner: "Partner 4",
                team: "Team D",
                painpointscustomerfrustrations: "Could not verify my account with OTP"
            }
        ];

        // Test ACPT categorization
        console.log('Testing ACPT Categorization...');
        const acptResult = categorizeIntoACPT(testData);
        console.log('ACPT Categorization Result:', acptResult);

        // Test table generation
        const tableHTML = generateACPTTableRows(testData, null);
        console.log('ACPT Table HTML:', tableHTML);

        const subDriverHTML = generateACPTSubDriverTableRows(testData, null);
        console.log('Sub-driver Table HTML:', subDriverHTML);

        // Test dimensional analysis
        const lobAnalysis = getLOBAnalysis(testData);
        const locationAnalysis = getLocationAnalysis(testData);
        const partnerAnalysis = getPartnerAnalysis(testData);
        const teamAnalysis = getTeamAnalysis(testData);

        console.log('LOB Analysis:', lobAnalysis);
        console.log('Location Analysis:', locationAnalysis);
        console.log('Partner Analysis:', partnerAnalysis);
        console.log('Team Analysis:', teamAnalysis);

        // Display results
        document.getElementById('results').innerHTML = `
            <h2>ACPT Categorization Results:</h2>
            <pre>${JSON.stringify(acptResult, null, 2)}</pre>
            
            <h2>ACPT Table HTML:</h2>
            <table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
                <tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
                ${tableHTML}
            </table>
            
            <h2>Sub-driver Table HTML:</h2>
            <table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
                <tr><th>ACPT</th><th>L1 Category</th><th>Sub-driver</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
                ${subDriverHTML}
            </table>
            
            <h2>Dimensional Analysis:</h2>
            <p><strong>LOB Analysis:</strong> ${lobAnalysis}</p>
            <p><strong>Location Analysis:</strong> ${locationAnalysis}</p>
            <p><strong>Partner Analysis:</strong> ${partnerAnalysis}</p>
            <p><strong>Team Analysis:</strong> ${teamAnalysis}</p>
        `;
    </script>
</body>
</html>
