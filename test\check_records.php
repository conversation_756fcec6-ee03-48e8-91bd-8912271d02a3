<?php
/**
 * <PERSON>ript to check if specific records exist in analyzed_comments
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to check
$data_id = '682b54974bab6';

// Specific record IDs to check
$record_ids = [17413, 17474, 19665];

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database\n";
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Counts - Feedback data: " . $counts['feedback_count'] . ", Analyzed comments: " . $counts['analyzed_count'] . "\n";
    
    // Check each record
    foreach ($record_ids as $record_id) {
        echo "\nChecking record ID: $record_id\n";
        
        // Get the record from feedback_data
        $feedbackQuery = "SELECT feedback_data FROM feedback_data WHERE id = :id AND data_id = :data_id";
        $feedbackStmt = $conn->prepare($feedbackQuery);
        $feedbackStmt->bindParam(':id', $record_id);
        $feedbackStmt->bindParam(':data_id', $data_id);
        $feedbackStmt->execute();
        $feedbackRecord = $feedbackStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$feedbackRecord) {
            echo "Record ID: $record_id not found in feedback_data\n";
            continue;
        }
        
        // Check if the record exists in analyzed_comments
        $analyzedQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id AND comment = :comment";
        $analyzedStmt = $conn->prepare($analyzedQuery);
        $analyzedStmt->bindParam(':data_id', $data_id);
        $analyzedStmt->bindParam(':comment', $feedbackRecord['feedback_data']);
        $analyzedStmt->execute();
        $analyzedCount = $analyzedStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($analyzedCount > 0) {
            echo "Record ID: $record_id EXISTS in analyzed_comments\n";
        } else {
            echo "Record ID: $record_id DOES NOT EXIST in analyzed_comments\n";
        }
    }
    
    echo "\nCheck completed\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
