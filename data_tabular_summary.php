<?php
// data_tabular_summary.php
// Endpoint to generate a tabular summary from analyzed_comments based on filters

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    // Debug: Log incoming POST data and filters
    file_put_contents('php_errors.log', "[TabularSummary] POST: " . json_encode($_POST) . "\n", FILE_APPEND);
    
    // Load environment and DB connection
    require_once 'config.php';
    require_once 'DatabaseInteraction.php';

    $db = new DatabaseInteraction();
    $pdo = $db->connect();

    // Start session to get user_id
    session_start();
    require_once 'filter_validation.php';

    // Get user_id from session
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'error' => 'User not authenticated.']);
        exit;
    }
    $user_id = $_SESSION['user_id'];

    $validator = new FilterValidation();
    // Collect filters from POST (map frontend names to DB fields as per documentation)
    $filters = [
        'domain_category' => $_POST['domain_category'] ?? null,
        'data_id' => $_POST['data_id'] ?? null,
        'sentiment' => $_POST['sentiment'] ?? null,
        'start_date' => $_POST['start_date'] ?? null,
        'end_date' => $_POST['end_date'] ?? null,
        'partner' => $_POST['product_type'] ?? ($_POST['partner'] ?? null), // product_type maps to partner
        'lob' => $_POST['channel_type'] ?? ($_POST['lob'] ?? null), // channel_type maps to lob
        'dummy_1' => $_POST['team'] ?? ($_POST['dummy_1'] ?? null), // team maps to dummy_1
        'dummy_5' => $_POST['resolution_status'] ?? ($_POST['dummy_5'] ?? null), // resolution_status maps to dummy_5
    ];
    file_put_contents('php_errors.log', "[TabularSummary] Filters: " . json_encode($filters) . "\n", FILE_APPEND);

    // Validate filter combination using FilterValidation
    if (!$validator->validateFilterCombination($user_id, $filters)) {
        echo json_encode(['success' => false, 'error' => 'No data for selected filter combination.']);
        exit;
    }

    // Build SQL for tabular summary (use mapped filter fields)
    $sql = "SELECT 
      main_driver,
      sub_driver,
      sentiment,
      domain_category,
      lob,
      vendor,
      location,
      partner,
      dummy_1 as team,
      dummy_5 as resolution_status,
      painpointscustomerfrustrations,
      detailedexplanationofthecomment,
      suggestionsforimprovement,
      COUNT(*) as total_comments,
      AVG(csat) as avg_csat,
      AVG(nps) as avg_nps
    FROM analyzed_comments
    WHERE user_id = :user_id";
    $params = [':user_id' => $user_id];

    // Use FilterValidation's buildFilterConditions for consistency
    $filterConditions = $validator->buildFilterConditions($filters, $params);
    $sql .= $filterConditions;

    $sql .= " GROUP BY main_driver, sub_driver, sentiment, domain_category, lob, vendor, location, partner, dummy_1, dummy_5, painpointscustomerfrustrations, detailedexplanationofthecomment, suggestionsforimprovement";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $tabularSummary = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'data' => $tabularSummary]);
} catch (Throwable $e) {
    // Log the error for debugging
    file_put_contents('php_errors.log', "[TabularSummary][ERROR] " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    // Always return valid JSON
    echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
    exit;
} 