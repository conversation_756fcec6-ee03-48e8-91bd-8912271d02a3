<?php
// data_tabular_summary.php
// Endpoint to generate a tabular summary from analyzed_comments based on filters

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    // Debug: Log incoming POST data and filters
    file_put_contents('php_errors.log', "[TabularSummary] POST: " . json_encode($_POST) . "\n", FILE_APPEND);
    
    // Load environment and DB connection
    require_once 'config.php';
    require_once 'DatabaseInteraction.php';

    $db = new DatabaseInteraction();
    $pdo = $db->connect();

    // Start session to get user_id
    session_start();
    require_once 'filter_validation.php';

    // Get user_id from session
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'error' => 'User not authenticated.']);
        exit;
    }
    $user_id = $_SESSION['user_id'];

    $validator = new FilterValidation();
    // Collect filters from POST (map frontend names to DB fields as per documentation)
    $filters = [
        'domain_category' => $_POST['domain_category'] ?? null,
        'data_id' => $_POST['data_id'] ?? null,
        'sentiment' => $_POST['sentiment'] ?? null,
        'start_date' => $_POST['start_date'] ?? null,
        'end_date' => $_POST['end_date'] ?? null,
        'partner' => $_POST['product_type'] ?? ($_POST['partner'] ?? null), // product_type maps to partner
        'lob' => $_POST['channel_type'] ?? ($_POST['lob'] ?? null), // channel_type maps to lob
        'dummy_1' => $_POST['team'] ?? ($_POST['dummy_1'] ?? null), // team maps to dummy_1
        'dummy_5' => $_POST['resolution_status'] ?? ($_POST['dummy_5'] ?? null), // resolution_status maps to dummy_5
    ];
    file_put_contents('php_errors.log', "[TabularSummary] Filters: " . json_encode($filters) . "\n", FILE_APPEND);

    // Validate filter combination using FilterValidation
    if (!$validator->validateFilterCombination($user_id, $filters)) {
        echo json_encode(['success' => false, 'error' => 'No data for selected filter combination.']);
        exit;
    }

    // Build SQL for enhanced tabular summary with meaningful aggregations
    $sql = "SELECT
      main_driver,
      sub_driver,
      sentiment,
      domain_category,
      lob,
      vendor,
      location,
      partner,
      dummy_1 as team,
      dummy_5 as resolution_status,
      painpointscustomerfrustrations as pain_points,
      suggestionsforimprovement as suggestions,
      COUNT(*) as total_comments,
      ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as avg_csat,
      ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as avg_nps,
      ROUND(MIN(CAST(csat AS DECIMAL(5,2))), 2) as min_csat,
      ROUND(MAX(CAST(csat AS DECIMAL(5,2))), 2) as max_csat,
      ROUND(MIN(CAST(nps AS DECIMAL(5,2))), 2) as min_nps,
      ROUND(MAX(CAST(nps AS DECIMAL(5,2))), 2) as max_nps,
      COUNT(CASE WHEN sentiment = 'Positive' THEN 1 END) as positive_count,
      COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as negative_count,
      COUNT(CASE WHEN sentiment = 'Neutral' THEN 1 END) as neutral_count,
      COUNT(CASE WHEN dummy_5 = 'Resolved' OR dummy_5 = 'Yes' THEN 1 END) as resolved_count
    FROM analyzed_comments
    WHERE user_id = :user_id";
    $params = [':user_id' => $user_id];

    // Use FilterValidation's buildFilterConditions for consistency
    $filterConditions = $validator->buildFilterConditions($filters, $params);
    $sql .= $filterConditions;

    $sql .= " GROUP BY main_driver, sub_driver, sentiment, domain_category, lob, vendor, location, partner, dummy_1, dummy_5, painpointscustomerfrustrations, suggestionsforimprovement
              ORDER BY total_comments DESC, avg_csat ASC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $tabularSummary = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add additional summary statistics for better AI analysis
    // Overall statistics
    $overallSql = "SELECT
        COUNT(*) as total_records,
        ROUND(AVG(csat), 2) as overall_avg_csat,
        ROUND(AVG(nps), 2) as overall_avg_nps,
        COUNT(DISTINCT main_driver) as unique_drivers,
        COUNT(DISTINCT vendor) as unique_vendors,
        COUNT(DISTINCT location) as unique_locations,
        COUNT(CASE WHEN sentiment = 'Positive' THEN 1 END) as total_positive,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as total_negative,
        COUNT(CASE WHEN sentiment = 'Neutral' THEN 1 END) as total_neutral,
        ROUND((COUNT(CASE WHEN sentiment = 'Positive' THEN 1 END) * 100.0 / COUNT(*)), 2) as positive_percentage,
        ROUND((COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) * 100.0 / COUNT(*)), 2) as negative_percentage
    FROM analyzed_comments
    WHERE user_id = :user_id" . $filterConditions;

    $overallStmt = $pdo->prepare($overallSql);
    $overallStmt->execute($params);
    $overallStats = $overallStmt->fetch(PDO::FETCH_ASSOC);

    // Top issues by frequency
    $topIssuesSql = "SELECT
        main_driver,
        sub_driver,
        COUNT(*) as frequency,
        ROUND(AVG(csat), 2) as avg_csat,
        ROUND(AVG(nps), 2) as avg_nps,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as negative_count
    FROM analyzed_comments
    WHERE user_id = :user_id" . $filterConditions . "
    GROUP BY main_driver, sub_driver
    ORDER BY frequency DESC
    LIMIT 10";

    $topIssuesStmt = $pdo->prepare($topIssuesSql);
    $topIssuesStmt->execute($params);
    $topIssues = $topIssuesStmt->fetchAll(PDO::FETCH_ASSOC);

    // Performance by vendor
    $vendorPerformanceSql = "SELECT
        vendor,
        COUNT(*) as total_comments,
        ROUND(AVG(csat), 2) as avg_csat,
        ROUND(AVG(nps), 2) as avg_nps,
        COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) as negative_feedback,
        ROUND((COUNT(CASE WHEN sentiment = 'Negative' THEN 1 END) * 100.0 / COUNT(*)), 2) as negative_percentage
    FROM analyzed_comments
    WHERE user_id = :user_id" . $filterConditions . "
    GROUP BY vendor
    ORDER BY avg_csat ASC";

    $vendorStmt = $pdo->prepare($vendorPerformanceSql);
    $vendorStmt->execute($params);
    $vendorPerformance = $vendorStmt->fetchAll(PDO::FETCH_ASSOC);

    $enhancedSummary = [
        'detailed_summary' => $tabularSummary,
        'overall_statistics' => $overallStats,
        'top_issues' => $topIssues,
        'vendor_performance' => $vendorPerformance
    ];

    echo json_encode(['success' => true, 'data' => $enhancedSummary]);
} catch (Throwable $e) {
    // Log the error for debugging
    file_put_contents('php_errors.log', "[TabularSummary][ERROR] " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    // Always return valid JSON
    echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
    exit;
} 