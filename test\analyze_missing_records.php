<?php
/**
 * <PERSON>ript to analyze missing records for data_id 682b54974bab6
 * This script will examine the specific characteristics of missing records
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to analyze
$data_id = '682b54974bab6';

// Function to log messages
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
    // Force output buffer flush
    if (ob_get_level() > 0) {
        ob_flush();
    }
    flush();
}

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    // Get all feedback data records
    log_message("Retrieving all feedback data records...");
    $allFeedbackQuery = "SELECT id, feedback_data, LENGTH(feedback_data) as data_length FROM feedback_data WHERE data_id = :data_id";
    $allFeedbackStmt = $conn->prepare($allFeedbackQuery);
    $allFeedbackStmt->bindParam(':data_id', $data_id);
    $allFeedbackStmt->execute();
    $allFeedback = $allFeedbackStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Retrieved " . count($allFeedback) . " feedback records");
    
    // Get all analyzed comments
    log_message("Retrieving all analyzed comments...");
    $allAnalyzedQuery = "SELECT id, comment, LENGTH(comment) as comment_length FROM analyzed_comments WHERE data_id = :data_id";
    $allAnalyzedStmt = $conn->prepare($allAnalyzedQuery);
    $allAnalyzedStmt->bindParam(':data_id', $data_id);
    $allAnalyzedStmt->execute();
    $allAnalyzed = $allAnalyzedStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Retrieved " . count($allAnalyzed) . " analyzed comments");
    
    // Extract just the comments for easier comparison
    $analyzedComments = array_column($allAnalyzed, 'comment');
    
    // Find missing records
    log_message("Identifying missing records...");
    $missingRecords = [];
    $longRecords = 0;
    $specialCharRecords = 0;
    $nullRecords = 0;
    
    foreach ($allFeedback as $record) {
        if (!in_array($record['feedback_data'], $analyzedComments)) {
            $missingRecords[] = $record;
            
            // Check for potential issues
            if ($record['data_length'] > 1000) {
                $longRecords++;
            }
            
            if ($record['feedback_data'] === null) {
                $nullRecords++;
            } else if (preg_match('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', $record['feedback_data'])) {
                $specialCharRecords++;
            }
        }
    }
    
    log_message("Found " . count($missingRecords) . " missing records");
    log_message("Long records (>1000 chars): $longRecords");
    log_message("Records with special characters: $specialCharRecords");
    log_message("Records with NULL values: $nullRecords");
    
    // Sample some missing records
    if (count($missingRecords) > 0) {
        log_message("\nSample of missing records:");
        $sampleSize = min(5, count($missingRecords));
        
        for ($i = 0; $i < $sampleSize; $i++) {
            $record = $missingRecords[$i];
            log_message("Record ID: " . $record['id'] . ", Length: " . $record['data_length']);
            
            // Truncate long feedback data for display
            $truncatedData = substr($record['feedback_data'], 0, 100);
            if (strlen($record['feedback_data']) > 100) {
                $truncatedData .= "... [truncated]";
            }
            
            log_message("Data: " . $truncatedData);
            
            // Try to insert this record directly
            try {
                $insertQuery = "INSERT INTO analyzed_comments (comment, data_id, user_id, main_driver, sub_driver, sentiment) 
                               VALUES (:comment, :data_id, 1, 'Auto-Generated', 'Auto-Generated', 'Neutral')";
                
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bindParam(':comment', $record['feedback_data']);
                $insertStmt->bindParam(':data_id', $data_id);
                
                $result = $insertStmt->execute();
                
                if ($result) {
                    log_message("Successfully inserted record ID: " . $record['id'] . " directly into analyzed_comments");
                } else {
                    log_message("Failed to insert record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
                }
            } catch (PDOException $e) {
                log_message("ERROR: Failed to insert record ID: " . $record['id'] . " - " . $e->getMessage());
            }
            
            log_message("---");
        }
    }
    
    // Check for duplicate records in feedback_data
    log_message("\nChecking for duplicates in feedback_data...");
    $duplicateQuery = "
        SELECT feedback_data, COUNT(*) as count
        FROM feedback_data
        WHERE data_id = :data_id
        GROUP BY feedback_data
        HAVING COUNT(*) > 1
    ";
    $duplicateStmt = $conn->prepare($duplicateQuery);
    $duplicateStmt->bindParam(':data_id', $data_id);
    $duplicateStmt->execute();
    $duplicates = $duplicateStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Found " . count($duplicates) . " duplicate feedback entries");
    
    // Check for records in comment_queue
    log_message("\nChecking for records in comment_queue...");
    $queueQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = :data_id";
    $queueStmt = $conn->prepare($queueQuery);
    $queueStmt->bindParam(':data_id', $data_id);
    $queueStmt->execute();
    $queueCount = $queueStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    log_message("Found $queueCount records in comment_queue for data_id $data_id");
    
    log_message("\nAnalysis completed");
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
