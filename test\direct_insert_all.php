<?php
/**
 * <PERSON><PERSON><PERSON> to directly insert all missing records into analyzed_comments
 * This bypasses the comment_queue and inserts records directly
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682b54974bab6';

// Function to log messages
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
}

// Connect to database
try {
    log_message("Starting direct insertion for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    if ($missing_count <= 0) {
        log_message("No missing records for data_id: $data_id");
        exit(0);
    }
    
    // Find all feedback records that don't have a corresponding analyzed comment
    // This approach uses a NOT EXISTS subquery which is more efficient for large datasets
    $missingQuery = "
        SELECT fd.*
        FROM feedback_data fd
        WHERE fd.data_id = :data_id
        AND NOT EXISTS (
            SELECT 1
            FROM analyzed_comments ac
            WHERE ac.data_id = fd.data_id
            AND ac.comment = fd.feedback_data
        )
        LIMIT 2000
    ";
    
    $missingStmt = $conn->prepare($missingQuery);
    $missingStmt->bindParam(':data_id', $data_id);
    $missingStmt->execute();
    $missingRecords = $missingStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Found " . count($missingRecords) . " missing records");
    
    if (count($missingRecords) == 0) {
        log_message("No missing records found to process");
        exit(0);
    }
    
    // Process missing records in batches
    $batchSize = 100;
    $totalRecords = count($missingRecords);
    $processed = 0;
    $failed = 0;
    
    log_message("Processing $totalRecords records in batches of $batchSize");
    
    for ($i = 0; $i < $totalRecords; $i += $batchSize) {
        $batch = array_slice($missingRecords, $i, $batchSize);
        log_message("Processing batch " . (floor($i / $batchSize) + 1) . " of " . ceil($totalRecords / $batchSize));
        
        foreach ($batch as $record) {
            try {
                // Insert directly into analyzed_comments
                $insertQuery = "INSERT INTO analyzed_comments (
                    comment, data_id, user_id, csat, nps, pid,
                    main_driver, sub_driver, sentiment
                ) VALUES (
                    :comment, :data_id, :user_id, :csat, :nps, :pid,
                    'Auto-Generated', 'Auto-Generated', 'Neutral'
                )";
                
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bindParam(':comment', $record['feedback_data']);
                $insertStmt->bindParam(':data_id', $record['data_id']);
                $insertStmt->bindParam(':user_id', $record['user_id']);
                $insertStmt->bindParam(':csat', $record['csat']);
                $insertStmt->bindParam(':nps', $record['nps']);
                $insertStmt->bindParam(':pid', $record['pid']);
                
                $result = $insertStmt->execute();
                
                if ($result) {
                    $processed++;
                    if ($processed % 10 == 0) {
                        log_message("Processed $processed records so far");
                    }
                } else {
                    $failed++;
                    log_message("Failed to insert record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
                }
            } catch (PDOException $e) {
                $failed++;
                // Only log unique constraint violations in detail
                if ($e->getCode() == '23000') {
                    log_message("Duplicate entry for record ID: " . $record['id']);
                } else {
                    log_message("ERROR: Failed to insert record ID: " . $record['id'] . " - " . $e->getMessage());
                }
            }
        }
        
        // Sleep briefly between batches to avoid overloading the database
        if ($i + $batchSize < $totalRecords) {
            log_message("Sleeping between batches...");
            sleep(1);
        }
    }
    
    log_message("Processing completed - Processed: $processed, Failed: $failed");
    
    // Get updated counts
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Updated counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
