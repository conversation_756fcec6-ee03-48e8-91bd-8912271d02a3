openapi: 3.0.2
info:
  title: Weather API
  version: 0.0.1
  contact:
    name: <PERSON>
    url: https://github.com/thephpleague/openapi-psr7-validator
    email: <EMAIL>

paths:

  /users/{id}:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          pattern: ^\w+$
    get:
      summary: Read data
      operationId: read
      responses:
        200:
          description: good read
          content:
            text/plain:
              schema:
                type: string


  /users/{group}:
    parameters:
      - name: group
        in: path
        required: true
        schema:
          type: integer
    get:
      summary: Read data
      operationId: read
      responses:
        200:
          description: good read
          content:
            text/plain:
              schema:
                type: string
