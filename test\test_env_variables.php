<?php
/**
 * test/test_env_variables.php
 * Test script to verify environment variables are loaded correctly
 */

// Load the same configuration as your main app
require_once __DIR__ . '/../config.php';

echo "<h1>Environment Variables Test</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

echo "<div class='test-section'>";
echo "<h2>Environment Variables Check</h2>";

$env_vars = [
    'DB_HOST_ALT' => 'Database host',
    'DB_NAME_ALT' => 'Database name', 
    'DB_USER_ALT' => 'Database user',
    'DB_PASS_ALT' => 'Database password',
    'OPENAI_API_KEY' => 'OpenAI API key'
];

$all_set = true;

foreach ($env_vars as $var => $description) {
    $value = $_ENV[$var] ?? getenv($var);
    if ($value) {
        if (strpos($var, 'PASS') !== false || strpos($var, 'KEY') !== false) {
            echo "<div class='success'>✅ $description: " . str_repeat('*', strlen($value)) . "</div>";
        } else {
            echo "<div class='success'>✅ $description: $value</div>";
        }
    } else {
        echo "<div class='error'>❌ $description: Not set</div>";
        $all_set = false;
    }
}

if ($all_set) {
    echo "<div class='success'>✅ All environment variables are properly set!</div>";
} else {
    echo "<div class='error'>❌ Some environment variables are missing</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Database Connection Test</h2>";

try {
    $host = $_ENV['DB_HOST_ALT'] ?? getenv('DB_HOST_ALT');
    $dbname = $_ENV['DB_NAME_ALT'] ?? getenv('DB_NAME_ALT');
    $username = $_ENV['DB_USER_ALT'] ?? getenv('DB_USER_ALT');
    $password = $_ENV['DB_PASS_ALT'] ?? getenv('DB_PASS_ALT');
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ Database connection successful!</div>";
    echo "<div class='info'>Connected to: $host/$dbname</div>";
    
    // Test a simple query
    $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<div class='info'>Total records in analyzed_comments: " . $result['count'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Python Environment Variables Test</h2>";

// Test if Python can access the environment variables
$python_test = "import os; print('DB_HOST_ALT:', os.getenv('DB_HOST_ALT', 'Not set')); print('DB_NAME_ALT:', os.getenv('DB_NAME_ALT', 'Not set')); print('OPENAI_API_KEY:', 'Set' if os.getenv('OPENAI_API_KEY') else 'Not set')";

$output = [];
$return_var = 0;
exec("python -c \"$python_test\" 2>&1", $output, $return_var);

if ($return_var === 0) {
    echo "<div class='success'>✅ Python can access environment variables:</div>";
    echo "<pre>" . implode("\n", $output) . "</pre>";
} else {
    echo "<div class='error'>❌ Python cannot access environment variables:</div>";
    echo "<pre>" . implode("\n", $output) . "</pre>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Summary</h2>";
if ($all_set) {
    echo "<div class='success'>✅ Environment variables are properly configured for both PHP and Python!</div>";
} else {
    echo "<div class='error'>❌ Environment variables need to be configured</div>";
}
echo "</div>";
?> 