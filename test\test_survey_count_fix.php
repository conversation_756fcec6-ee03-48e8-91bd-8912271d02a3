<?php
/**
 * Test Script to Verify "Total Survey Assessed" Fix
 * This script tests both individual and batch API endpoints to ensure
 * the survey count is returned in the correct format
 */

// Include database configuration
require_once 'config.php';
require_once 'DatabaseInteraction.php';

// Start session to get user_id
session_start();

// HTML Header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Survey Count Fix Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; margin: 5px 0; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Survey Count Fix Verification</h1>
        <p><strong>Testing "Total Survey Assessed" display functionality</strong></p>
        
<?php

// Test parameters
$user_id = $_SESSION['user_id'] ?? 1;
$test_params = [
    'data_id' => 'all',
    'sentiment' => 'all', 
    'domain_category' => 'all'
];

echo "<div class='info'>";
echo "<h3>📋 Test Parameters</h3>";
echo "<p><strong>User ID:</strong> {$user_id}</p>";
echo "<p><strong>Data ID:</strong> {$test_params['data_id']}</p>";
echo "<p><strong>Sentiment:</strong> {$test_params['sentiment']}</p>";
echo "<p><strong>Domain Category:</strong> {$test_params['domain_category']}</p>";
echo "</div>";

// Test 1: Individual Comments API
echo "<div class='test-section'>";
echo "<h2>🧪 Test 1: Individual Comments API</h2>";

$individual_url = "data.php?type=comments&data_id={$test_params['data_id']}&sentiment={$test_params['sentiment']}&domain_category={$test_params['domain_category']}";
echo "<p><strong>URL:</strong> <code>{$individual_url}</code></p>";

try {
    $individual_response = file_get_contents($individual_url);
    $individual_data = json_decode($individual_response, true);
    
    echo "<h3>📤 Response:</h3>";
    echo "<pre>" . json_encode($individual_data, JSON_PRETTY_PRINT) . "</pre>";
    
    if (isset($individual_data['total']) && is_numeric($individual_data['total'])) {
        echo "<div class='success'>✅ Individual API: Correct format - {total: {$individual_data['total']}}</div>";
        $individual_count = $individual_data['total'];
    } else {
        echo "<div class='error'>❌ Individual API: Incorrect format or missing 'total' property</div>";
        $individual_count = 0;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Individual API Error: " . $e->getMessage() . "</div>";
    $individual_count = 0;
}
echo "</div>";

// Test 2: Batch API
echo "<div class='test-section'>";
echo "<h2>🧪 Test 2: Batch API (Dashboard-Batch)</h2>";

$batch_url = "data.php?type=dashboard-batch&data_id={$test_params['data_id']}&sentiment={$test_params['sentiment']}&domain_category={$test_params['domain_category']}";
echo "<p><strong>URL:</strong> <code>{$batch_url}</code></p>";

try {
    $batch_response = file_get_contents($batch_url);
    $batch_data = json_decode($batch_response, true);
    
    echo "<h3>📤 Comments Data from Batch Response:</h3>";
    if (isset($batch_data['commentsData'])) {
        echo "<pre>" . json_encode($batch_data['commentsData'], JSON_PRETTY_PRINT) . "</pre>";
        
        if (isset($batch_data['commentsData']['total']) && is_numeric($batch_data['commentsData']['total'])) {
            echo "<div class='success'>✅ Batch API: Correct format - {total: {$batch_data['commentsData']['total']}}</div>";
            $batch_count = $batch_data['commentsData']['total'];
        } else {
            echo "<div class='error'>❌ Batch API: Incorrect format or missing 'total' property</div>";
            $batch_count = 0;
        }
    } else {
        echo "<div class='error'>❌ Batch API: Missing 'commentsData' property</div>";
        $batch_count = 0;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Batch API Error: " . $e->getMessage() . "</div>";
    $batch_count = 0;
}
echo "</div>";

// Test 3: Data Consistency Check
echo "<div class='test-section'>";
echo "<h2>🧪 Test 3: Data Consistency Check</h2>";

if ($individual_count > 0 && $batch_count > 0) {
    if ($individual_count === $batch_count) {
        echo "<div class='success'>✅ Data Consistency: Both APIs return the same count ({$individual_count})</div>";
    } else {
        echo "<div class='error'>❌ Data Inconsistency: Individual API ({$individual_count}) ≠ Batch API ({$batch_count})</div>";
    }
} else {
    echo "<div class='error'>❌ Cannot verify consistency: One or both APIs failed</div>";
}
echo "</div>";

// Test 4: Direct Database Query
echo "<div class='test-section'>";
echo "<h2>🧪 Test 4: Direct Database Verification</h2>";

try {
    $db = new DatabaseInteraction();
    $direct_count = $db->getTotalComments(
        $test_params['data_id'] === 'all' ? null : $test_params['data_id'],
        $user_id,
        $test_params['sentiment'] === 'all' ? null : $test_params['sentiment'],
        $test_params['domain_category'] === 'all' ? null : $test_params['domain_category']
    );
    
    echo "<p><strong>Direct Database Query Result:</strong> {$direct_count}</p>";
    
    if ($direct_count === $individual_count && $direct_count === $batch_count) {
        echo "<div class='success'>✅ Database Consistency: All sources return the same count ({$direct_count})</div>";
    } else {
        echo "<div class='error'>❌ Database Inconsistency: Direct ({$direct_count}) vs Individual ({$individual_count}) vs Batch ({$batch_count})</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database Query Error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 5: Dashboard JavaScript Simulation
echo "<div class='test-section'>";
echo "<h2>🧪 Test 5: Dashboard JavaScript Simulation</h2>";

echo "<p>Simulating how the dashboard JavaScript processes the data:</p>";

if (isset($batch_data['commentsData'])) {
    echo "<pre>";
    echo "// JavaScript code in dashboard:\n";
    echo "updateBasicMetrics(\n";
    echo "    " . json_encode($batch_data['commentsData'], JSON_PRETTY_PRINT) . ",\n";
    echo "    sentimentsData,\n";
    echo "    historicalSentimentsData\n";
    echo ");\n\n";
    echo "// Inside updateBasicMetrics function:\n";
    echo "document.getElementById('totalcomments').textContent = commentsData.total || 0;\n";
    echo "// Result: " . ($batch_data['commentsData']['total'] ?? 0) . "\n";
    echo "</pre>";
    
    if (isset($batch_data['commentsData']['total'])) {
        echo "<div class='success'>✅ Dashboard Simulation: 'Total Survey Assessed' will display {$batch_data['commentsData']['total']}</div>";
    } else {
        echo "<div class='error'>❌ Dashboard Simulation: 'Total Survey Assessed' will display 0 (missing total property)</div>";
    }
} else {
    echo "<div class='error'>❌ Dashboard Simulation: Cannot simulate - missing commentsData</div>";
}
echo "</div>";

// Summary
echo "<div class='test-section'>";
echo "<h2>📊 Test Summary</h2>";

$tests_passed = 0;
$total_tests = 5;

// Check individual API
if (isset($individual_data['total'])) $tests_passed++;

// Check batch API  
if (isset($batch_data['commentsData']['total'])) $tests_passed++;

// Check consistency
if ($individual_count === $batch_count && $individual_count > 0) $tests_passed++;

// Check database consistency
if (isset($direct_count) && $direct_count === $individual_count) $tests_passed++;

// Check dashboard simulation
if (isset($batch_data['commentsData']['total'])) $tests_passed++;

$success_rate = ($tests_passed / $total_tests) * 100;

echo "<div class='info'>";
echo "<h3>🎯 Results</h3>";
echo "<p><strong>Tests Passed:</strong> {$tests_passed} / {$total_tests}</p>";
echo "<p><strong>Success Rate:</strong> " . number_format($success_rate, 1) . "%</p>";

if ($success_rate >= 80) {
    echo "<div class='success'>";
    echo "<h4>🎉 Fix Successful!</h4>";
    echo "<p>The 'Total Survey Assessed' display should now work correctly in both dashboard.php and dashboard-genric2.php</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h4>❌ Fix Incomplete</h4>";
    echo "<p>There are still issues that need to be addressed for the 'Total Survey Assessed' display to work properly.</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>🔧 Next Steps</h3>";
echo "<div class='info'>";
echo "<ol>";
echo "<li><strong>Clear browser cache</strong> and test the dashboard</li>";
echo "<li><strong>Check dashboard.php</strong> - the 'Total Survey Assessed' should display the correct number</li>";
echo "<li><strong>Check dashboard-genric2.php</strong> - the 'Total Survey Assessed' should display the correct number</li>";
echo "<li><strong>Test filter changes</strong> - the count should update when filters are applied</li>";
echo "<li><strong>Verify performance</strong> - the optimizations should still provide fast loading</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

?>
    </div>
</body>
</html>
