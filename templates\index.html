<!-- Index file with File upload pop up message -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoC Voice of Customer Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: white;
            text-align: center;
            padding: 10px;
            padding-left: 40px;
            padding-right: 40px;
        }

        .container {
            background-color: #eef8d8;
            padding: 10px;
            border-radius: 10px;
            max-width: 1000px;
            margin: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        h1 {
            font-size: 24px;
            font-weight: bold;
        }

        .upload-box {
            border: 2px dashed #4d4d4d;
            padding: 20px;
            margin: 20px 100px;
            border-radius: 5px;
            background-color: white;
            cursor: pointer;
        }

        .upload-box p {
            margin: 0;
            color: #555;
        }

        .upload-btn {
            background-color: #5b9c34;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .upload-btn:hover {
            background-color: #4a8328;
        }

        input[type="file"] {
            display: none;
        }

        .features {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            background-color: #eef8d8;
            padding: 10px;
            border-radius: 10px;
        }

        .feature {
            text-align: center;
            max-width: 300px;
        }

        .feature img {
            width: 50px;
            height: 50px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>(VoC) Voice of Customer Tool</h1>
        <p>🚀 Get insight about the Voice of your Customer immediately</p>
        <p style="padding-left: 80px;padding-right: 80px;">
            Easily uncover the <b>root cause of your customer issues</b>, delve into their <b>underlying
                sentiment</b>,</br>
            harness these insights to <b>identify gaps in your business</b>, and <b>formulate actionable
                strategies</b></br> with
            our Voice of Customer Tool.
        </p>
        <p style="color: #555;">Upload your file here 👇</p>

        <form id="uploadForm" action="/upload" method="post" enctype="multipart/form-data">
            <div class="upload-box" onclick="document.getElementById('fileInput').click()">
                <p>📎 Attach file </p>
                <p id="fileName">There is nothing attached.</p>
                <input type="file" id="fileInput" name="file" accept=".xlsx" onchange="displayFileName()">
            </div>
            <button type="submit" class="upload-btn">Upload</button>
        </form>

        <div class="features">
            <div class="feature">
                <img src="{{url_for('static',filename='summary.png')}}" alt="Sentiment Analysis">
                <p>Get comprehensive overview about all of your customers sentiment.</p>
            </div>
            <div class="feature">
                <img src="{{url_for('static',filename='ai.png')}}" alt="AI Analysis">
                <p>Identify the root causes of the negative sentiment of your customers immediately.</p>
            </div>
            <div class="feature">
                <img src="{{url_for('static',filename='idea.png')}}" alt="Insights">
                <p>Get actionable insights in just one click and delve deeper into what causes poor customer experience.
                </p>
            </div>
        </div>
    </div>
    <script>
        function displayFileName() {
            const fileInput = document.getElementById('fileInput');
            const fileNameDisplay = document.getElementById('fileName');
            if (fileInput.files.length > 0) {
                fileNameDisplay.textContent = fileInput.files[0].name;
            } else {
                fileNameDisplay.textContent = "There is nothing attached.";
            }
        }

        // document.getElementById("uploadForm").addEventListener("submit", function(event) {
        //     event.preventDefault();
        //     const formData = new FormData(this);
        //     fetch("/upload", {
        //         method: "POST",
        //         body: formData
        //     })
        //     .then(response => response.text())
        //     .then(data => {
        //         alert(data);
        //     })
        //     .catch(error => {
        //         alert("Error uploading file");
        //     });
        // });

        document.getElementById("uploadForm").addEventListener("submit", function (event) {
            event.preventDefault();  // Prevent normal form submission
            const formData = new FormData(this);

            fetch("/upload", {
                method: "POST",
                body: formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(data => {
                    // You might want to handle this differently, e.g., redirect or update the page content
                    console.log(data); // Log data or handle it as needed
                    window.location.href = '/summary'; // Redirect if the upload is successful
                })
                .catch(error => {
                    console.error('Error during fetch operation:', error);
                    alert("Error uploading file: " + error.message);
                });
        });

    </script>
</body>

</html>