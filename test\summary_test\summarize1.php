<?php
// Enable error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session securely
session_start();
session_regenerate_id(true);

// Redirect to login if username session not set
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Validate CSRF token if form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token'])) {
    die("CSRF token validation failed.");
}

require_once 'DatabaseInteraction.php';
require_once 'vendor/autoload.php';
require_once __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';

// Load environment variables securely
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

$db = new DatabaseInteraction();
$conn = $db->connect();

// Validate data_id parameter
if (!isset($_GET['data_id'])) {
    die("Error: No data_id provided.");
}

$data_id = $_GET['data_id'];
$feedback_data = $db->getFeedbackDataByDataId($data_id);

if (!$feedback_data) {
    die("Error: Feedback data not found.");
}

// Retrieve user_id associated with the data_id
$user_id = $feedback_data[0]['user_id'];

// Use all records for summary generation
$all_feedback = $feedback_data;
$text_to_analyze = implode("\n", array_column($all_feedback, 'feedback_data'));

// Get the total count of feedback records
$total_feedback_count = count($feedback_data);

// Keep first 10 records for display purposes
$limited_feedback = array_slice($feedback_data, 0, 10);

// Get domain category from feedback_data with default
$domain_category = !empty($feedback_data[0]['domain_category']) ?
    htmlspecialchars($feedback_data[0]['domain_category']) :
    'Collections';

$_SESSION['domain_cat'] = $domain_category;

// Generate Summary
$summary = summarize_feedback($text_to_analyze, $domain_category, $total_feedback_count);

// Save summary to database
save_summary_to_database($data_id, $summary, $domain_category, $user_id);

// Format Summary
$formatted_summary = format_summary($summary);

// Enqueue comments for processing if they haven't been analyzed
foreach ($feedback_data as $row) {
    $comment = $row['feedback_data'];
    $csat = $row['csat'];
    $nps = $row['nps'];
    $pid = $row['pid'];

    if (!$db->isCommentAnalyzed($comment, $data_id)) {
        $db->enqueueComment($comment, $data_id, $user_id, $csat, $nps, $pid);
    }
}

// Check if the user wants to export the PDF
if (isset($_GET['export_pdf'])) {
    generate_pdf($data_id, $domain_category, $summary, $limited_feedback, $total_feedback_count);
    exit();
}

// Generate CSRF token for forms
$_SESSION['csrf_token'] = bin2hex(random_bytes(32));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Analysis</title>
    <link rel="icon" href="assets/images/hricon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        nav {
            background-color: #b98b04;
            color: white;
            padding: 5px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .small-list {
            font-size: 12px;
        }
        .small-list li {
            font-size: 12px;
        }
        .summary-container img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navbar -->
    <nav>
        <div class="logo">
            <img src="assets/images/logo.png" alt="Logo" style="max-height: 60px; width: auto; display: block;">
        </div>
        <div class="ml-10 flex items-baseline space-x-4 nav-right">
            <span class="text-white">Hello, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
            <a href="upload.php" class="text-white hover:text-indigo-300">Upload Feedback</a>
            <a href="logout.php" class="text-white hover:text-indigo-300">Logout</a>
        </div>
    </nav>

    <div class="container mx-auto flex items-center justify-center min-h-screen p-2 py-2">
        <div class="bg-yellow-100 p-2 rounded-lg shadow-md max-w-8xl mx-auto border border-black">
            <h1 class="text-3xl font-bold text-gray-800 text-center mb-6">📊 Feedback Analysis</h1>

            <h3 class="text-lg font-semibold text-gray-700">📂 Data ID: <span class="text-indigo-600"><?php echo htmlspecialchars($data_id); ?></span></h3>

            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-700">📂 Domain Category: <span class="text-xl font-medium text-indigo-600"><?php echo $domain_category; ?></span></h3>
                <h3 class="text-lg font-semibold text-gray-700 mt-2">📊 Total Comments: <span class="text-xl font-medium text-indigo-600"><?php echo $total_feedback_count; ?></span></h3>

                <?php
                // Get and display the selected main drivers
                $selected_main_drivers = $db->getDataMainDrivers($data_id);
                if (!empty($selected_main_drivers)):
                ?>
                <h3 class="text-lg font-semibold text-gray-700 mt-2">🔍 Selected Primary Issues (L1):</h3>
                <ul class="list-disc pl-6 text-indigo-600" style="margin:0; padding-left:20px; line-height:1;">
                    <?php foreach ($selected_main_drivers as $driver): ?>
                        <li style="margin:0; padding:0; line-height:1;"><?php echo htmlspecialchars($driver); ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>
            </div>

            <div class="bg-yellow-50 p-2 rounded-lg shadow-md max-w-8xl mx-auto border border-black">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">📝 Overall Summary:</h3>
                <div class="summary-container"><?php echo $formatted_summary; ?></div>
            </div>

            <br>

            <div class="bg-yellow-50 p-2 rounded-lg shadow-md border border-black">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">💬 Sample Comments (Showing First 10 of <?php echo $total_feedback_count; ?>):</h3>
                <ul class="list-disc pl-6 space-y-1 text-gray-600">
                    <?php foreach ($limited_feedback as $row): ?>
                        <li class="small-list border-l-4 border-indigo-600 pl-3 py-1"><?php echo htmlspecialchars($row['feedback_data']); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="window.location.href='dashboard.php'" class="bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-indigo-700 transition">
                    🔙 Dashboard
                </button>
                <button onclick="window.location.href='?data_id=<?php echo urlencode($data_id); ?>&export_pdf=1'" class="bg-green-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-green-700 transition">
                    📄 Export as PDF
                </button>
            </div>
        </div>
    </div>

    <footer class="bg-yellow-300 py-2 shadow-md mt-auto">
        <div class="container mx-auto px-9 flex justify-between items-center">
            <div>
                <p class="text-gray-700 text-xs"><b>&#169 <?php echo date("Y"); ?> Bill Gosling Outsourcing. All rights reserved.</b></p>
                <p class="text-gray-700 text-xs">This system is for the use of authorized personnel only and by accessing this system you hereby consent to the system being monitored by the Company. Any unauthorized use will be considered a breach of the Company's Information Security policies and may also be unlawful under law. Bill Gosling Outsourcing reserves the right to take any action including disciplinary action or legal proceedings in a court of law against persons involved in the violation of the access restrictions herein. The Information Is 'Internal, Restricted'.</p>
            </div>
        </div>
    </footer>
</body>
</html>

<?php
function generate_pdf($data_id, $domain_category, $summary, $limited_feedback, $total_feedback_count = 0) {
    global $db;
    require_once __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';

    // Create new PDF document
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetTitle("Feedback Analysis Report - Data ID: $data_id");
    $pdf->SetHeaderData('', 0, "Feedback Analysis Report", "Data ID: $data_id");
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetAutoPageBreak(TRUE, 15);
    $pdf->AddPage();
    $pdf->SetFont('dejavusans', '', 10);

    // Fix special character issue
    $formatted_summary = nl2br(html_entity_decode($summary, ENT_QUOTES, 'UTF-8'));

    // PDF Content with inline styles
    $html = "
    <style>
        h1 { text-align: center; font-size: 16px; font-weight: bold; color: #2d3748; margin-bottom: 8px; }
        h3 { font-size: 13px; font-weight: bold; color: #0066cc; margin-bottom: 3px; margin-top: 5px; }
        p { font-size: 11px; color: #333; margin-bottom: 3px; margin-top: 3px; line-height: 1.3; }
        span { color: #0066cc; font-weight: bold; }
        ul { padding-left: 10px; font-size: 11px; margin-top: 3px; margin-bottom: 3px; }
        li { margin-bottom: 2px; padding-left: 3px; border-left: 2px solid #0066cc; padding-left: 5px; }
        .section { background-color: #f8f9fa; padding: 6px; border-radius: 5px; margin-bottom: 8px; border: 1px solid #ddd; }
        table { width: 100%; border-collapse: collapse; margin: 5px 0; font-size: 10px; background-color: #fffef0; }
        th, td { border: 1px solid #ccc; padding: 4px; text-align: left; }
        th { background-color: #e6e6e6; font-weight: bold; color: #333; border-bottom: 2px solid #ccc; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f0f0f0; }
        .bullet-item { margin: 2px 0; display: flex; }
        .bullet { color: #0066cc; margin-right: 4px; }
        .example-comment { color: #006600; font-style: italic; font-size: 9px; }
    </style>

    <div class='section'>
        <h1>Feedback Analysis Report</h1>
        <h3>Data ID: <span>$data_id</span></h3>
    </div>

    <div class='section'>
        <h3>Domain Category:</h3>
        <p><span>$domain_category</span></p>
    </div>

    <div class='section'>
        <h3>Total Comments:</h3>
        <p><span>$total_feedback_count</span></p>
    </div>";

    // Add selected main drivers to PDF if available
    $selected_main_drivers = $db->getDataMainDrivers($data_id);
    if (!empty($selected_main_drivers)) {
        $main_drivers_list = implode(", ", $selected_main_drivers);
        $html .= "
    <div class='section'>
        <h3>Selected Primary Issues (L1):</h3>
        <p><span>$main_drivers_list</span></p>
    </div>";
    }

    // Process the summary for PDF
    $pdf_summary = $summary;

    // Convert markdown tables to HTML tables
    $pattern = '/\|(.*?)\|\s*\n\|([-|\s]+)\|\s*\n((?:\|.*?\|\s*\n)+)/s';
    preg_match_all($pattern, $pdf_summary, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $original_table = $match[0];
        $header = $match[1];
        $headerCells = array_map('trim', explode('|', $header));

        $rows = trim($match[3]);
        $rowsArray = explode("\n", $rows);

        $html_table = '<table border="1" cellpadding="3" style="width:100%; border-collapse:collapse; margin:5px 0;">';

        // Add header
        $html_table .= '<tr style="background-color:#e6e6e6;">';
        foreach ($headerCells as $cell) {
            if (!empty(trim($cell))) {
                $html_table .= '<th style="text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;">' . trim($cell) . '</th>';
            }
        }
        $html_table .= '</tr>';

        // Add body
        foreach ($rowsArray as $row) {
            if (empty(trim($row))) continue;

            $html_table .= '<tr>';
            $cells = array_map('trim', explode('|', trim($row, '|')));

            foreach ($cells as $cell) {
                if (isset($cell)) {
                    if (preg_match('/^"(.*)"$/', trim($cell), $quoteMatch)) {
                        $html_table .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\"><span style=\"color:#006600; font-style:italic;\">\"{$quoteMatch[1]}\"</span></td>";
                    } else {
                        $html_table .= "<td style=\"text-align:left; border:1px solid #ccc; padding:4px; font-size:10px;\">" . trim($cell) . "</td>";
                    }
                }
            }
            $html_table .= '</tr>';
        }
        $html_table .= '</table>';

        // Replace the original table with the HTML version
        $pdf_summary = str_replace($original_table, $html_table, $pdf_summary);
    }

    // Clean up markdown formatting
    $pdf_summary = preg_replace('/#{1,6}\s+/', '', $pdf_summary);
    $pdf_summary = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $pdf_summary);
    $pdf_summary = preg_replace('/^\s*[-•*]\s+(.*?)(?=\n|$)/m', '• $1', $pdf_summary);
    $pdf_summary = nl2br($pdf_summary);

    $html .= "
    <div class='section'>
        <h3 style='color:#0066cc;'>ACPT Framework Analysis:</h3>
        <div>$pdf_summary</div>
    </div>

    <div class='section'>
        <h3 style='color:#0066cc;'>Sample Comments (Showing First 10 of $total_feedback_count):</h3>
        <ul>";

    // Add feedback comments to PDF
    foreach ($limited_feedback as $row) {
        $comment = html_entity_decode($row['feedback_data'], ENT_QUOTES, 'UTF-8');
        $html .= "<li>$comment</li>";
    }

    $html .= "</ul></div>";

    // Write HTML to PDF
    $pdf->writeHTML($html, true, false, true, false, '');
    $pdf->Output("Feedback_Analysis_$data_id.pdf", 'D');
    exit();
}

function summarize_feedback($text, $domain_category, $total_count = 0) {
    try {
        global $db, $data_id;

        // Get the user-selected main drivers
        $selected_main_drivers = $db->getDataMainDrivers($data_id);
        $main_drivers_list = !empty($selected_main_drivers) ? implode(", ", $selected_main_drivers) : "";

        // Parse the text into individual comments
        $comments = array_filter(explode("\n", $text));

        // Create the prompt with strict counting requirements
        $prompt = <<<PROMPT
**CRITICAL COUNTING REQUIREMENTS:**
1. Analyzing exactly {$total_count} comments
2. Every comment must be categorized in BOTH tables
3. Sum of ALL counts in EACH table MUST EQUAL {$total_count}
4. Verify counts match before responding
5. If counts don't match, adjust categorization

**Domain:** {$domain_category}
**Primary Issues (L1):** {$main_drivers_list}
**Total Comments:** {$total_count}

**Sample Comments (first 5):**
PROMPT;

        foreach (array_slice($comments, 0, 5) as $index => $comment) {
            $prompt .= "\n" . ($index + 1) . ". " . $comment;
        }

        $prompt .= <<<PROMPT

**ACPT Framework Definitions**:
- **Agent**: Concerns with associate behavior, communication, professionalism.
- **Customer**: Customer behavior, expectations, understanding, or misuse.
- **Process**: Failures or gaps in business process, policies, or flow.
- **Technology**: Issues with tools, systems, logins, outages, or performance.

**REQUIRED OUTPUT FORMAT:**

1. **Summary:** [2-3 paragraph overview]

2. **Top Pain Points or Praises:** [Bullet points]

3. **Key Takeaways:** [Actionable insights]

4. **ACPT Table:**
| ACPT Category | Issue Description | Count | Sample Comment |
|---------------|-------------------|-------|----------------|
| Agent         | Issue 1           | 15    | "Sample text"  |
| **TOTAL**     |                   | {$total_count} |                |

5. **ACPT-Aligned Sub Driver & Sentiment Table:**
| ACPT Category | L1 Category | Sub Driver (L2) | Sentiment | Count | Sample Comment |
|---------------|-------------|------------|-----------|-------|----------------|
| Agent         | Behavior    | Issue 1    | Negative  | 15    | "Sample text"  |
| **TOTAL**     |             |            |           | {$total_count} |                |

**VERIFICATION:**
1. Sum of ACPT Table = {$total_count}?
2. Sum of Aligned Table = {$total_count}?
3. Every comment accounted for?
4. If NO to any, REVISE CATEGORIZATION
PROMPT;

        // Log the API key status (masked for security)
        $apiKey = $_ENV['OPENAI_API_KEY'] ?? 'Not found';
        $maskedKey = !empty($apiKey) ? substr($apiKey, 0, 10) . '...' : 'Empty';
        error_log("OpenAI API Key status: " . $maskedKey);

        try {
            // Try with gpt-4o-mini first (more reliable and cost-effective)
            $client = OpenAI::client($_ENV['OPENAI_API_KEY']);

            error_log("Attempting to generate summary with gpt-4o-mini for data_id: $data_id");
            $response = $client->chat()->create([
                "model" => "gpt-4o-mini",
                "messages" => [
                    ["role" => "system", "content" => "You are an expert feedback analyst. You MUST:
1. Categorize EVERY comment in BOTH tables
2. Ensure sum of counts in EACH table equals EXACTLY {$total_count}
3. Include 'TOTAL' rows showing {$total_count}
4. Verify counts before responding"],
                    ["role" => "user", "content" => $prompt]
                ],
                "max_tokens" => 6000,
                "temperature" => 0.1
            ]);

            $result = trim($response->choices[0]->message->content);
            error_log("Successfully generated summary with gpt-4o-mini");
        } catch (\Exception $e) {
            // If gpt-4o-mini fails, fall back to gpt-3.5-turbo
            error_log("gpt-4o-mini failed with error: " . $e->getMessage() . ". Falling back to gpt-3.5-turbo");

            $client = OpenAI::client($_ENV['OPENAI_API_KEY']);
            $response = $client->chat()->create([
                "model" => "gpt-3.5-turbo",
                "messages" => [
                    ["role" => "system", "content" => "You are an expert feedback analyst. You MUST:
1. Categorize EVERY comment in BOTH tables
2. Ensure sum of counts in EACH table equals EXACTLY {$total_count}
3. Include 'TOTAL' rows showing {$total_count}
4. Verify counts before responding"],
                    ["role" => "user", "content" => $prompt]
                ],
                "max_tokens" => 4000,
                "temperature" => 0.1
            ]);

            $result = trim($response->choices[0]->message->content);
            error_log("Successfully generated summary with gpt-3.5-turbo");
        }

        // Validate the counts
        $validation = validate_table_counts_in_result($result, $total_count);

        if (!$validation['success']) {
            // Retry with stricter instructions if counts don't match
            error_log("Count validation failed. ACPT total: {$validation['acpt_total']}, Aligned total: {$validation['aligned_total']}, Expected: {$total_count}");

            $retry_prompt = "PREVIOUS ATTEMPT HAD INCORRECT COUNTS:\n";
            $retry_prompt .= "- ACPT Table sum was {$validation['acpt_total']} (should be {$total_count})\n";
            $retry_prompt .= "- Aligned Table sum was {$validation['aligned_total']} (should be {$total_count})\n\n";
            $retry_prompt .= "FIX THESE ISSUES BY:\n";
            $retry_prompt .= "1. Adjusting category counts\n";
            $retry_prompt .= "2. Adding 'Uncategorized' rows if needed\n";
            $retry_prompt .= "3. VERIFYING COUNTS BEFORE RESPONDING\n\n";
            $retry_prompt .= "Previous attempt:\n---\n{$result}\n---\n";

            try {
                $response = $client->chat()->create([
                    "model" => "gpt-3.5-turbo",
                    "messages" => [
                        ["role" => "system", "content" => "FIX THE COUNT DISCREPANCIES. You MUST:
1. Ensure BOTH tables sum to EXACTLY {$total_count}
2. Add 'Uncategorized' rows if needed
3. Verify counts match before responding"],
                        ["role" => "user", "content" => $retry_prompt]
                    ],
                    "max_tokens" => 4000,
                    "temperature" => 0.0
                ]);

                $result = trim($response->choices[0]->message->content);
                error_log("Successfully generated corrected summary");

                $final_validation = validate_table_counts_in_result($result, $total_count);
                if (!$final_validation['success']) {
                    error_log("Final validation failed. ACPT total: {$final_validation['acpt_total']}, Aligned total: {$final_validation['aligned_total']}, Expected: {$total_count}");
                    // Even if validation fails, we'll return the result rather than throwing an exception
                }
            } catch (\Exception $e) {
                error_log("Error during retry: " . $e->getMessage());
                // Return the original result if retry fails
            }
        }

        return $result;
    } catch (\Exception $e) {
        error_log("Critical error in summarize_feedback: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());
        return "Summary generation failed. Please try again. Error: " . $e->getMessage();
    }
}

function validate_table_counts_in_result($result, $total_count) {
    $validation = [
        'success' => true,
        'acpt_total' => 0,
        'aligned_total' => 0,
        'messages' => []
    ];

    // Log the validation attempt
    error_log("Validating table counts for total_count: $total_count");

    // More flexible pattern matching for ACPT Table
    // This will match both "ACPT Table:" and "ACPT Table" formats
    if (preg_match_all('/ACPT\s+Table:?.*?\|\s*(Agent|Process|Technology|Customer|Uncategorized)\s*\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $matches)) {
        $validation['acpt_total'] = array_sum(array_map('intval', $matches[2]));
        error_log("ACPT Table category counts found: " . implode(", ", $matches[2]));
    } else {
        error_log("No ACPT Table category counts found in the result");
    }

    // More flexible pattern matching for ACPT-Aligned Table
    // This will match both "ACPT-Aligned Table:" and "ACPT-Aligned Sub Driver & Sentiment Table:" formats
    if (preg_match_all('/ACPT-Aligned.*?Table:?.*?\|\s*(Agent|Process|Technology|Customer|Uncategorized)\s*\|\s*.*?\|\s*.*?\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $matches)) {
        $validation['aligned_total'] = array_sum(array_map('intval', $matches[2]));
        error_log("ACPT-Aligned Table category counts found: " . implode(", ", $matches[2]));
    } else {
        error_log("No ACPT-Aligned Table category counts found in the result");
    }

    // Check for explicit TOTAL rows with more flexible pattern matching
    if (preg_match('/ACPT\s+Table:?.*?\|\s*(?:\*\*)?TOTAL(?:\*\*)?\s*\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $total_match)) {
        $total_row_count = intval($total_match[1]);
        error_log("ACPT Table TOTAL row found: $total_row_count");

        if ($total_row_count != $total_count) {
            $validation['messages'][] = "ACPT Table TOTAL row mismatch: $total_row_count (should be $total_count)";
        }
    } else {
        error_log("No ACPT Table TOTAL row found");
    }

    if (preg_match('/ACPT-Aligned.*?Table:?.*?\|\s*(?:\*\*)?TOTAL(?:\*\*)?\s*\|\s*.*?\|\s*.*?\|\s*.*?\|\s*(\d+)\s*\|/si', $result, $total_match)) {
        $total_row_count = intval($total_match[1]);
        error_log("ACPT-Aligned Table TOTAL row found: $total_row_count");

        if ($total_row_count != $total_count) {
            $validation['messages'][] = "Aligned Table TOTAL row mismatch: $total_row_count (should be $total_count)";
        }
    } else {
        error_log("No ACPT-Aligned Table TOTAL row found");
    }

    // Determine if validation passed
    $validation['success'] = ($validation['acpt_total'] == $total_count) &&
                            ($validation['aligned_total'] == $total_count);

    // If the total count is 0, consider it a success (edge case handling)
    if ($total_count == 0) {
        $validation['success'] = true;
    }

    // Log validation result
    error_log("Validation result: " . ($validation['success'] ? "Success" : "Failed") .
              ", ACPT total: {$validation['acpt_total']}, Aligned total: {$validation['aligned_total']}, Expected: $total_count");

    return $validation;
}

function format_summary($summary) {
    // Basic formatting
    $summary = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $summary);
    $summary = preg_replace('/#{1,6}\s+/', '', $summary);

    // Format section headers
    $sections = [
        '1' => 'Summary',
        '2' => 'Top Pain Points or Praises',
        '3' => 'Key Takeaways',
        '4' => 'ACPT Table',
        '5' => 'ACPT-Aligned Sub Driver & Sentiment Table'
    ];

    foreach ($sections as $num => $title) {
        $summary = preg_replace('/'.$num.'\.\s+\*\*'.$title.'.*?\*\*:/',
                               "<h3 class=\"section-header\">{$num}. {$title}:</h3>",
                               $summary);
    }

    // Format tables
    $pattern = '/\|(.*?)\|\s*\n\|([-|\s]+)\|\s*\n((?:\|.*?\|\s*\n)+)/s';
    preg_match_all($pattern, $summary, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $original_table = $match[0];
        $header = $match[1];
        $headerCells = array_map('trim', explode('|', $header));

        $rows = trim($match[3]);
        $rowsArray = explode("\n", $rows);

        $html = '<div class="table-container"><table class="acpt-table"><thead><tr>';
        foreach ($headerCells as $cell) {
            if (!empty(trim($cell))) {
                $html .= '<th>' . trim($cell) . '</th>';
            }
        }
        $html .= '</tr></thead><tbody>';

        foreach ($rowsArray as $row) {
            if (empty(trim($row))) continue;

            $html .= '<tr>';
            $cells = array_map('trim', explode('|', trim($row, '|')));

            foreach ($cells as $cell) {
                if (isset($cell)) {
                    if (preg_match('/^"(.*)"$/', trim($cell), $quoteMatch)) {
                        $html .= "<td><span class=\"example-comment\">\"{$quoteMatch[1]}\"</span></td>";
                    } else {
                        $html .= "<td>" . trim($cell) . "</td>";
                    }
                }
            }
            $html .= '</tr>';
        }
        $html .= '</tbody></table></div>';

        $summary = str_replace($original_table, $html, $summary);
    }

    // Format bullet points
    $summary = preg_replace('/^\s*[-•*]\s+(.*?)(?=\n|$)/m',
                           '<div class="bullet-item"><span class="bullet">•</span> <span class="bullet-content">$1</span></div>',
                           $summary);

    // Final cleanup
    $summary = str_replace('---', '<hr class="separator">', $summary);
    $summary = nl2br($summary);

    // CSS styling
    $css = <<<CSS
    <style>
        .section-header {
            color: #1976d2;
            font-size: 13px;
            font-weight: bold;
            margin-top: 2px;
            margin-bottom: 2px;
            border-bottom: 1px solid #1976d2;
            padding-bottom: 0px;
        }
        .bullet-item {
            margin: 0px 0;
            padding-left: 5px;
            font-size: 11px;
            display: flex;
            align-items: flex-start;
        }
        .bullet {
            color: #1976d2;
            font-weight: bold;
            margin-right: 5px;
            font-size: 13px;
        }
        .table-container {
            margin: 3px 0;
            overflow-x: auto;
        }
        .acpt-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            border: 1px solid #ccc;
            background-color: #fffde7;
        }
        .acpt-table th, .acpt-table td {
            border: 1px solid #ccc;
            padding: 2px 4px;
            text-align: left;
        }
        .acpt-table th {
            background-color: #e6e6e6;
            font-weight: bold;
        }
        .example-comment {
            color: #006600;
            font-style: italic;
            font-size: 10px;
        }
        .separator {
            border: 0;
            height: 1px;
            background: #ddd;
            margin: 2px 0;
        }
    </style>
CSS;

    return "{$css}<div class=\"summary-container\">{$summary}</div>";
}

function save_summary_to_database($data_id, $summary, $domain_category, $user_id) {
    global $db;

    try {
        // Log the attempt to save summary
        error_log("Attempting to save summary for data_id: $data_id, domain_category: $domain_category, user_id: $user_id");

        // Validate inputs
        if (empty($data_id) || empty($summary) || empty($user_id)) {
            error_log("Invalid inputs for save_summary_to_database: data_id=$data_id, summary length=" . strlen($summary) . ", user_id=$user_id");
            return false;
        }

        // Check if the feedback_summaries table exists
        $table_check = $db->connect()->query("SHOW TABLES LIKE 'feedback_summaries'");
        if ($table_check->rowCount() == 0) {
            // Table doesn't exist, create it
            error_log("feedback_summaries table doesn't exist, creating it now");
            $create_table_sql = "CREATE TABLE IF NOT EXISTS `feedback_summaries` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `data_id` varchar(255) NOT NULL,
                `summary` text NOT NULL,
                `domain_category` varchar(255) DEFAULT NULL,
                `user_id` int(11) NOT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `data_id` (`data_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            $db->connect()->exec($create_table_sql);
        }

        // Check if summary exists
        $check_query = "SELECT id FROM feedback_summaries WHERE data_id = :data_id";
        $check_stmt = $db->connect()->prepare($check_query);
        $check_stmt->bindParam(':data_id', $data_id);
        $check_stmt->execute();

        $exists = $check_stmt->rowCount() > 0;

        if ($exists) {
            error_log("Summary already exists for data_id: $data_id, updating existing record");
            $query = "UPDATE feedback_summaries SET summary = :summary, domain_category = :domain_category, user_id = :user_id, created_at = CURRENT_TIMESTAMP WHERE data_id = :data_id";
        } else {
            error_log("Creating new summary record for data_id: $data_id");
            $query = "INSERT INTO feedback_summaries (data_id, summary, domain_category, user_id) VALUES (:data_id, :summary, :domain_category, :user_id)";
        }

        $conn = $db->connect();
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':summary', $summary);
        $stmt->bindParam(':domain_category', $domain_category);
        $stmt->bindParam(':user_id', $user_id);
        $result = $stmt->execute();

        if ($result) {
            error_log("Successfully saved summary for data_id: $data_id");
            return true;
        } else {
            error_log("Failed to save summary for data_id: $data_id. PDO error info: " . json_encode($stmt->errorInfo()));
            return false;
        }
    } catch (PDOException $e) {
        error_log("PDOException saving summary: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());
        return false;
    } catch (Exception $e) {
        error_log("General exception saving summary: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());
        return false;
    }
}
?>