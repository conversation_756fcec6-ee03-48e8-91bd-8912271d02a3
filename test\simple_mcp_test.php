<?php
// Simple script to test connection to MCP-MySQL-2 server

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Testing simple connection to MCP-MySQL-2 server...\n";

// Database credentials for MCP-MySQL-2
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'feedback_final_10';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    // Connect to the database with a timeout
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 5 // 5 seconds timeout
    ];
    
    echo "Attempting to connect to {$host}...\n";
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password, $options);
    
    echo "Successfully connected to MCP-MySQL-2 server!\n";
    
    // Test a simple query
    $stmt = $conn->query("SELECT 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Simple query result: " . json_encode($result) . "\n";
    
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
