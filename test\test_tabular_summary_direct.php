<?php
// test_tabular_summary_direct.php
// Test script to verify the tabular summary functionality with direct database connection

echo "Testing Tabular Summary Functionality (Direct Connection)\n";
echo "========================================================\n\n";

// Database credentials for AWS
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    // Connect to the database
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Successfully connected to database!\n\n";
    
    // Test the tabular summary query
    $sql = "SELECT 
      main_driver,
      sub_driver,
      sentiment,
      domain_category,
      lob,
      vendor,
      location,
      partner,
      COUNT(*) as total_comments,
      AVG(csat) as avg_csat,
      AVG(nps) as avg_nps
    FROM analyzed_comments
    WHERE domain_category = 'Collections'
    GROUP BY main_driver, sub_driver, sentiment, domain_category, lob, vendor, location, partner
    LIMIT 10";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Tabular summary query executed successfully!\n";
    echo "📊 Number of records returned: " . count($results) . "\n\n";
    
    if (count($results) > 0) {
        echo "📋 Sample data:\n";
        echo "===============\n";
        $sample = $results[0];
        foreach ($sample as $key => $value) {
            echo "  $key: " . ($value ?? 'NULL') . "\n";
        }
        
        echo "\n📊 Full results (first 3 records):\n";
        echo "==================================\n";
        for ($i = 0; $i < min(3, count($results)); $i++) {
            echo "Record " . ($i + 1) . ":\n";
            foreach ($results[$i] as $key => $value) {
                echo "  $key: " . ($value ?? 'NULL') . "\n";
            }
            echo "\n";
        }
    } else {
        echo "⚠️  No records found for the test query.\n";
    }
    
    // Test with different filters
    echo "🧪 Testing with different filters:\n";
    echo "==================================\n";
    
    // Test with sentiment filter
    $sql2 = "SELECT 
      main_driver,
      sentiment,
      COUNT(*) as total_comments,
      AVG(csat) as avg_csat,
      AVG(nps) as avg_nps
    FROM analyzed_comments
    WHERE sentiment = 'Negative'
    GROUP BY main_driver, sentiment
    LIMIT 5";
    
    $stmt2 = $pdo->prepare($sql2);
    $stmt2->execute();
    $results2 = $stmt2->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Negative sentiment records: " . count($results2) . "\n";
    
    // Test with date range
    $sql3 = "SELECT 
      main_driver,
      sentiment,
      COUNT(*) as total_comments
    FROM analyzed_comments
    WHERE created_at >= '2024-01-01' AND created_at <= '2024-12-31'
    GROUP BY main_driver, sentiment
    LIMIT 5";
    
    $stmt3 = $pdo->prepare($sql3);
    $stmt3->execute();
    $results3 = $stmt3->fetchAll(PDO::FETCH_ASSOC);
    
    echo "2024 date range records: " . count($results3) . "\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}

echo "\n✅ Test completed successfully!\n";
echo "The tabular summary functionality is ready for integration with the dashboard.\n";
?> 