Dashboard Calculations Documentation

This document provides a comprehensive list of all calculations used in the dashboard.php file for the feedback analysis dashboard.

Table of Contents:
1. Sentiment Distribution Calculations
2. Contribution Percentage Calculations
3. Sentiment Score Calculations
4. Change Metrics Calculations
5. CSAT and NPS Metrics

-----------------------------------------------------------

1. Sentiment Distribution Calculations
-----------------------------------------------------------

Calculation Name: Positive Sentiment Percentage
Formula: (positiveCount / totalSentiments) * 100
Code Snippet: const positiveWidth = totalSentiments > 0 ? (positiveCount / totalSentiments * 100).toFixed(2) : 0;
Explanation: Calculates the percentage of positive sentiment feedback out of all feedback.
Input Variables: 
- positiveCount: Number of positive sentiment comments
- totalSentiments: Total number of comments (positive + negative + neutral)
Units: Percentage (%)

---

Calculation Name: Negative Sentiment Percentage
Formula: (negativeCount / totalSentiments) * 100
Code Snippet: const negativeWidth = totalSentiments > 0 ? (negativeCount / totalSentiments * 100).toFixed(2) : 0;
Explanation: Calculates the percentage of negative sentiment feedback out of all feedback.
Input Variables: 
- negativeCount: Number of negative sentiment comments
- totalSentiments: Total number of comments (positive + negative + neutral)
Units: Percentage (%)

---

Calculation Name: Neutral Sentiment Percentage
Formula: (neutralCount / totalSentiments) * 100
Code Snippet: const neutralWidth = totalSentiments > 0 ? (neutralCount / totalSentiments * 100).toFixed(2) : 0;
Explanation: Calculates the percentage of neutral sentiment feedback out of all feedback.
Input Variables: 
- neutralCount: Number of neutral sentiment comments
- totalSentiments: Total number of comments (positive + negative + neutral)
Units: Percentage (%)

---

Calculation Name: Sub-driver Positive Percentage
Formula: (positive_count / total_count) * 100
Code Snippet: $positive_percentage = $total > 0 ? round(($result['positive_count'] / $total) * 100, 1) : 0;
Explanation: Calculates the percentage of positive sentiment comments for a specific sub-driver.
Input Variables: 
- positive_count: Number of positive comments for the sub-driver
- total_count: Total number of comments for the sub-driver
Units: Percentage (%)

---

Calculation Name: Sub-driver Neutral Percentage
Formula: (neutral_count / total_count) * 100
Code Snippet: $neutral_percentage = $total > 0 ? round(($result['neutral_count'] / $total) * 100, 1) : 0;
Explanation: Calculates the percentage of neutral sentiment comments for a specific sub-driver.
Input Variables: 
- neutral_count: Number of neutral comments for the sub-driver
- total_count: Total number of comments for the sub-driver
Units: Percentage (%)

---

Calculation Name: Sub-driver Negative Percentage
Formula: (negative_count / total_count) * 100
Code Snippet: $negative_percentage = $total > 0 ? round(($result['negative_count'] / $total) * 100, 1) : 0;
Explanation: Calculates the percentage of negative sentiment comments for a specific sub-driver.
Input Variables: 
- negative_count: Number of negative comments for the sub-driver
- total_count: Total number of comments for the sub-driver
Units: Percentage (%)

-----------------------------------------------------------

2. Contribution Percentage Calculations
-----------------------------------------------------------

Calculation Name: L2 Driver Contribution Percentage
Formula: (subDriver.total_count / totalCount) * 100
Code Snippet: const contribution = ((subDriver.total_count / totalCount) * 100).toFixed(1);
Explanation: Calculates what percentage of comments within an L1 driver category belongs to a specific L2 driver.
Input Variables: 
- subDriver.total_count: Total number of comments for the specific L2 driver
- totalCount: Total number of comments for all L2 drivers within the selected L1 driver
Units: Percentage (%)

---

Calculation Name: Driver Contribution to Total Feedback
Formula: (driverCounts[index] / window.totalSentiments) * 100
Code Snippet: const contributionPercentage = window.totalSentiments > 0 ? ((driverCounts[index] / window.totalSentiments) * 100).toFixed(2) : 0;
Explanation: Calculates what percentage of all feedback is attributed to a specific driver.
Input Variables: 
- driverCounts[index]: Number of comments for the specific driver
- window.totalSentiments: Total number of comments across all drivers
Units: Percentage (%)

-----------------------------------------------------------

3. Sentiment Score Calculations
-----------------------------------------------------------

Calculation Name: Overall Sentiment Score
Formula: (positiveCount / totalSentiments) * 100
Code Snippet: const overallSentimentScore = totalSentiments > 0 ? ((positiveCount / totalSentiments) * 100).toFixed(2) : 0;
Explanation: Calculates an overall sentiment score based on the percentage of positive feedback.
Input Variables: 
- positiveCount: Number of positive sentiment comments
- totalSentiments: Total number of comments (positive + negative + neutral)
Units: Score (0-100)

-----------------------------------------------------------

4. Change Metrics Calculations
-----------------------------------------------------------

Calculation Name: Positive Sentiment Change
Formula: positiveCount - historicalPositiveCount
Code Snippet: const positiveChange = positiveCount - historicalPositiveCount;
Explanation: Calculates the change in positive sentiment comments compared to a historical period.
Input Variables: 
- positiveCount: Current number of positive sentiment comments
- historicalPositiveCount: Number of positive sentiment comments from the previous period
Units: Count (absolute number)

---

Calculation Name: Negative Sentiment Change
Formula: negativeCount - historicalNegativeCount
Code Snippet: const negativeChange = negativeCount - historicalNegativeCount;
Explanation: Calculates the change in negative sentiment comments compared to a historical period.
Input Variables: 
- negativeCount: Current number of negative sentiment comments
- historicalNegativeCount: Number of negative sentiment comments from the previous period
Units: Count (absolute number)

---

Calculation Name: Neutral Sentiment Change
Formula: neutralCount - historicalNeutralCount
Code Snippet: const neutralChange = neutralCount - historicalNeutralCount;
Explanation: Calculates the change in neutral sentiment comments compared to a historical period.
Input Variables: 
- neutralCount: Current number of neutral sentiment comments
- historicalNeutralCount: Number of neutral sentiment comments from the previous period
Units: Count (absolute number)

-----------------------------------------------------------

5. CSAT and NPS Metrics
-----------------------------------------------------------

Calculation Name: Average CSAT Score
Formula: AVG(csat)
Code Snippet: AVG(csat) as avg_csat (SQL query)
Explanation: Calculates the average Customer Satisfaction Score for a specific driver or sub-driver.
Input Variables: 
- csat: Individual CSAT scores from customer feedback
Units: Score (typically 1-5 or 1-10)

---

Calculation Name: Average NPS Score
Formula: AVG(nps)
Code Snippet: AVG(nps) as avg_nps (SQL query)
Explanation: Calculates the average Net Promoter Score for a specific driver or sub-driver.
Input Variables: 
- nps: Individual NPS scores from customer feedback
Units: Score (typically -100 to +100)

---

Calculation Name: Formatted CSAT Average
Formula: round(avg_csat, 2)
Code Snippet: $avg_csat = is_null($result['avg_csat']) ? 0 : round($result['avg_csat'], 2);
Explanation: Formats the average CSAT score to always display with 2 decimal places.
Input Variables: 
- avg_csat: Raw average CSAT score
Units: Score (typically 1-5 or 1-10)

---

Calculation Name: Formatted NPS Average
Formula: round(avg_nps, 2)
Code Snippet: $avg_nps = is_null($result['avg_nps']) ? 0 : round($result['avg_nps'], 2);
Explanation: Formats the average NPS score to always display with 2 decimal places.
Input Variables: 
- avg_nps: Raw average NPS score
Units: Score (typically -100 to +100)

-----------------------------------------------------------------------------------------------

# Dashboard Calculation Summary

I've created a comprehensive document that details the additional calculations present in SQL queries and backend processes that feed data to the dashboard, as referenced in the DatabaseInteraction.php file. The document covers:

## 1. Sentiment Analysis Calculations
- Sentiment distribution counts
- Sentiment across main drivers
- Historical sentiment trends
- Time series sentiment analysis

## 2. Driver Analysis Calculations
- Main driver distribution
- Sub-driver distribution
- Sub-driver contribution analysis
- Sentiment level calculation

## 3. CSAT and NPS Calculations
- CSAT impact analysis
- NPS impact analysis
- Top CSAT impact sub-drivers
- Top NPS impact sub-drivers

## 4. Advanced Analytics
- NPS promoter and detractor analysis
- Detailed sub-driver metrics
- Timeline metrics

## 5. Filtering Mechanisms
- All calculations support filtering by data_id, user_id, sentiment, and domain_category

The document provides detailed SQL queries for each calculation along with explanations of how they're used in the dashboard and references to the corresponding methods in DatabaseInteraction.php.

This document completes the documentation of backend calculations that feed data to the dashboard, which was previously noted as pending in the Dashboard_Calculations.docx file.


# Dashboard Calculations in SQL Queries and Backend Processes

This document outlines the additional calculations present in SQL queries and backend processes that feed data to the dashboard, as referenced in the DatabaseInteraction.php file.

## 1. Sentiment Analysis Calculations

### 1.1 Sentiment Distribution Counts
```sql
SELECT sentiment, COUNT(*) AS count 
FROM analyzed_comments
WHERE [filters]
GROUP BY sentiment
```
- Calculates the count of comments by sentiment (Positive, Neutral, Negative)
- Used in sentiment distribution charts and metrics
- Method: `getSentimentsCount()` (lines 339-379)

### 1.2 Sentiment Across Main Drivers
```sql
SELECT main_driver, sentiment, COUNT(*) AS count 
FROM analyzed_comments
WHERE [filters]
GROUP BY main_driver, sentiment
```
- Calculates the distribution of sentiments across different main drivers
- Used in cross-tabulation charts showing sentiment by driver
- Method: `getSentimentsAcrossMainDriversCount()` (lines 427-470)

### 1.3 Historical Sentiment Trends
```sql
SELECT sentiment, COUNT(*) AS count 
FROM analyzed_comments 
WHERE created_at BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()
AND [filters]
GROUP BY sentiment
```
- Calculates sentiment distribution over the past 7 days
- Used for trend analysis in dashboard
- Method: `getHistoricalSentiments()` (lines 1160-1196)

### 1.4 Time Series Sentiment Analysis
```sql
SELECT
  DATE(ac.created_at) AS date,
  SUM(CASE WHEN ac.sentiment = 'Positive' THEN 1 ELSE 0 END) AS Positive,
  SUM(CASE WHEN ac.sentiment = 'Neutral' THEN 1 ELSE 0 END) AS Neutral,
  SUM(CASE WHEN ac.sentiment = 'Negative' THEN 1 ELSE 0 END) AS Negative
FROM analyzed_comments ac
WHERE [filters]
GROUP BY DATE(ac.created_at)
ORDER BY date
```
- Calculates daily sentiment counts for time series analysis
- Used in trend charts showing sentiment over time
- Method: `getTimeSeriesSentiments()` (lines 689-733)

## 2. Driver Analysis Calculations

### 2.1 Main Driver Distribution
```sql
SELECT main_driver, COUNT(*) AS count 
FROM analyzed_comments
WHERE [filters]
GROUP BY main_driver
```
- Calculates the frequency of each main driver
- Used in driver distribution charts
- Method: `getMainDriversCount()` (lines 299-337)

### 2.2 Sub-Driver Distribution
```sql
SELECT main_driver, sub_driver 
FROM analyzed_comments
WHERE [filters]
GROUP BY main_driver, sub_driver
```
- Groups sub-drivers by their main drivers
- Used in hierarchical driver analysis
- Method: `getSubDriversCount()` (lines 382-425)

### 2.3 Sub-Driver Contribution Analysis
```sql
SELECT
  main_driver,
  sub_driver,
  COUNT(*) AS total_count,
  SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_count,
  SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_count,
  SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_count,
  AVG(csat) as avg_csat,
  AVG(nps) as avg_nps
FROM analyzed_comments
WHERE [filters]
GROUP BY main_driver, sub_driver
```
- Calculates detailed metrics for each sub-driver including:
  - Total count
  - Positive/neutral/negative counts
  - Sentiment percentages
  - Average CSAT and NPS scores
- Used in detailed driver analysis tables
- Method: `getSubDriversContribution()` (lines 473-544)

### 2.4 Sentiment Level Calculation
```php
$sentiment_score = $positive_percentage - $negative_percentage;

if ($sentiment_score > 30) {
    $sentiment_level = "🟢";
} elseif ($sentiment_score > 10) {
    $sentiment_level = "🟡";
} elseif ($sentiment_score > -10) {
    $sentiment_level = "🟠";
} else {
    $sentiment_level = "🔴";
}
```
- Calculates a sentiment score as the difference between positive and negative percentages
- Assigns a color-coded sentiment level based on the score
- Used in driver sentiment analysis tables
- Method: `getDetailedSentimentsData()` (lines 824-891)

## 3. CSAT and NPS Calculations

### 3.1 CSAT Impact Analysis
```sql
SELECT
  AVG(csat) AS average_csat,
  GROUP_CONCAT(DISTINCT sub_driver ORDER BY csat DESC SEPARATOR ', ') AS top_positive_drivers,
  GROUP_CONCAT(DISTINCT sub_driver ORDER BY csat ASC SEPARATOR ', ') AS top_negative_drivers
FROM analyzed_comments
WHERE csat IS NOT NULL
AND [filters]
```
- Calculates average CSAT score
- Identifies top positive and negative drivers affecting CSAT
- Calculates CSAT impact as the difference from baseline (80)
- Method: `getCsatImpact()` (lines 1197-1243)

### 3.2 NPS Impact Analysis
```sql
SELECT
  SUM(CASE WHEN nps >= 9 THEN 1 ELSE 0 END) AS promoters,
  SUM(CASE WHEN nps < 7 THEN 1 ELSE 0 END) AS detractors,
  GROUP_CONCAT(DISTINCT sub_driver ORDER BY nps DESC SEPARATOR ', ') AS top_positive_drivers,
  GROUP_CONCAT(DISTINCT sub_driver ORDER BY nps ASC SEPARATOR ', ') AS top_negative_drivers
FROM analyzed_comments
WHERE nps IS NOT NULL
AND [filters]
```
- Calculates NPS promoters (score ≥ 9) and detractors (score < 7)
- Calculates NPS as (promoters - detractors) / total_responses * 100
- Identifies top drivers affecting NPS positively and negatively
- Method: `getNpsImpact()` (lines 1245-1295)

### 3.3 Top CSAT Impact Sub-Drivers
```sql
SELECT sub_driver, AVG(csat) as average_csat
FROM analyzed_comments
WHERE [filters]
GROUP BY sub_driver
HAVING COUNT(*) > 5
ORDER BY average_csat DESC
LIMIT 5
```
- Identifies sub-drivers with highest average CSAT scores
- Filters to include only sub-drivers with sufficient data (> 5 comments)
- Method: `getTopCsatImpactSubDrivers()` (lines 965-1000)

### 3.4 Top NPS Impact Sub-Drivers
```sql
SELECT sub_driver, AVG(nps) as average_nps
FROM analyzed_comments
WHERE [filters]
GROUP BY sub_driver
HAVING COUNT(*) > 5
ORDER BY average_nps DESC
LIMIT 5
```
- Identifies sub-drivers with highest average NPS scores
- Filters to include only sub-drivers with sufficient data (> 5 comments)
- Method: `getTopNpsImpactSubDrivers()` (lines 1002-1037)

## 4. Advanced Analytics

### 4.1 NPS Promoter and Detractor Analysis
```sql
-- Promoters
SELECT sub_driver, COUNT(*) as promoter
FROM analyzed_comments
WHERE nps >= 9
AND [filters]
GROUP BY sub_driver
ORDER BY promoter DESC
LIMIT 5

-- Detractors
SELECT sub_driver, COUNT(*) as detractor
FROM analyzed_comments
WHERE nps < 7
AND [filters]
GROUP BY sub_driver
ORDER BY detractor DESC
LIMIT 5
```
- Identifies top sub-drivers associated with NPS promoters and detractors
- Used in NPS driver analysis
- Methods: `getTopNpsPromoterSubDrivers()` (lines 1039-1073) and `getTopNpsDetractorSubDrivers()` (lines 1075-1109)

### 4.2 Detailed Sub-Driver Metrics
```sql
SELECT
  sub_driver,
  COUNT(*) as total_count,
  AVG(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_ratio,
  AVG(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_ratio,
  AVG(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_ratio,
  AVG(csat) as avg_csat,
  AVG(nps) as avg_nps
FROM analyzed_comments
WHERE [filters]
GROUP BY sub_driver
ORDER BY total_count DESC
```
- Calculates comprehensive metrics for each sub-driver:
  - Total count
  - Positive/negative/neutral ratios
  - Average CSAT and NPS scores
- Used in detailed sub-driver analysis
- Method: `getDetailedSubDriverMetrics()` (lines 1377-1411)

### 4.3 Timeline Metrics
```sql
SELECT
  DATE(created_at) as date,
  COUNT(*) as total,
  SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_count,
  SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_count,
  SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_count,
  AVG(csat) as avg_csat,
  AVG(nps) as avg_nps
FROM analyzed_comments
WHERE [filters]
GROUP BY DATE(created_at)
ORDER BY date
```
- Calculates daily metrics including:
  - Total comment count
  - Sentiment distribution
  - Average CSAT and NPS scores
- Used in timeline analysis charts
- Method: `getTimelineMetrics()` (lines 1413-1447)

## 5. Filtering Mechanisms

All dashboard calculations support the following filtering parameters:
- `data_id`: Filter by specific data set
- `user_id`: Filter by user
- `sentiment`: Filter by sentiment (Positive, Neutral, Negative, or 'all')
- `domain_category`: Filter by domain category

These filters are dynamically applied to SQL queries using WHERE clauses constructed based on the provided parameters.
