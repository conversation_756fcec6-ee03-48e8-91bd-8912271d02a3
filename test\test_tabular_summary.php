<?php
// test_tabular_summary.php
// Test script to verify the tabular summary functionality with different filters

function runTest($postData, $label) {
    echo "\n==============================\n";
    echo "Test: $label\n";
    echo "==============================\n";
    
    // Simulate POST data
    $_POST = $postData;
    
    // Include the tabular summary script
    ob_start();
    include __DIR__ . '/../data_tabular_summary.php';
    $output = ob_get_clean();
    
    echo "Response from data_tabular_summary.php:\n";
    echo $output . "\n";
    
    // Try to decode JSON response
    $response = json_decode($output, true);
    if ($response) {
        if ($response['success']) {
            echo "✅ SUCCESS: Tabular summary generated successfully!\n";
            echo "📊 Number of records returned: " . count($response['data']) . "\n";
            if (count($response['data']) > 0) {
                echo "\n📋 Sample data:\n";
                $sample = $response['data'][0];
                foreach ($sample as $key => $value) {
                    echo "  $key: $value\n";
                }
            }
        } else {
            echo "❌ ERROR: " . ($response['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ ERROR: Invalid JSON response\n";
        echo "Raw output: " . $output . "\n";
    }
}

echo "Testing Tabular Summary Functionality with Different Filters\n";
echo "============================================================\n";

// 1. Default test (domain_category only)
runTest([
    'domain_category' => 'Collections',
    'data_id' => '',
    'sentiment' => '',
    'start_date' => '',
    'end_date' => '',
    'lob' => '',
    'vendor' => '',
    'location' => '',
    'partner' => '',
    'resolution_status' => ''
], 'Domain Category: Collections');

// 2. Test with sentiment filter
runTest([
    'domain_category' => 'Collections',
    'sentiment' => 'Negative',
    'data_id' => '',
    'start_date' => '',
    'end_date' => '',
    'lob' => '',
    'vendor' => '',
    'location' => '',
    'partner' => '',
    'resolution_status' => ''
], 'Domain Category: Collections, Sentiment: Negative');

// 3. Test with date range filter (patching)
runTest([
    'domain_category' => 'Collections',
    'start_date' => '2024-01-01',
    'end_date' => '2024-12-31',
    'data_id' => '',
    'sentiment' => '',
    'lob' => '',
    'vendor' => '',
    'location' => '',
    'partner' => '',
    'resolution_status' => ''
], 'Domain Category: Collections, Date Range: 2024-01-01 to 2024-12-31');

// 4. Test with all filters
runTest([
    'domain_category' => 'Collections',
    'data_id' => '686793923253e',
    'sentiment' => 'Negative',
    'start_date' => '2024-01-01',
    'end_date' => '2024-12-31',
    'lob' => 'Cable',
    'vendor' => 'Sitel',
    'location' => 'New York',
    'partner' => 'Partner A',
    'resolution_status' => ''
], 'All Filters');

echo "\nAll tests completed.\n";
?> 