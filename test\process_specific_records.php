<?php
/**
 * <PERSON>ript to process specific missing records for data_id 682b54974bab6
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = '682b54974bab6';

// Specific record IDs to process
$record_ids = [17413, 17474, 19665];

// Function to log messages
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
    // Force output buffer flush
    if (ob_get_level() > 0) {
        ob_flush();
    }
    flush();
}

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Process each record
    foreach ($record_ids as $record_id) {
        log_message("\nProcessing record ID: $record_id");
        
        // Get the record details
        $recordQuery = "SELECT * FROM feedback_data WHERE id = :id AND data_id = :data_id";
        $recordStmt = $conn->prepare($recordQuery);
        $recordStmt->bindParam(':id', $record_id);
        $recordStmt->bindParam(':data_id', $data_id);
        $recordStmt->execute();
        $record = $recordStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$record) {
            log_message("Record ID: $record_id not found");
            continue;
        }
        
        log_message("Found record: " . json_encode([
            'id' => $record['id'],
            'data_id' => $record['data_id'],
            'user_id' => $record['user_id'],
            'feedback_data' => $record['feedback_data'],
            'feedback_data_length' => strlen($record['feedback_data'])
        ]));
        
        // Check if record is already in analyzed_comments
        $checkQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id AND comment = :comment";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bindParam(':data_id', $data_id);
        $checkStmt->bindParam(':comment', $record['feedback_data']);
        $checkStmt->execute();
        $checkResult = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($checkResult['count'] > 0) {
            log_message("Record ID: $record_id already exists in analyzed_comments");
            continue;
        }
        
        // Try to insert directly into analyzed_comments
        try {
            log_message("Attempting direct insertion into analyzed_comments...");
            
            $insertQuery = "INSERT INTO analyzed_comments (
                comment, data_id, user_id, csat, nps, pid,
                main_driver, sub_driver, sentiment,
                domain_category, resolution_comment, internal_scores, feedback_submit_date,
                feedback_month, feedback_time, lob, vendor, location, partner,
                dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid,
                'Auto-Generated', 'Auto-Generated', 'Neutral',
                :domain_category, :resolution_comment, :internal_scores, :feedback_submit_date,
                :feedback_month, :feedback_time, :lob, :vendor, :location, :partner,
                :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
            )";
            
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindParam(':comment', $record['feedback_data']);
            $insertStmt->bindParam(':data_id', $record['data_id']);
            $insertStmt->bindParam(':user_id', $record['user_id']);
            $insertStmt->bindParam(':csat', $record['csat']);
            $insertStmt->bindParam(':nps', $record['nps']);
            $insertStmt->bindParam(':pid', $record['pid']);
            $insertStmt->bindParam(':domain_category', $record['domain_category']);
            $insertStmt->bindParam(':resolution_comment', $record['resolution_comment']);
            $insertStmt->bindParam(':internal_scores', $record['internal_scores']);
            $insertStmt->bindParam(':feedback_submit_date', $record['feedback_submit_date']);
            $insertStmt->bindParam(':feedback_month', $record['feedback_month']);
            $insertStmt->bindParam(':feedback_time', $record['feedback_time']);
            $insertStmt->bindParam(':lob', $record['lob']);
            $insertStmt->bindParam(':vendor', $record['vendor']);
            $insertStmt->bindParam(':location', $record['location']);
            $insertStmt->bindParam(':partner', $record['partner']);
            $insertStmt->bindParam(':dummy_1', $record['dummy_1']);
            $insertStmt->bindParam(':dummy_2', $record['dummy_2']);
            $insertStmt->bindParam(':dummy_3', $record['dummy_3']);
            $insertStmt->bindParam(':dummy_4', $record['dummy_4']);
            $insertStmt->bindParam(':dummy_5', $record['dummy_5']);
            
            $result = $insertStmt->execute();
            
            if ($result) {
                log_message("Successfully inserted record ID: $record_id into analyzed_comments");
                
                // Verify the record was inserted
                $verifyQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id AND comment = :comment";
                $verifyStmt = $conn->prepare($verifyQuery);
                $verifyStmt->bindParam(':data_id', $data_id);
                $verifyStmt->bindParam(':comment', $record['feedback_data']);
                $verifyStmt->execute();
                $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                
                if ($verifyResult['count'] > 0) {
                    log_message("Verified record ID: $record_id was successfully inserted into analyzed_comments");
                } else {
                    log_message("WARNING: Record ID: $record_id was not found in analyzed_comments after insertion");
                }
            } else {
                log_message("Failed to insert record ID: $record_id - Error: " . json_encode($insertStmt->errorInfo()));
            }
        } catch (PDOException $e) {
            log_message("ERROR: Failed to insert record ID: $record_id - " . $e->getMessage());
        }
    }
    
    log_message("\nProcessing completed");
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
