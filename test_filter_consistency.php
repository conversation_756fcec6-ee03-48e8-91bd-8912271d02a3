<?php
// Test script to verify filter consistency between dashboard and tabular summary
require_once 'DatabaseInteraction.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    echo "Error: No user session found\n";
    exit;
}

$user_id = $_SESSION['user_id'];

// Test filters that should match the dashboard (601 records)
$test_filters = [
    'domain_category' => '',
    'data_id' => '',
    'sentiment' => '',
    'start_date' => '',
    'end_date' => '',
    'partner' => '',
    'lob' => '',
    'dummy_1' => '',
    'dummy_5' => ''
];

echo "Testing filter consistency...\n";
echo "User ID: $user_id\n\n";

// Test 1: Count records using dashboard-style query
try {
    $db = new DatabaseInteraction();
    $pdo = $db->connect();
    
    // Build filter conditions exactly like data_tabular_summary.php
    $filterConditions = "";
    $params = [':user_id' => $user_id];
    
    // Basic filters - handle both empty and 'all' values
    if (isset($test_filters['data_id']) && $test_filters['data_id'] !== '' && $test_filters['data_id'] !== 'all') {
        $filterConditions .= " AND data_id = :data_id";
        $params[':data_id'] = $test_filters['data_id'];
    }

    if (isset($test_filters['domain_category']) && $test_filters['domain_category'] !== '' && $test_filters['domain_category'] !== 'all') {
        $filterConditions .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $test_filters['domain_category'];
    }

    if (isset($test_filters['sentiment']) && $test_filters['sentiment'] !== '' && $test_filters['sentiment'] !== 'all') {
        $filterConditions .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $test_filters['sentiment'];
    }

    // Date range filters - EXACT match with filter_validation.php
    if (isset($test_filters['start_date']) && $test_filters['start_date'] !== '') {
        $filterConditions .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $test_filters['start_date'];
    }

    if (isset($test_filters['end_date']) && $test_filters['end_date'] !== '') {
        $filterConditions .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $test_filters['end_date'];
    }

    // Additional filters to match dashboard exactly
    if (isset($test_filters['partner']) && $test_filters['partner'] !== '' && $test_filters['partner'] !== 'all') {
        $filterConditions .= " AND partner = :partner";
        $params[':partner'] = $test_filters['partner'];
    }

    if (isset($test_filters['lob']) && $test_filters['lob'] !== '' && $test_filters['lob'] !== 'all') {
        $filterConditions .= " AND lob = :lob";
        $params[':lob'] = $test_filters['lob'];
    }

    if (isset($test_filters['dummy_1']) && $test_filters['dummy_1'] !== '' && $test_filters['dummy_1'] !== 'all') {
        $filterConditions .= " AND dummy_1 = :dummy_1";
        $params[':dummy_1'] = $test_filters['dummy_1'];
    }

    if (isset($test_filters['dummy_5']) && $test_filters['dummy_5'] !== '' && $test_filters['dummy_5'] !== 'all') {
        $filterConditions .= " AND dummy_5 = :dummy_5";
        $params[':dummy_5'] = $test_filters['dummy_5'];
    }
    
    echo "Filter conditions: $filterConditions\n";
    echo "Parameters: " . json_encode($params) . "\n\n";
    
    // Count total records
    $countSql = "SELECT COUNT(*) as total FROM analyzed_comments WHERE user_id = :user_id" . $filterConditions;
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "Total records with current filters: $totalCount\n";
    
    // Test the tabular summary aggregation
    $sql = "SELECT
      main_driver,
      sub_driver,
      sentiment,
      COUNT(*) as total_comments
    FROM analyzed_comments
    WHERE user_id = :user_id" . $filterConditions . "
    GROUP BY main_driver, sub_driver, sentiment
    ORDER BY total_comments DESC
    LIMIT 10";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $aggregatedTotal = 0;
    echo "\nTop 10 aggregated results:\n";
    foreach ($results as $row) {
        echo "- {$row['main_driver']} | {$row['sub_driver']} | {$row['sentiment']}: {$row['total_comments']} comments\n";
        $aggregatedTotal += $row['total_comments'];
    }
    
    echo "\nAggregated total from top 10: $aggregatedTotal\n";
    echo "Expected: Should match the dashboard count of 601 (if no filters applied)\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
