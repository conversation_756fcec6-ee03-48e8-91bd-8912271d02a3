<?php
/**
 * <PERSON><PERSON>t to identify and optionally remove synthetic records
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682b54974bab6';

// Whether to actually delete the synthetic records (set to false for a dry run)
$delete_records = isset($_GET['delete']) && $_GET['delete'] === 'true';

// Log file
$log_file = 'synthetic_records.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    
    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting to identify synthetic records for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Find synthetic records by pattern matching
    log_message("Finding synthetic records by pattern matching...");
    
    $syntheticQuery = "
        SELECT id, comment, created_at
        FROM analyzed_comments
        WHERE data_id = :data_id
        AND (
            comment LIKE 'Auto-generated record%'
            OR (main_driver = 'Auto-Generated' AND sub_driver = 'Auto-Generated' AND sentiment = 'Neutral')
        )
        ORDER BY id DESC
    ";
    
    $syntheticStmt = $conn->prepare($syntheticQuery);
    $syntheticStmt->bindParam(':data_id', $data_id);
    $syntheticStmt->execute();
    $syntheticRecords = $syntheticStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $syntheticCount = count($syntheticRecords);
    log_message("Found $syntheticCount potential synthetic records");
    
    if ($syntheticCount == 0) {
        log_message("No synthetic records found for data_id: $data_id");
        exit(0);
    }
    
    // Display some sample synthetic records
    log_message("Sample synthetic records:");
    $sampleSize = min(10, $syntheticCount);
    
    for ($i = 0; $i < $sampleSize; $i++) {
        $record = $syntheticRecords[$i];
        log_message("ID: " . $record['id'] . ", Comment: " . $record['comment'] . ", Created: " . $record['created_at']);
    }
    
    // Check if we should delete the synthetic records
    if ($delete_records) {
        log_message("Deleting $syntheticCount synthetic records...");
        
        // Delete in batches to avoid overloading the database
        $batchSize = 100;
        $deleted = 0;
        
        for ($i = 0; $i < $syntheticCount; $i += $batchSize) {
            $batch = array_slice($syntheticRecords, $i, $batchSize);
            $ids = array_column($batch, 'id');
            $placeholders = implode(',', array_fill(0, count($ids), '?'));
            
            $deleteQuery = "DELETE FROM analyzed_comments WHERE id IN ($placeholders)";
            $deleteStmt = $conn->prepare($deleteQuery);
            
            // Bind the ID values
            foreach ($ids as $index => $id) {
                $deleteStmt->bindValue($index + 1, $id);
            }
            
            $deleteStmt->execute();
            $deleted += $deleteStmt->rowCount();
            
            log_message("Deleted batch " . (floor($i / $batchSize) + 1) . " of " . ceil($syntheticCount / $batchSize));
        }
        
        log_message("Deleted $deleted synthetic records");
        
        // Get updated counts
        $countQuery = "
            SELECT 
                (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
                (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count,
                (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id) as queue_count
        ";
        $countStmt = $conn->prepare($countQuery);
        $countStmt->bindParam(':data_id', $data_id);
        $countStmt->execute();
        $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
        
        $feedback_count = $counts['feedback_count'];
        $analyzed_count = $counts['analyzed_count'];
        $queue_count = $counts['queue_count'];
        $missing_count = $feedback_count - $analyzed_count - $queue_count;
        
        log_message("Updated counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, In queue: $queue_count, Missing: $missing_count");
    } else {
        log_message("This was a dry run. No records were deleted.");
        log_message("To delete these synthetic records, add ?delete=true to the URL.");
    }
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
