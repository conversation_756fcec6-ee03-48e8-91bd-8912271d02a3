<?php
// Test script to debug tabular summary SQL
session_start();
require_once 'config.php';
require_once 'FilterValidation.php';

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    // Check if user is authenticated
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'error' => 'User not authenticated.']);
        exit;
    }

    $user_id = $_SESSION['user_id'];
    echo json_encode(['debug' => 'User ID: ' . $user_id]);

    // Test database connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Simple test query first
    $testSql = "SELECT COUNT(*) as total FROM analyzed_comments WHERE user_id = :user_id";
    $testStmt = $pdo->prepare($testSql);
    $testStmt->execute([':user_id' => $user_id]);
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode(['debug' => 'Total records for user: ' . $testResult['total']]);

    // Test the enhanced query step by step
    $simpleSql = "SELECT 
      main_driver,
      sub_driver,
      sentiment,
      COUNT(*) as total_comments,
      AVG(csat) as avg_csat,
      AVG(nps) as avg_nps
    FROM analyzed_comments
    WHERE user_id = :user_id
    GROUP BY main_driver, sub_driver, sentiment
    ORDER BY total_comments DESC
    LIMIT 5";

    $simpleStmt = $pdo->prepare($simpleSql);
    $simpleStmt->execute([':user_id' => $user_id]);
    $simpleResult = $simpleStmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['success' => true, 'simple_test' => $simpleResult]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
}
?>
