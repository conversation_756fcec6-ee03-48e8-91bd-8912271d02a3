# AWS Elastic Beanstalk Deployment Troubleshooting Guide

## Common Deployment Issues and Solutions

### 1. Configuration File Syntax

- **YAML Syntax**: Elastic Beanstalk configuration files use YAML syntax, which is sensitive to indentation. Make sure all indentation is consistent (use spaces, not tabs).
- **File Encoding**: Save all configuration files with UTF-8 encoding without BOM.
- **Line Endings**: Use Unix-style line endings (LF) rather than Windows-style line endings (CRLF).

### 2. Deployment Strategy

- **Try the Combined Configuration**: Use the `00_combined_config.config` file for deployment. If it works, you can later split it back into separate files if needed.
- **Incremental Deployment**: Try deploying with just the PHP extensions configuration first, then add the other configurations one by one.

### 3. Checking Deployment Logs

To troubleshoot deployment failures:

1. Go to the AWS Elastic Beanstalk console
2. Select your environment
3. Click on "Logs" in the left navigation
4. Request "Last 100 lines of logs" or "Full logs"
5. Look for error messages related to your configuration files

### 4. Package Installation Issues

If specific packages are causing issues:

- Check if the package names are correct for your Amazon Linux version
- Try removing packages one by one to identify which one is causing the issue
- Consider using the `yum` command directly in a container command instead of the `packages` section

### 5. Service Restart Issues

If service restarts are failing:

- Check the service names in your environment (they might be different from what you expect)
- Use conditional commands as shown in the updated configuration files

## Deployment Steps

1. Make sure all configuration files have the correct syntax and formatting
2. Deploy using the AWS EB CLI or the AWS Management Console
3. Check the logs for any errors
4. If deployment fails, try the combined configuration file
5. If it still fails, try deploying with minimal configuration and add more gradually

## Testing After Deployment

After successful deployment:

1. Check if the PHP zip extension is installed:
   ```
   ssh into your instance and run: php -m | grep zip
   ```

2. Check if the cron job is set up correctly:
   ```
   cat /etc/cron.d/worker
   ```

3. Check if the worker script is running:
   ```
   ps aux | grep worker.php
   ```

4. Check the cron log:
   ```
   tail -f /var/log/cron.log
   ```
