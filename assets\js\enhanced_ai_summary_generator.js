// Enhanced AI Summary Generator for Tabular Feedback Data
async function generateAISummary(enhancedData) {
    // Handle both old and new data structures
    let tableData, overallStats, topIssues, vendorPerformance;

    if (enhancedData && enhancedData.detailed_summary) {
        // New enhanced structure
        tableData = enhancedData.detailed_summary;
        overallStats = enhancedData.overall_statistics;
        topIssues = enhancedData.top_issues;
        vendorPerformance = enhancedData.vendor_performance;
    } else {
        // Backward compatibility with old structure
        tableData = enhancedData || [];
        overallStats = null;
        topIssues = null;
        vendorPerformance = null;
    }

    const totalComments = overallStats ? overallStats.total_records :
                         tableData.reduce((sum, row) => sum + (row.total_comments || 0), 0);

    if (totalComments === 0) {
        alert('No feedback data to analyze.');
        return;
    }

    const avgCSAT = overallStats ? overallStats.overall_avg_csat :
                   tableData.reduce((sum, row) => sum + ((row.avg_csat || 0) * (row.total_comments || 0)), 0) / totalComments;
    const avgNPS = overallStats ? overallStats.overall_avg_nps :
                  tableData.reduce((sum, row) => sum + ((row.avg_nps || 0) * (row.total_comments || 0)), 0) / totalComments;

    const prompt = buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS);

    try {
        const response = await fetch('ai_summary.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ prompt })
        });

        const data = await response.json();

        if (data.status === 'error') {
            if (data.message === 'Not logged in') {
                alert('You must be logged in to generate an AI summary.');
            } else {
                console.error('AI Summary Error:', data.message);
                alert('Error generating AI summary. Check console for details.');
            }
            return;
        }

        const summaryHTML = data.summary_html || '<p>No summary generated.</p>';
        document.getElementById('summaryContainer').innerHTML = summaryHTML;

    } catch (error) {
        console.error('Fetch Error:', error);
        alert('Network error or server unavailable.');
    }
}

function generateAdvancedAnalytics(tableData, totalComments, avgCSAT, avgNPS) {
    const analytics = {
        vendors: {},
        painPoints: {},
        domains: {},
        resolutions: {}
    };

    tableData.forEach(row => {
        const vendor = row.vendor || 'Unknown';
        const pain = row.pain_points || 'Unknown';
        const domain = row.domain_category || 'Unknown';
        const resolution = row.resolution_status || 'Unknown';

        if (!analytics.vendors[vendor]) analytics.vendors[vendor] = { comments: 0, csatSum: 0, delta: 0 };
        analytics.vendors[vendor].comments += row.total_comments || 0;
        analytics.vendors[vendor].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

        if (!analytics.painPoints[pain]) analytics.painPoints[pain] = { freq: 0, csatSum: 0, severity: 0 };
        analytics.painPoints[pain].freq += row.total_comments || 0;
        analytics.painPoints[pain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

        if (!analytics.domains[domain]) analytics.domains[domain] = { comments: 0, csatSum: 0, resolved: 0, total: 0 };
        analytics.domains[domain].comments += row.total_comments || 0;
        analytics.domains[domain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);
        analytics.domains[domain].total += row.total_comments || 0;
        if ((row.resolution_status || '').toLowerCase() === 'resolved') {
            analytics.domains[domain].resolved += row.total_comments || 0;
        }

        if (!analytics.resolutions[resolution]) analytics.resolutions[resolution] = { comments: 0 };
        analytics.resolutions[resolution].comments += row.total_comments || 0;
    });

    for (let v in analytics.vendors) {
        const avg = analytics.vendors[v].csatSum / analytics.vendors[v].comments;
        analytics.vendors[v].delta = parseFloat((avg - avgCSAT).toFixed(2));
    }

    for (let p in analytics.painPoints) {
        const data = analytics.painPoints[p];
        const avg = data.csatSum / data.freq;
        data.severity = parseFloat((data.freq * (5 - avg)).toFixed(2));
    }

    return analytics;
}

function buildRecordInfo(tableData, totalComments, avgCSAT, avgNPS) {
    const vendors = new Set(tableData.map(r => r.vendor || 'Unknown')).size;
    const locations = new Set(tableData.map(r => r.location || 'Unknown')).size;
    return {
        totalComments,
        avgCSAT: avgCSAT.toFixed(1),
        avgNPS: avgNPS.toFixed(1),
        vendors,
        locations
    };
}

// New enhanced prompt builder using summary data
function buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS) {
    // Build comprehensive data sections for AI analysis
    const overallSection = overallStats ? `
OVERALL STATISTICS:
- Total Records: ${overallStats.total_records}
- Average CSAT: ${overallStats.overall_avg_csat} (Scale: 1-5)
- Average NPS: ${overallStats.overall_avg_nps} (Scale: 1-5)
- Unique Drivers: ${overallStats.unique_drivers}
- Unique Vendors: ${overallStats.unique_vendors}
- Unique Locations: ${overallStats.unique_locations}
- Sentiment Distribution: ${overallStats.total_positive} Positive (${overallStats.positive_percentage}%), ${overallStats.total_negative} Negative (${overallStats.negative_percentage}%), ${overallStats.total_neutral} Neutral
` : '';

    const topIssuesSection = topIssues && topIssues.length > 0 ? `
TOP ISSUES BY FREQUENCY:
${topIssues.map(issue => `- ${issue.main_driver} > ${issue.sub_driver}: ${issue.frequency} mentions, CSAT ${issue.avg_csat}, NPS ${issue.avg_nps}, ${issue.negative_count} negative`).join('\n')}
` : '';

    const vendorSection = vendorPerformance && vendorPerformance.length > 0 ? `
VENDOR PERFORMANCE ANALYSIS:
${vendorPerformance.map(vendor => `- ${vendor.vendor}: ${vendor.total_comments} comments, CSAT ${vendor.avg_csat}, NPS ${vendor.avg_nps}, ${vendor.negative_feedback} negative (${vendor.negative_percentage}%)`).join('\n')}
` : '';

    // Build detailed summary section
    const detailedSection = tableData && tableData.length > 0 ? `
DETAILED BREAKDOWN BY CATEGORY:
${tableData.slice(0, 20).map(row => `- ${row.main_driver}/${row.sub_driver} (${row.sentiment}): ${row.total_comments} comments, CSAT ${row.csat_score || row.avg_csat}, NPS ${row.nps_score || row.avg_nps}, Vendor: ${row.vendor}, Location: ${row.location}`).join('\n')}
` : '';

    return `You are an expert customer experience analyst. Analyze this comprehensive feedback dataset and provide a detailed executive summary.

${overallSection}
${topIssuesSection}
${vendorSection}
${detailedSection}

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections with actual calculated data
4. Use the provided data structure
5. Maintain professional tone throughout
6. Calculate actual numbers from the data provided above

PART 1: EXECUTIVE SUMMARY
Generate exactly this structure with actual data:

<h3><strong>📊 Executive Summary</strong></h3>
<p><strong>Dataset Overview:</strong></p>
<p>Analyzed customer feedback across with an average CSAT of <strong>${avgCSAT.toFixed(1)}</strong> and NPS of <strong>${avgNPS.toFixed(1)}</strong>. Overall customer satisfaction appears to be [analyze based on scores], with a significant number of comments indicating [analyze sentiment patterns].</p>

<h4><strong>🔍 Top Pain Points:</strong></h4>
<ul>
[List top 5 pain points from the data with actual frequencies and impact]
</ul>

<h4><strong>🎯 Unresolved Agent Lack of empathy, Delayed response, Inadequate resolution:</strong></h4>
<ul>
[Analyze agent-related issues from the data]
</ul>

<h4><strong>⚡ Poor interaction with the agent:</strong></h4>
<ul>
[Analyze interaction quality issues]
</ul>

<h4><strong>🏆 Best Performing Vendor:</strong></h4>
<ul>
[Identify best performing vendor with actual CSAT/NPS scores]
</ul>

<h4><strong>📈 Needs Improvement:</strong></h4>
<ul>
[Identify areas needing improvement with specific metrics]
</ul>

<h4><strong>🎖️ Actionable Recommendations:</strong></h4>
<ul>
[Provide specific recommendations based on the data analysis]
</ul>

<h4><strong>🔄 Priority:</strong></h4>
<ul>
[List priority actions based on frequency and impact]
</ul>

<h4><strong>📊 Impact Patterns:</strong></h4>
<ul>
[Analyze patterns in the data]
</ul>

<h4><strong>🎯 Trend Watch:</strong></h4>
<ul>
[Identify trends from the data]
</ul>

PART 2: DIAGNOSTIC REPORT (Data-Driven ACPT Breakdown)

<h3><strong>📋 Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
[Provide actual summary statistics from the data]
</ul>

<h4><strong>🔍 Top Issues or Praises</strong></h4>
<ul>
[List actual top issues from the frequency data]
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
[Calculate actual counts from the data for Agent, Customer, Process, Technology categories]
</table>

<h4><strong>🔍 ACPT Sub-driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT</th><th>L1 Category</th><th>Sub-driver</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
[Populate with actual data from the detailed breakdown]
</table>

<h4><strong>📊 Data Validation</strong></h4>
<p>Sum of ACPT Table = [Calculate actual sum] [Verify and confirm]</p>
<p>Sum of Sub-driver Table = [Calculate actual sum] [Verify and confirm]</p>
<p>% feedback categories = [Calculate actual percentages]</p>

<h4><strong>🔍 Insight Analysis</strong></h4>
<p>[Provide insights based on actual data patterns, correlations between CSAT/NPS and issues, vendor performance analysis]</p>

<h4><strong>📊 Root Cause Hypothesis</strong></h4>
<p>[Analyze root causes based on the frequency and severity of issues in the data]</p>

<h4><strong>🎯 Trend Watch</strong></h4>
<p>[Identify trends from the actual data provided]</p>

Remember: Use ONLY the data provided above. Calculate actual numbers, don't use placeholders. Ensure all tables contain real data from the analysis.`;
}

function buildEnhancedPrompt(analytics, info, avgCSAT, avgNPS) {
    // Build vendor performance data
    const vendorData = Object.entries(analytics.vendors)
        .map(([vendor, data]) => ({
            vendor,
            comments: data.comments,
            avgCsat: (data.csatSum / data.comments).toFixed(1),
            delta: data.delta
        }))
        .sort((a, b) => b.delta - a.delta);

    // Build pain points data
    const painPointsData = Object.entries(analytics.painPoints)
        .map(([pain, data]) => ({
            pain,
            frequency: data.freq,
            avgCsat: (data.csatSum / data.freq).toFixed(1),
            severity: data.severity
        }))
        .sort((a, b) => b.severity - a.severity);

    // Build domains data
    const domainsData = Object.entries(analytics.domains)
        .map(([domain, data]) => ({
            domain,
            comments: data.comments,
            avgCsat: (data.csatSum / data.comments).toFixed(1),
            resolutionRate: ((data.resolved / data.total) * 100).toFixed(1)
        }))
        .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat));

    return `You are an expert AI feedback analyst. Generate a comprehensive analysis in TWO distinct parts with exact HTML formatting. Follow the structure EXACTLY as specified below.

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections
4. Use the provided data structure
5. Maintain professional tone throughout

DATASET OVERVIEW:
- Total Comments: ${info.totalComments}
- Average CSAT: ${info.avgCSAT} (Scale: 1-5)
- Average NPS: ${info.avgNPS} (Scale: 1-5)
- Vendors Analyzed: ${info.vendors}
- Locations Covered: ${info.locations}

VENDOR PERFORMANCE DATA:
${vendorData.map(v => `- ${v.vendor}: ${v.comments} comments, CSAT ${v.avgCsat}, Delta ${v.delta}`).join('\n')}

PAIN POINTS DATA:
${painPointsData.map(p => `- ${p.pain}: ${p.frequency} mentions, CSAT ${p.avgCsat}, Severity ${p.severity}`).join('\n')}

DOMAINS DATA:
${domainsData.map(d => `- ${d.domain}: ${d.comments} comments, CSAT ${d.avgCsat}, Resolution Rate ${d.resolutionRate}%`).join('\n')}

---

<h3><strong>Part 1: Executive Summary</strong></h3>

<h4><strong>Executive Overview</strong></h4>
<p>Analyzed ${info.totalComments} customer feedback entries with an average CSAT of ${info.avgCSAT} ${parseFloat(info.avgCSAT) >= 4 ? '<span style="color:green;">▲</span>' : '<span style="color:red;">▼</span>'} and NPS of ${info.avgNPS}. [Provide 2-3 sentences of high-level insights about overall customer satisfaction trends]</p>

<h4><strong>Top Pain Points</strong></h4>
<ul>
<li><strong>${painPointsData[0]?.pain || 'Data Processing'}:</strong> ${painPointsData[0]?.frequency || 0} mentions (Severity: ${painPointsData[0]?.severity || 0})</li>
<li><strong>${painPointsData[1]?.pain || 'Service Quality'}:</strong> ${painPointsData[1]?.frequency || 0} mentions (Severity: ${painPointsData[1]?.severity || 0})</li>
<li><strong>${painPointsData[2]?.pain || 'Response Time'}:</strong> ${painPointsData[2]?.frequency || 0} mentions (Severity: ${painPointsData[2]?.severity || 0})</li>
</ul>

<h4><strong>Performance Insights</strong></h4>
<p><strong>Best Performing Vendor:</strong> ${vendorData[0]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[0]?.delta || 0} <span style="color:green;">▲</span>)</p>
<p><strong>Needs Improvement:</strong> ${vendorData[vendorData.length - 1]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[vendorData.length - 1]?.delta || 0} <span style="color:red;">▼</span>)</p>

<h4><strong>Actionable Recommendations</strong></h4>
<ul>
<li><strong>Priority 1:</strong> Address "${painPointsData[0]?.pain || 'top pain point'}" immediately - highest severity score</li>
<li><strong>Priority 2:</strong> Investigate underperforming vendor: ${vendorData[vendorData.length - 1]?.vendor || 'N/A'}</li>
<li><strong>Priority 3:</strong> Implement feedback loop for domains with CSAT below 3.0</li>
</ul>

<h4><strong>Success Patterns</strong></h4>
<p>Vendor "${vendorData[0]?.vendor || 'N/A'}" demonstrates superior performance with ${vendorData[0]?.delta || 0} points above average CSAT. [Analyze what makes this vendor successful]</p>

<h4><strong>Root Cause Hypothesis</strong></h4>
<p>[Provide 2-3 sentences hypothesizing the underlying causes of the top pain points based on the data patterns]</p>

<h4><strong>Trend Watch</strong></h4>
<p>[Highlight 2-3 emerging patterns or risks based on the data distribution and performance metrics]</p>

---

<h3><strong>Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
<li>Total Comments: ${info.totalComments}</li>
<li>Average CSAT: ${info.avgCSAT}</li>
<li>Average NPS: ${info.avgNPS}</li>
<li>Vendors: ${info.vendors}</li>
<li>Locations: ${info.locations}</li>
</ul>

<h4><strong>🔥 Top Issues or Praises</strong></h4>
<p>Most frequently mentioned themes:</p>
<ul>
<li>${painPointsData[0]?.pain || 'Service Issues'} (${painPointsData[0]?.frequency || 0} mentions)</li>
<li>${painPointsData[1]?.pain || 'Process Issues'} (${painPointsData[1]?.frequency || 0} mentions)</li>
<li>${painPointsData[2]?.pain || 'Product Issues'} (${painPointsData[2]?.frequency || 0} mentions)</li>
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
<tr><td>Agent</td><td>Service delays, communication gaps</td><td>[Calculate from data]</td><td>"No callback after escalation"</td></tr>
<tr><td>Customer</td><td>Account issues, verification problems</td><td>[Calculate from data]</td><td>"Couldn't verify with OTP"</td></tr>
<tr><td>Process</td><td>Workflow inefficiencies, delays</td><td>[Calculate from data]</td><td>"Refund process too slow"</td></tr>
<tr><td>Technology</td><td>System errors, technical failures</td><td>[Calculate from data]</td><td>"App keeps crashing"</td></tr>
</table>

<h4><strong>🔍 ACPT Sub-driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT</th><th>L1 Category</th><th>Sub-driver</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
<tr><td>Agent</td><td>Response Time</td><td>Delayed Follow-up</td><td>Negative</td><td>[Calculate]</td><td>"Waiting 3 days for response"</td></tr>
<tr><td>Process</td><td>Refund Process</td><td>Resolution Gaps</td><td>Negative</td><td>[Calculate]</td><td>"Still waiting for refund"</td></tr>
<tr><td>Technology</td><td>App Performance</td><td>System Stability</td><td>Negative</td><td>[Calculate]</td><td>"App crashes frequently"</td></tr>
</table>

<h4><strong>✅ Data Verification</strong></h4>
<ul>
<li>Sum of ACPT Table = ${info.totalComments}? [Verify and confirm]</li>
<li>Sum of Sub-driver Table = ${info.totalComments}? [Verify and confirm]</li>
<li>All feedback categorized? [Confirm 100% coverage]</li>
</ul>

<h4><strong>🎯 Insight Anchors</strong></h4>
<p><strong>Counterintuitive Findings:</strong></p>
<ul>
<li>[Identify unexpected pattern from vendor performance data]</li>
<li>[Highlight surprising correlation between pain points and CSAT]</li>
<li>[Point out domain-specific trends that contradict expectations]</li>
</ul>

REMEMBER: Output BOTH Part 1 AND Part 2 completely. Use provided data to fill in specific numbers where placeholders exist. Maintain HTML formatting exactly as shown.`;
}

// Additional helper function to validate analytics data
function validateAnalyticsData(analytics) {
    // Check if analytics object has required properties
    const requiredProps = ['vendors', 'painPoints', 'domains', 'resolutions'];
    for (let prop of requiredProps) {
        if (!analytics[prop] || typeof analytics[prop] !== 'object') {
            console.warn(`Missing or invalid analytics property: ${prop}`);
            analytics[prop] = {};
        }
    }
    
    // Ensure all vendor objects have required properties
    Object.keys(analytics.vendors).forEach(vendor => {
        const vendorData = analytics.vendors[vendor];
        if (!vendorData.comments) vendorData.comments = 0;
        if (!vendorData.csatSum) vendorData.csatSum = 0;
        if (!vendorData.delta) vendorData.delta = 0;
    });
    
    // Ensure all pain point objects have required properties
    Object.keys(analytics.painPoints).forEach(pain => {
        const painData = analytics.painPoints[pain];
        if (!painData.freq) painData.freq = 0;
        if (!painData.csatSum) painData.csatSum = 0;
        if (!painData.severity) painData.severity = 0;
    });
    
    return analytics;
}

// Updated generateAISummary function with better error handling
async function generateAISummary(tableData) {
    const totalComments = tableData.reduce((sum, row) => sum + (row.total_comments || 0), 0);
    if (totalComments === 0) {
        alert('No feedback data to analyze.');
        return;
    }

    const avgCSAT = tableData.reduce((sum, row) => sum + ((row.avg_csat || 0) * (row.total_comments || 0)), 0) / totalComments;
    const avgNPS = tableData.reduce((sum, row) => sum + ((row.avg_nps || 0) * (row.total_comments || 0)), 0) / totalComments;

    const analytics = validateAnalyticsData(generateAdvancedAnalytics(tableData, totalComments, avgCSAT, avgNPS));
    const recordInfo = buildRecordInfo(tableData, totalComments, avgCSAT, avgNPS);
    const prompt = buildEnhancedPrompt(analytics, recordInfo, avgCSAT, avgNPS);

    // Debug: Log prompt length and preview
    console.log('Prompt length:', prompt.length);
    console.log('Prompt preview:', prompt.substring(0, 500));

    try {
        const response = await fetch('ai_summary.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ prompt })
        });

        const data = await response.json();

        if (data.status === 'error') {
            if (data.message === 'Not logged in') {
                alert('You must be logged in to generate an AI summary.');
            } else {
                console.error('AI Summary Error:', data.message);
                alert('Error generating AI summary. Check console for details.');
            }
            return;
        }

        const summaryHTML = data.summary_html || '<p>No summary generated.</p>';
        
        // Debug: Log the raw response
        console.log('Raw AI Response:', summaryHTML);
        
        document.getElementById('summaryContainer').innerHTML = summaryHTML;

    } catch (error) {
        console.error('Fetch Error:', error);
        alert('Network error or server unavailable.');
    }
}
// Export for Node.js
if (typeof module !== 'undefined') {
    module.exports = { generateAISummary };
}