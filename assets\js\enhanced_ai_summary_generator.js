// Enhanced AI Summary Generator for Tabular Feedback Data
async function generateAISummary(enhancedData) {
    console.log('🚀 Starting AI Summary Generation...');
    console.log('📊 Input enhancedData:', enhancedData);

    // Handle both old and new data structures
    let tableData, overallStats, topIssues, vendorPerformance;

    if (enhancedData && enhancedData.detailed_summary) {
        // New enhanced structure
        console.log('✅ Using new enhanced structure');
        tableData = enhancedData.detailed_summary;
        overallStats = enhancedData.overall_statistics;
        topIssues = enhancedData.top_issues;
        vendorPerformance = enhancedData.vendor_performance;
        console.log('📊 tableData length:', tableData ? tableData.length : 0);
        console.log('📊 overallStats:', overallStats);
        console.log('📊 topIssues length:', topIssues ? topIssues.length : 0);
        console.log('📊 vendorPerformance length:', vendorPerformance ? vendorPerformance.length : 0);
    } else {
        // Backward compatibility with old structure
        console.log('⚠️ Using backward compatibility structure');
        tableData = enhancedData || [];
        overallStats = null;
        topIssues = null;
        vendorPerformance = null;
        console.log('📊 tableData length (compat):', tableData ? tableData.length : 0);
        console.log('📊 tableData type:', typeof tableData);
        console.log('📊 tableData is array:', Array.isArray(tableData));
    }

    if (!Array.isArray(tableData) || tableData.length === 0) {
        console.error('❌ Invalid tableData structure:', tableData);
        return '<p>No data available for summary.</p>';
    }

    const totalComments = overallStats ? overallStats.total_records :
                         tableData.length; // Each row represents one comment

    if (totalComments === 0) {
        return '<p>No feedback data to analyze.</p>';
    }

    const avgCSAT = overallStats && overallStats.overall_avg_csat !== null ?
                   parseFloat(overallStats.overall_avg_csat) :
                   (totalComments > 0 ? tableData.reduce((sum, row) => sum + (parseFloat(row.csat) || 0), 0) / totalComments : 0);
    const avgNPS = overallStats && overallStats.overall_avg_nps !== null ?
                  parseFloat(overallStats.overall_avg_nps) :
                  (totalComments > 0 ? tableData.reduce((sum, row) => sum + (parseFloat(row.nps) || 0), 0) / totalComments : 0);

    console.log('🔧 Building enhanced prompt...');
    const prompt = buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS);
    console.log('📝 Prompt built successfully, length:', prompt ? prompt.length : 0);
    console.log('📝 Prompt preview (first 500 chars):', prompt ? prompt.substring(0, 500) : 'No prompt');

    try {
        console.log('🌐 Sending request to ai_summary.php...');
        const response = await fetch('ai_summary.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ prompt })
        });
        console.log('📡 Response received, status:', response.status);

        const data = await response.json();
        console.log('📄 Response data:', data);

        if (data.status === 'error') {
            console.error('❌ AI Summary Error:', data.message);
            if (data.message === 'Not logged in') {
                alert('You must be logged in to generate an AI summary.');
            } else {
                console.error('AI Summary Error:', data.message);
                alert('Error generating AI summary. Check console for details.');
            }
            return;
        }

        const summaryHTML = data.summary_html || '<p>No summary generated.</p>';

        // Return the HTML instead of trying to set it directly
        // The dashboard code will handle displaying it in aiSummaryContent
        return summaryHTML;

    } catch (error) {
        console.error('Fetch Error:', error);
        alert('Network error or server unavailable.');
    }
}

function generateAdvancedAnalytics(tableData, totalComments, avgCSAT, avgNPS) {
    const analytics = {
        vendors: {},
        painPoints: {},
        domains: {},
        resolutions: {}
    };

    tableData.forEach(row => {
        const vendor = row.vendor || 'Unknown';
        const pain = row.pain_points || 'Unknown';
        const domain = row.domain_category || 'Unknown';
        const resolution = row.resolution_status || 'Unknown';

        if (!analytics.vendors[vendor]) analytics.vendors[vendor] = { comments: 0, csatSum: 0, delta: 0 };
        analytics.vendors[vendor].comments += row.total_comments || 0;
        analytics.vendors[vendor].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

        if (!analytics.painPoints[pain]) analytics.painPoints[pain] = { freq: 0, csatSum: 0, severity: 0 };
        analytics.painPoints[pain].freq += row.total_comments || 0;
        analytics.painPoints[pain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

        if (!analytics.domains[domain]) analytics.domains[domain] = { comments: 0, csatSum: 0, resolved: 0, total: 0 };
        analytics.domains[domain].comments += row.total_comments || 0;
        analytics.domains[domain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);
        analytics.domains[domain].total += row.total_comments || 0;
        if ((row.resolution_status || '').toLowerCase() === 'resolved') {
            analytics.domains[domain].resolved += row.total_comments || 0;
        }

        if (!analytics.resolutions[resolution]) analytics.resolutions[resolution] = { comments: 0 };
        analytics.resolutions[resolution].comments += row.total_comments || 0;
    });

    for (let v in analytics.vendors) {
        const avg = analytics.vendors[v].csatSum / analytics.vendors[v].comments;
        analytics.vendors[v].delta = parseFloat((avg - avgCSAT).toFixed(2));
    }

    for (let p in analytics.painPoints) {
        const data = analytics.painPoints[p];
        const avg = data.csatSum / data.freq;
        data.severity = parseFloat((data.freq * (5 - avg)).toFixed(2));
    }

    return analytics;
}

function buildRecordInfo(tableData, totalComments, avgCSAT, avgNPS) {
    const vendors = new Set(tableData.map(r => r.vendor || 'Unknown')).size;
    const locations = new Set(tableData.map(r => r.location || 'Unknown')).size;
    return {
        totalComments,
        avgCSAT: avgCSAT.toFixed(1),
        avgNPS: avgNPS.toFixed(1),
        vendors,
        locations
    };
}

// ACPT Categorization Functions
function categorizeIntoACPT(tableData) {
    const acptCategories = {
        'Agent': { count: 0, issues: [], samples: [] },
        'Customer': { count: 0, issues: [], samples: [] },
        'Process': { count: 0, issues: [], samples: [] },
        'Technology': { count: 0, issues: [], samples: [] }
    };

    // ACPT categorization rules based on main_driver and sub_driver
    const acptRules = {
        'Agent': [
            'agent', 'staff', 'representative', 'support', 'service', 'communication',
            'response', 'follow', 'callback', 'empathy', 'rude', 'helpful', 'knowledge',
            'training', 'behavior', 'attitude', 'professional'
        ],
        'Customer': [
            'customer', 'user', 'account', 'profile', 'verification', 'authentication',
            'login', 'password', 'otp', 'kyc', 'documentation', 'identity', 'personal'
        ],
        'Process': [
            'process', 'procedure', 'workflow', 'policy', 'refund', 'billing', 'payment',
            'resolution', 'escalation', 'approval', 'delay', 'time', 'duration', 'steps'
        ],
        'Technology': [
            'app', 'website', 'system', 'technical', 'error', 'bug', 'crash', 'loading',
            'server', 'network', 'connectivity', 'feature', 'functionality', 'interface'
        ]
    };

    tableData.forEach(row => {
        const mainDriver = (row.main_driver || '').toLowerCase();
        const subDriver = (row.sub_driver || '').toLowerCase();
        const combinedText = mainDriver + ' ' + subDriver;
        const count = 1; // Each row represents one comment

        let categorized = false;

        // Check each ACPT category
        for (const [category, keywords] of Object.entries(acptRules)) {
            if (keywords.some(keyword => combinedText.includes(keyword))) {
                acptCategories[category].count += count;
                acptCategories[category].issues.push(`${row.main_driver} - ${row.sub_driver}`);

                // Try multiple field names for sample comments
                const sampleComment = row.painpointscustomerfrustrations ||
                                    row.suggestionsforimprovement ||
                                    row.comment ||
                                    row.pain_points ||
                                    row.suggestions ||
                                    row.feedback ||
                                    `${row.main_driver} related issue`;

                if (sampleComment && sampleComment.trim() !== '') {
                    acptCategories[category].samples.push(sampleComment);
                }
                categorized = true;
                break;
            }
        }

        // Default categorization if no match found
        if (!categorized) {
            // Default to Process for unmatched items
            acptCategories['Process'].count += count;
            acptCategories['Process'].issues.push(`${row.main_driver} - ${row.sub_driver}`);

            const sampleComment = row.painpointscustomerfrustrations ||
                                row.suggestionsforimprovement ||
                                row.comment ||
                                row.pain_points ||
                                row.suggestions ||
                                row.feedback ||
                                `${row.main_driver} related issue`;

            if (sampleComment && sampleComment.trim() !== '') {
                acptCategories['Process'].samples.push(sampleComment);
            }
        }
    });

    return acptCategories;
}

function generateACPTTableRows(tableData, overallStats) {
    const acptData = categorizeIntoACPT(tableData);
    let tableRows = '';

    Object.entries(acptData).forEach(([category, data]) => {
        const issueDescription = data.issues.slice(0, 3).join(', ') || 'Various issues';
        const sampleComment = data.samples[0] || `Sample ${category.toLowerCase()} related feedback`;

        tableRows += `<tr>
            <td style="text-align: center; font-weight: bold;">${category}</td>
            <td>${issueDescription}</td>
            <td style="text-align: center;">${data.count}</td>
            <td style="font-style: italic;">"${sampleComment.substring(0, 50)}..."</td>
        </tr>`;
    });

    return tableRows;
}

function generateACPTSubDriverTableRows(tableData, overallStats) {
    let tableRows = '';

    // Group by ACPT category and show top sub-drivers
    const acptData = categorizeIntoACPT(tableData);

    tableData.slice(0, 10).forEach(row => {
        const mainDriver = row.main_driver || 'Unknown';
        const subDriver = row.sub_driver || 'Unknown';
        const sentiment = row.sentiment || 'Neutral';
        const count = 1; // Each row represents one comment

        // Try multiple field names for sample comments
        let sampleComment = row.painpointscustomerfrustrations ||
                           row.suggestionsforimprovement ||
                           row.comment ||
                           row.pain_points ||
                           row.suggestions ||
                           row.feedback ||
                           row.sample_comment ||
                           row.sample_feedback ||
                           '';

        // If no comment found, create a meaningful default
        if (!sampleComment || sampleComment.trim() === '') {
            sampleComment = `${mainDriver} related to ${subDriver}`;
        }

        // Clean and truncate the sample comment
        const sample = sampleComment.toString().trim().substring(0, 50);

        // Determine ACPT category for this row using the same logic as categorizeIntoACPT
        let acptCategory = 'Process'; // default
        const combinedText = (mainDriver + ' ' + subDriver).toLowerCase();

        const acptRules = {
            'Agent': ['agent', 'staff', 'representative', 'support', 'service', 'communication', 'response', 'follow', 'callback', 'empathy'],
            'Customer': ['customer', 'user', 'account', 'profile', 'verification', 'authentication', 'login', 'password', 'otp', 'kyc'],
            'Technology': ['app', 'website', 'system', 'technical', 'error', 'bug', 'crash', 'loading', 'server', 'network']
        };

        for (const [category, keywords] of Object.entries(acptRules)) {
            if (keywords.some(keyword => combinedText.includes(keyword))) {
                acptCategory = category;
                break;
            }
        }

        tableRows += `<tr>
            <td style="text-align: center; font-weight: bold;">${acptCategory}</td>
            <td>${mainDriver}</td>
            <td>${subDriver}</td>
            <td style="text-align: center;">${sentiment}</td>
            <td style="text-align: center;">${count}</td>
            <td style="font-style: italic;">"${sample}..."</td>
        </tr>`;
    });

    return tableRows;
}

function calculateACPTSum(tableData) {
    const acptData = categorizeIntoACPT(tableData);
    return Object.values(acptData).reduce((sum, category) => sum + category.count, 0);
}

// Dimensional analysis functions for LOB, location, partner, team
function getLOBAnalysis(tableData) {
    if (!tableData || tableData.length === 0) return 'No LOB data available';

    const lobData = {};
    tableData.forEach(row => {
        const lob = row.lob || row.LOB || 'Unknown';
        if (!lobData[lob]) lobData[lob] = { count: 0, csatSum: 0, csatCount: 0 };
        const comments = 1; // Each row represents one comment
        const csat = parseFloat(row.csat) || 0;

        lobData[lob].count += comments;
        if (csat > 0) {
            lobData[lob].csatSum += csat;
            lobData[lob].csatCount += comments;
        }
    });

    const results = Object.entries(lobData)
        .filter(([lob, data]) => data.count > 0)
        .sort((a, b) => b[1].count - a[1].count)
        .map(([lob, data]) => {
            const avgCsat = data.csatCount > 0 ? (data.csatSum / data.csatCount).toFixed(1) : 'N/A';
            return `${lob}: ${data.count} comments, Avg CSAT: ${avgCsat}`;
        })
        .slice(0, 5);

    return results.length > 0 ? results.join('; ') : 'No LOB data available';
}

function getLocationAnalysis(tableData) {
    if (!tableData || tableData.length === 0) return 'No location data available';

    const locationData = {};
    tableData.forEach(row => {
        const location = row.location || row.Location || 'Unknown';
        if (!locationData[location]) locationData[location] = { count: 0, csatSum: 0, csatCount: 0 };
        const comments = 1; // Each row represents one comment
        const csat = parseFloat(row.csat) || 0;

        locationData[location].count += comments;
        if (csat > 0) {
            locationData[location].csatSum += csat;
            locationData[location].csatCount += comments;
        }
    });

    const results = Object.entries(locationData)
        .filter(([location, data]) => data.count > 0)
        .sort((a, b) => b[1].count - a[1].count)
        .map(([location, data]) => {
            const avgCsat = data.csatCount > 0 ? (data.csatSum / data.csatCount).toFixed(1) : 'N/A';
            return `${location}: ${data.count} comments, Avg CSAT: ${avgCsat}`;
        })
        .slice(0, 5);

    return results.length > 0 ? results.join('; ') : 'No location data available';
}

function getPartnerAnalysis(tableData) {
    if (!tableData || tableData.length === 0) return 'No partner data available';

    const partnerData = {};
    tableData.forEach(row => {
        const partner = row.partner || row.Partner || 'Unknown';
        if (!partnerData[partner]) partnerData[partner] = { count: 0, csatSum: 0, csatCount: 0 };
        const comments = 1; // Each row represents one comment
        const csat = parseFloat(row.csat) || 0;

        partnerData[partner].count += comments;
        if (csat > 0) {
            partnerData[partner].csatSum += csat;
            partnerData[partner].csatCount += comments;
        }
    });

    const results = Object.entries(partnerData)
        .filter(([partner, data]) => data.count > 0)
        .sort((a, b) => b[1].count - a[1].count)
        .map(([partner, data]) => {
            const avgCsat = data.csatCount > 0 ? (data.csatSum / data.csatCount).toFixed(1) : 'N/A';
            return `${partner}: ${data.count} comments, Avg CSAT: ${avgCsat}`;
        })
        .slice(0, 5);

    return results.length > 0 ? results.join('; ') : 'No partner data available';
}

function getTeamAnalysis(tableData) {
    if (!tableData || tableData.length === 0) return 'No team data available';

    const teamData = {};
    tableData.forEach(row => {
        // Team data is stored in dummy_1 field according to the database structure
        const team = row.dummy_1 || row.team || row.Team || row.vendor || 'Unknown';
        if (!teamData[team]) teamData[team] = { count: 0, csatSum: 0, csatCount: 0 };
        const comments = 1; // Each row represents one comment
        const csat = parseFloat(row.csat) || 0;

        teamData[team].count += comments;
        if (csat > 0) {
            teamData[team].csatSum += csat;
            teamData[team].csatCount += comments;
        }
    });

    const results = Object.entries(teamData)
        .filter(([team, data]) => data.count > 0)
        .sort((a, b) => b[1].count - a[1].count)
        .map(([team, data]) => {
            const avgCsat = data.csatCount > 0 ? (data.csatSum / data.csatCount).toFixed(1) : 'N/A';
            return `${team}: ${data.count} comments, Avg CSAT: ${avgCsat}`;
        })
        .slice(0, 5);

    return results.length > 0 ? results.join('; ') : 'No team data available';
}

// Main Driver and Sub Driver Analysis Functions
function generateDriverAnalysisData(tableData) {
    if (!tableData || tableData.length === 0) return 'No driver data available';

    const driverData = {};

    tableData.forEach(row => {
        const mainDriver = row.main_driver || 'Unknown';
        const subDriver = row.sub_driver || 'Unknown';
        const key = `${mainDriver}|${subDriver}`;

        if (!driverData[key]) {
            driverData[key] = {
                mainDriver,
                subDriver,
                count: 0,
                csatSum: 0,
                csatCount: 0,
                npsSum: 0,
                npsCount: 0,
                sentiments: { Positive: 0, Negative: 0, Neutral: 0 }
            };
        }

        driverData[key].count += 1;

        const csat = parseFloat(row.csat) || 0;
        const nps = parseFloat(row.nps) || 0;
        const sentiment = row.sentiment || 'Neutral';

        if (csat > 0) {
            driverData[key].csatSum += csat;
            driverData[key].csatCount += 1;
        }

        if (nps > 0) {
            driverData[key].npsSum += nps;
            driverData[key].npsCount += 1;
        }

        if (driverData[key].sentiments[sentiment] !== undefined) {
            driverData[key].sentiments[sentiment] += 1;
        }
    });

    const results = Object.values(driverData)
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
        .map(data => {
            const avgCsat = data.csatCount > 0 ? (data.csatSum / data.csatCount).toFixed(1) : 'N/A';
            const avgNps = data.npsCount > 0 ? (data.npsSum / data.npsCount).toFixed(1) : 'N/A';
            const negativePercentage = data.count > 0 ? ((data.sentiments.Negative / data.count) * 100).toFixed(1) : '0';

            return `${data.mainDriver} > ${data.subDriver}: ${data.count} mentions, CSAT ${avgCsat}, NPS ${avgNps}, ${negativePercentage}% negative`;
        });

    return results.length > 0 ? results.join('\n') : 'No driver analysis available';
}

function generateDimensionalAnalysisData(tableData, overallStats) {
    if (!tableData || tableData.length === 0) return 'No dimensional data available';

    const analysis = {
        lob: getLOBAnalysis(tableData),
        location: getLocationAnalysis(tableData),
        partner: getPartnerAnalysis(tableData),
        team: getTeamAnalysis(tableData),
        vendor: getVendorAnalysis(tableData)
    };

    return `
LOB PERFORMANCE:
${analysis.lob}

LOCATION PERFORMANCE:
${analysis.location}

PARTNER PERFORMANCE:
${analysis.partner}

TEAM PERFORMANCE:
${analysis.team}

VENDOR PERFORMANCE:
${analysis.vendor}
    `.trim();
}

function getVendorAnalysis(tableData) {
    if (!tableData || tableData.length === 0) return 'No vendor data available';

    const vendorData = {};
    tableData.forEach(row => {
        const vendor = row.vendor || 'Unknown';
        if (!vendorData[vendor]) vendorData[vendor] = { count: 0, csatSum: 0, csatCount: 0 };
        const comments = 1; // Each row represents one comment
        const csat = parseFloat(row.csat) || 0;

        vendorData[vendor].count += comments;
        if (csat > 0) {
            vendorData[vendor].csatSum += csat;
            vendorData[vendor].csatCount += comments;
        }
    });

    const results = Object.entries(vendorData)
        .filter(([vendor, data]) => data.count > 0)
        .sort((a, b) => b[1].count - a[1].count)
        .map(([vendor, data]) => {
            const avgCsat = data.csatCount > 0 ? (data.csatSum / data.csatCount).toFixed(1) : 'N/A';
            return `${vendor}: ${data.count} comments, Avg CSAT: ${avgCsat}`;
        })
        .slice(0, 5);

    return results.length > 0 ? results.join('; ') : 'No vendor data available';
}

// New enhanced prompt builder using summary data
function buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS) {
    console.log('🔧 Building enhanced prompt with summary data...');
    console.log('📊 Input parameters:');
    console.log('  - tableData length:', tableData ? tableData.length : 0);
    console.log('  - overallStats:', overallStats);
    console.log('  - topIssues length:', topIssues ? topIssues.length : 0);
    console.log('  - vendorPerformance length:', vendorPerformance ? vendorPerformance.length : 0);
    console.log('  - avgCSAT:', avgCSAT);
    console.log('  - avgNPS:', avgNPS);

    // Build comprehensive data sections for AI analysis
    const overallSection = overallStats ? `
OVERALL STATISTICS:
- Total Records: ${overallStats.total_records}
- Average CSAT: ${overallStats.overall_avg_csat} (Scale: 1-5)
- Average NPS: ${overallStats.overall_avg_nps} (Scale: 1-5)
- Unique Drivers: ${overallStats.unique_drivers}
- Unique Vendors: ${overallStats.unique_vendors}
- Unique Locations: ${overallStats.unique_locations}
- Sentiment Distribution: ${overallStats.total_positive} Positive (${overallStats.positive_percentage}%), ${overallStats.total_negative} Negative (${overallStats.negative_percentage}%), ${overallStats.total_neutral} Neutral
` : '';

    const topIssuesSection = topIssues && topIssues.length > 0 ? `
TOP ISSUES BY FREQUENCY:
${topIssues.map(issue => `- ${issue.main_driver} > ${issue.sub_driver}: ${issue.frequency} mentions, CSAT ${issue.avg_csat}, NPS ${issue.avg_nps}, ${issue.negative_count} negative`).join('\n')}
` : '';

    const vendorSection = vendorPerformance && vendorPerformance.length > 0 ? `
VENDOR PERFORMANCE ANALYSIS:
${vendorPerformance.map(vendor => `- ${vendor.vendor}: ${vendor.total_comments} comments, CSAT ${vendor.avg_csat}, NPS ${vendor.avg_nps}, ${vendor.negative_feedback} negative (${vendor.negative_percentage}%)`).join('\n')}
` : '';

    // Build detailed summary section with enhanced dimensions (limit to top 10 for token efficiency)
    const detailedSection = tableData && tableData.length > 0 ? `
DETAILED BREAKDOWN BY CATEGORY (Top 10):
${tableData.slice(0, 10).map(row => `- ${row.main_driver}/${row.sub_driver}: ${row.total_comments} comments, CSAT ${row.csat_score || row.avg_csat}, NPS ${row.nps_score || row.avg_nps}`).join('\n')}
` : '';

    // Build additional dimensional analysis
    console.log('🔍 Generating dimensional analysis...');
    const dimensionalAnalysis = tableData && tableData.length > 0 ? generateDimensionalAnalysisData(tableData, overallStats) : 'No dimensional data available';

    // Build main driver and sub driver analysis
    console.log('🔍 Generating driver analysis...');
    const driverAnalysis = tableData && tableData.length > 0 ? generateDriverAnalysisData(tableData) : 'No driver data available';

    console.log('📊 Dimensional Analysis result:', dimensionalAnalysis.substring(0, 200));
    console.log('📊 Driver Analysis result:', driverAnalysis.substring(0, 200));

    console.log('📊 Final dimensional analysis length:', dimensionalAnalysis.length);
    console.log('📊 Dimensional analysis preview:', dimensionalAnalysis.substring(0, 300));

    // Generate actual ACPT table data
    const acptTableData = categorizeIntoACPT(tableData);
    const acptTableHTML = generateACPTTableRows(tableData, overallStats);
    const acptSubDriverHTML = generateACPTSubDriverTableRows(tableData, overallStats);
    const totalACPTSum = calculateACPTSum(tableData);
    const totalSubDriverSum = tableData.length; // Each row represents one comment

    // Debug logging
    console.log('Table Data Sample:', tableData ? tableData.slice(0, 2) : 'No data');
    console.log('ACPT Table HTML:', acptTableHTML);
    console.log('ACPT Sub-driver HTML:', acptSubDriverHTML);
    console.log('Dimensional Analysis:', dimensionalAnalysis);
    console.log('Total ACPT Sum:', totalACPTSum);
    console.log('Total Sub-driver Sum:', totalSubDriverSum);

    // Debug dimensional analysis functions individually
    if (tableData && tableData.length > 0) {
        console.log('LOB Analysis:', getLOBAnalysis(tableData));
        console.log('Location Analysis:', getLocationAnalysis(tableData));
        console.log('Partner Analysis:', getPartnerAnalysis(tableData));
        console.log('Team Analysis:', getTeamAnalysis(tableData));
    }

    // Log if dimensional analysis is empty
    if (!dimensionalAnalysis || dimensionalAnalysis.trim() === '') {
        console.warn('⚠️ WARNING: Dimensional analysis is empty!');
    } else {
        console.log('✅ Dimensional analysis generated successfully');
    }

    // Final prompt assembly debugging
    console.log('🔧 Assembling final prompt...');
    console.log('📊 Final prompt sections:');
    console.log('  - overallSection length:', overallSection.length);
    console.log('  - topIssuesSection length:', topIssuesSection.length);
    console.log('  - vendorSection length:', vendorSection.length);
    console.log('  - detailedSection length:', detailedSection.length);
    console.log('  - dimensionalAnalysis length:', dimensionalAnalysis.length);
    console.log('  - acptTableHTML length:', acptTableHTML.length);
    console.log('  - acptSubDriverHTML length:', acptSubDriverHTML.length);

    const finalPrompt = `You are an expert AI feedback analyst. Generate a comprehensive analysis in TWO distinct parts with exact HTML formatting. Follow the structure EXACTLY as specified below.

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections
4. Use the provided data structure
5. Maintain professional tone throughout

DATASET OVERVIEW:
- Total Comments: ${overallStats ? overallStats.total_records : tableData.length}
- Average CSAT: ${avgCSAT.toFixed(1)} (Scale: 1-5)
- Average NPS: ${avgNPS.toFixed(1)} (Scale: 1-5)
- Vendors Analyzed: ${overallStats ? overallStats.unique_vendors : 'Multiple'}
- Locations Covered: ${overallStats ? overallStats.unique_locations : 'Multiple'}

${overallSection}
${topIssuesSection}
${vendorSection}
${detailedSection}

DIMENSIONAL ANALYSIS:
${dimensionalAnalysis}

MAIN DRIVER & SUB DRIVER ANALYSIS:
${driverAnalysis}

---

<h3><strong>Part 1: Executive Summary</strong></h3>

<h4><strong>Executive Overview</strong></h4>
<p>Analyzed ${overallStats ? overallStats.total_records : tableData.length} customer feedback entries with an average CSAT of ${avgCSAT.toFixed(1)} ${avgCSAT >= 4 ? '<span style="color:green;">▲</span>' : '<span style="color:red;">▼</span>'} and NPS of ${avgNPS.toFixed(1)}. [Provide 2-3 sentences of high-level insights about overall customer satisfaction trends based on the data above]</p>

<h4><strong>Top Pain Points</strong></h4>
<ul>
<li><strong>Service Experience & Interaction:</strong> [Extract from top issues and driver analysis data above] mentions</li>
<li><strong>Billing & Payment:</strong> [Extract from top issues and driver analysis data above] mentions</li>
<li><strong>Resolution Related:</strong> [Extract from top issues and driver analysis data above] mentions</li>
</ul>

<h4><strong>Performance Insights</strong></h4>
<p><strong>Best Performing Area:</strong> [Analyze from dimensional analysis data above - identify highest performing LOB, location, partner, or vendor]</p>
<p><strong>Needs Improvement:</strong> [Analyze from dimensional analysis data above - identify lowest performing areas]</p>
<p><strong>Driver Impact:</strong> [Analyze from main driver analysis data above - identify most impactful drivers]</p>

<h4><strong>Actionable Recommendations</strong></h4>
<ul>
<li><strong>Priority 1:</strong> [Based on top pain points and dimensional analysis]</li>
<li><strong>Priority 2:</strong> [Based on vendor performance data]</li>
<li><strong>Priority 3:</strong> [Based on CSAT/NPS correlation analysis]</li>
</ul>

<h4><strong>Success Patterns</strong></h4>
<p>[Analyze what areas/vendors/locations are performing well based on the dimensional analysis data]</p>

<h4><strong>Root Cause Hypothesis</strong></h4>
<p>[Provide 2-3 sentences hypothesizing the underlying causes of the top pain points based on the data patterns]</p>

<h4><strong>Trend Watch</strong></h4>
<p>[Highlight 2-3 emerging patterns or risks based on the data distribution and performance metrics]</p>

---

<h3><strong>Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
<li>Total Comments: ${overallStats ? overallStats.total_records : tableData.length}</li>
<li>Average CSAT: ${avgCSAT.toFixed(1)}</li>
<li>Average NPS: ${avgNPS.toFixed(1)}</li>
<li>Vendors: ${overallStats ? overallStats.unique_vendors : 'Multiple'}</li>
<li>Locations: ${overallStats ? overallStats.unique_locations : 'Multiple'}</li>
</ul>

<h4><strong>🔥 Top Issues or Praises</strong></h4>
<p>Most frequently mentioned themes:</p>
<ul>
<li>[Extract from top issues data with actual frequencies]</li>
<li>[Extract from top issues data with actual frequencies]</li>
<li>[Extract from top issues data with actual frequencies]</li>
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
${acptTableHTML}
</table>

<h4><strong>🔍 ACPT-Aligned Sub Driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>L1 Category</th><th>Sub Driver (L2)</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
${acptSubDriverHTML}
</table>

<h4><strong>📈 Dimensional Analysis</strong></h4>
<p><strong>LOB Performance:</strong> [Extract and analyze LOB performance data from the dimensional analysis section above]</p>
<p><strong>Location Insights:</strong> [Extract and analyze location performance data from the dimensional analysis section above]</p>
<p><strong>Partner Performance:</strong> [Extract and analyze partner performance data from the dimensional analysis section above]</p>
<p><strong>Team Analysis:</strong> [Extract and analyze team performance data from the dimensional analysis section above]</p>
<p><strong>Vendor Analysis:</strong> [Extract and analyze vendor performance data from the dimensional analysis section above]</p>

<h4><strong>📊 Main Driver & Sub Driver Analysis</strong></h4>
<p><strong>Top Main Drivers by Impact:</strong> [Extract and analyze the top main drivers from the driver analysis data above]</p>
<p><strong>Critical Sub-Drivers:</strong> [Extract and analyze the critical sub-drivers from the driver analysis data above]</p>
<p><strong>Driver-Sentiment Correlation:</strong> [Analyze the relationship between drivers and sentiment from the data above]</p>

<h4><strong>🔍 Insight Analysis</strong></h4>
<p>[Provide insights based on actual data patterns, correlations between CSAT/NPS and issues, vendor performance analysis]</p>

<h4><strong>📊 Root Cause Hypothesis</strong></h4>
<p>[Analyze root causes based on the frequency and severity of issues in the data]</p>

<h4><strong>🎯 Trend Watch</strong></h4>
<p>[Identify trends from the actual data provided]</p>

<h4><strong>📊 Data Validation</strong></h4>
<p>Total Records Available = ${overallStats ? overallStats.total_records : totalSubDriverSum} records</p>
<p>Coverage = ${overallStats ? '100%' : Math.round((totalACPTSum / totalSubDriverSum) * 100) + '%'}</p>

Remember:
1. Use ONLY the data provided above
2. The ACPT tables are already generated with real data - DO NOT modify them or add template syntax
3. Calculate actual numbers, don't use placeholders
4. MANDATORY: Extract and analyze insights from the DIMENSIONAL ANALYSIS and MAIN DRIVER & SUB DRIVER ANALYSIS sections above
5. Include specific performance metrics from LOB, Location, Partner, Team, and Vendor data in your narrative
6. Analyze the main driver and sub driver patterns, including sentiment correlations and frequency data
7. Ensure all tables contain real data from the analysis
8. DO NOT add any template syntax like \${} to the output
9. The sample comments in ACPT tables should be meaningful, not "No comment available"
10. Focus on actionable insights from both dimensional and driver analysis data
11. Reference specific numbers, percentages, and performance metrics from the provided data`;

    console.log('✅ Final prompt assembled, total length:', finalPrompt.length);
    console.log('📝 Final prompt preview (first 1000 chars):', finalPrompt.substring(0, 1000));

    return finalPrompt;
}

// Helper function for buildEnhancedPrompt ACPT table generation

// Helper function for buildEnhancedPrompt ACPT table generation
function generateACPTTableRowsForPrompt(analytics, info) {
    // Generate ACPT categorization based on analytics data
    const acptData = {
        'Agent': { count: 0, description: 'Service delays, communication gaps', sample: 'No callback after escalation' },
        'Customer': { count: 0, description: 'Account issues, verification problems', sample: 'Couldn\'t verify with OTP' },
        'Process': { count: 0, description: 'Workflow inefficiencies, delays', sample: 'Refund process too slow' },
        'Technology': { count: 0, description: 'System errors, technical failures', sample: 'App keeps crashing' }
    };

    // Distribute analytics data into ACPT categories
    Object.entries(analytics.painPoints).forEach(([pain, data]) => {
        const painLower = pain.toLowerCase();
        if (painLower.includes('agent') || painLower.includes('staff') || painLower.includes('service')) {
            acptData['Agent'].count += data.freq;
        } else if (painLower.includes('customer') || painLower.includes('account') || painLower.includes('user')) {
            acptData['Customer'].count += data.freq;
        } else if (painLower.includes('app') || painLower.includes('system') || painLower.includes('technical')) {
            acptData['Technology'].count += data.freq;
        } else {
            acptData['Process'].count += data.freq;
        }
    });

    let tableRows = '';
    Object.entries(acptData).forEach(([category, data]) => {
        tableRows += `<tr><td>${category}</td><td>${data.description}</td><td>${data.count}</td><td>"${data.sample}"</td></tr>`;
    });

    return tableRows;
}

function generateACPTSubDriverRowsForPrompt(analytics, info) {
    // Generate sub-driver table rows based on analytics data
    const subDriverData = [
        { acpt: 'Agent', l1: 'Response Time', subDriver: 'Delayed Follow-up', sentiment: 'Negative', count: 0, sample: 'Waiting 3 days for response' },
        { acpt: 'Process', l1: 'Refund Process', subDriver: 'Resolution Gaps', sentiment: 'Negative', count: 0, sample: 'Still waiting for refund' },
        { acpt: 'Technology', l1: 'App Performance', subDriver: 'System Stability', sentiment: 'Negative', count: 0, sample: 'App crashes frequently' }
    ];

    // Distribute pain points data into sub-drivers
    Object.entries(analytics.painPoints).forEach(([pain, data]) => {
        const painLower = pain.toLowerCase();
        if (painLower.includes('response') || painLower.includes('delay')) {
            subDriverData[0].count += data.freq;
        } else if (painLower.includes('refund') || painLower.includes('process')) {
            subDriverData[1].count += data.freq;
        } else if (painLower.includes('app') || painLower.includes('system')) {
            subDriverData[2].count += data.freq;
        }
    });

    let tableRows = '';
    subDriverData.forEach(row => {
        tableRows += `<tr><td>${row.acpt}</td><td>${row.l1}</td><td>${row.subDriver}</td><td>${row.sentiment}</td><td>${row.count}</td><td>"${row.sample}"</td></tr>`;
    });

    return tableRows;
}

function buildEnhancedPrompt(analytics, info, avgCSAT, avgNPS) {
    // Build vendor performance data
    const vendorData = Object.entries(analytics.vendors)
        .map(([vendor, data]) => ({
            vendor,
            comments: data.comments,
            avgCsat: (data.csatSum / data.comments).toFixed(1),
            delta: data.delta
        }))
        .sort((a, b) => b.delta - a.delta);

    // Build pain points data
    const painPointsData = Object.entries(analytics.painPoints)
        .map(([pain, data]) => ({
            pain,
            frequency: data.freq,
            avgCsat: (data.csatSum / data.freq).toFixed(1),
            severity: data.severity
        }))
        .sort((a, b) => b.severity - a.severity);

    // Build domains data
    const domainsData = Object.entries(analytics.domains)
        .map(([domain, data]) => ({
            domain,
            comments: data.comments,
            avgCsat: (data.csatSum / data.comments).toFixed(1),
            resolutionRate: ((data.resolved / data.total) * 100).toFixed(1)
        }))
        .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat));

    return `You are an expert AI feedback analyst. Generate a comprehensive analysis in TWO distinct parts with exact HTML formatting. Follow the structure EXACTLY as specified below.

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections
4. Use the provided data structure
5. Maintain professional tone throughout

DATASET OVERVIEW:
- Total Comments: ${info.totalComments}
- Average CSAT: ${info.avgCSAT} (Scale: 1-5)
- Average NPS: ${info.avgNPS} (Scale: 1-5)
- Vendors Analyzed: ${info.vendors}
- Locations Covered: ${info.locations}

VENDOR PERFORMANCE DATA:
${vendorData.map(v => `- ${v.vendor}: ${v.comments} comments, CSAT ${v.avgCsat}, Delta ${v.delta}`).join('\n')}

PAIN POINTS DATA:
${painPointsData.map(p => `- ${p.pain}: ${p.frequency} mentions, CSAT ${p.avgCsat}, Severity ${p.severity}`).join('\n')}

DOMAINS DATA:
${domainsData.map(d => `- ${d.domain}: ${d.comments} comments, CSAT ${d.avgCsat}, Resolution Rate ${d.resolutionRate}%`).join('\n')}

DIMENSIONAL ANALYSIS DATA:
[Dimensional analysis data would be included here]

MAIN DRIVER & SUB DRIVER DATA:
[Main driver and sub driver data would be included here]

---

<h3><strong>Part 1: Executive Summary</strong></h3>

<h4><strong>Executive Overview</strong></h4>
<p>Analyzed ${info.totalComments} customer feedback entries with an average CSAT of ${info.avgCSAT} ${parseFloat(info.avgCSAT) >= 4 ? '<span style="color:green;">▲</span>' : '<span style="color:red;">▼</span>'} and NPS of ${info.avgNPS}. [Provide 2-3 sentences of high-level insights about overall customer satisfaction trends]</p>

<h4><strong>Top Pain Points</strong></h4>
<ul>
<li><strong>${painPointsData[0]?.pain || 'Data Processing'}:</strong> ${painPointsData[0]?.frequency || 0} mentions (Severity: ${painPointsData[0]?.severity || 0})</li>
<li><strong>${painPointsData[1]?.pain || 'Service Quality'}:</strong> ${painPointsData[1]?.frequency || 0} mentions (Severity: ${painPointsData[1]?.severity || 0})</li>
<li><strong>${painPointsData[2]?.pain || 'Response Time'}:</strong> ${painPointsData[2]?.frequency || 0} mentions (Severity: ${painPointsData[2]?.severity || 0})</li>
</ul>

<h4><strong>Performance Insights</strong></h4>
<p><strong>Best Performing Vendor:</strong> ${vendorData[0]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[0]?.delta || 0} <span style="color:green;">▲</span>)</p>
<p><strong>Needs Improvement:</strong> ${vendorData[vendorData.length - 1]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[vendorData.length - 1]?.delta || 0} <span style="color:red;">▼</span>)</p>

<h4><strong>Actionable Recommendations</strong></h4>
<ul>
<li><strong>Priority 1:</strong> Address "${painPointsData[0]?.pain || 'top pain point'}" immediately - highest severity score</li>
<li><strong>Priority 2:</strong> Investigate underperforming vendor: ${vendorData[vendorData.length - 1]?.vendor || 'N/A'}</li>
<li><strong>Priority 3:</strong> Implement feedback loop for domains with CSAT below 3.0</li>
</ul>

<h4><strong>Success Patterns</strong></h4>
<p>Vendor "${vendorData[0]?.vendor || 'N/A'}" demonstrates superior performance with ${vendorData[0]?.delta || 0} points above average CSAT. [Analyze what makes this vendor successful]</p>

<h4><strong>Root Cause Hypothesis</strong></h4>
<p>[Provide 2-3 sentences hypothesizing the underlying causes of the top pain points based on the data patterns]</p>

<h4><strong>Trend Watch</strong></h4>
<p>[Highlight 2-3 emerging patterns or risks based on the data distribution and performance metrics]</p>

---

<h3><strong>Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
<li>Total Comments: ${info.totalComments}</li>
<li>Average CSAT: ${info.avgCSAT}</li>
<li>Average NPS: ${info.avgNPS}</li>
<li>Vendors: ${info.vendors}</li>
<li>Locations: ${info.locations}</li>
</ul>

<h4><strong>🔥 Top Issues or Praises</strong></h4>
<p>Most frequently mentioned themes:</p>
<ul>
<li>${painPointsData[0]?.pain || 'Service Issues'} (${painPointsData[0]?.frequency || 0} mentions)</li>
<li>${painPointsData[1]?.pain || 'Process Issues'} (${painPointsData[1]?.frequency || 0} mentions)</li>
<li>${painPointsData[2]?.pain || 'Product Issues'} (${painPointsData[2]?.frequency || 0} mentions)</li>
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
${generateACPTTableRowsForPrompt(analytics, info)}
</table>

<h4><strong>🔍 ACPT-Aligned Sub Driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>L1 Category</th><th>Sub Driver (L2)</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
${generateACPTSubDriverRowsForPrompt(analytics, info)}
</table>

<h4><strong>📈 Main Driver & Sub Driver Analysis</strong></h4>
<p><strong>Top Main Drivers by Impact:</strong></p>
<ul>
<li>[Analyze top 3 main drivers from the data with frequency and impact]</li>
<li>[Include associated sub-drivers for each main driver]</li>
<li>[Highlight driver-specific trends and correlations with CSAT/NPS]</li>
</ul>

<h4><strong>🌐 Dimensional Analysis</strong></h4>
<p><strong>Performance by Dimensions:</strong></p>
<ul>
<li><strong>Location Analysis:</strong> [Break down performance by location with top/bottom performers]</li>
<li><strong>Vendor Analysis:</strong> [Detailed vendor performance comparison with specific metrics]</li>
<li><strong>Team Analysis:</strong> [Team-specific insights and performance patterns]</li>
<li><strong>Product Type Analysis:</strong> [Product-specific feedback trends and issues]</li>
<li><strong>Channel Analysis:</strong> [Channel-specific performance and customer experience]</li>
</ul>

<p><strong>Cross-Dimensional Correlations:</strong></p>
<ul>
<li>[Identify correlations between location and vendor performance]</li>
<li>[Highlight team-product type combinations with notable patterns]</li>
<li>[Point out dimensional interactions affecting customer satisfaction]</li>
</ul>

<h4><strong>✅ Data Verification</strong></h4>
<ul>
<li>Sum of ACPT Table = ${info.totalComments}? [Verify and confirm]</li>
<li>Sum of Sub-driver Table = ${info.totalComments}? [Verify and confirm]</li>
<li>All feedback categorized? [Confirm 100% coverage]</li>
</ul>

<h4><strong>🎯 Insight Anchors</strong></h4>
<p><strong>Counterintuitive Findings:</strong></p>
<ul>
<li>[Identify unexpected pattern from vendor performance data]</li>
<li>[Highlight surprising correlation between pain points and CSAT]</li>
<li>[Point out domain-specific trends that contradict expectations]</li>
<li>[Reveal dimensional insights that challenge assumptions]</li>
</ul>

REMEMBER: Output BOTH Part 1 AND Part 2 completely. Use provided data to fill in specific numbers where placeholders exist. Maintain HTML formatting exactly as shown.`;
}

// Additional helper function to validate analytics data
function validateAnalyticsData(analytics) {
    // Check if analytics object has required properties
    const requiredProps = ['vendors', 'painPoints', 'domains', 'resolutions'];
    for (let prop of requiredProps) {
        if (!analytics[prop] || typeof analytics[prop] !== 'object') {
            console.warn(`Missing or invalid analytics property: ${prop}`);
            analytics[prop] = {};
        }
    }
    
    // Ensure all vendor objects have required properties
    Object.keys(analytics.vendors).forEach(vendor => {
        const vendorData = analytics.vendors[vendor];
        if (!vendorData.comments) vendorData.comments = 0;
        if (!vendorData.csatSum) vendorData.csatSum = 0;
        if (!vendorData.delta) vendorData.delta = 0;
    });
    
    // Ensure all pain point objects have required properties
    Object.keys(analytics.painPoints).forEach(pain => {
        const painData = analytics.painPoints[pain];
        if (!painData.freq) painData.freq = 0;
        if (!painData.csatSum) painData.csatSum = 0;
        if (!painData.severity) painData.severity = 0;
    });
    
    return analytics;
}

// Helper function to generate dimensional analysis data
function generateDimensionalAnalysisData(tableData, overallStats) {
    if (!tableData || !Array.isArray(tableData)) {
        return 'No dimensional data available';
    }

    const dimensions = {
        locations: {},
        vendors: {},
        teams: {},
        productTypes: {},
        channels: {}
    };

    // Aggregate data by dimensions
    tableData.forEach(row => {
        const location = row.location || 'Unknown';
        const vendor = row.vendor || 'Unknown';
        const team = row.dummy_1 || row.team || 'Unknown';
        const productType = row.product_type || 'Unknown';
        const channel = row.channel || 'Unknown';
        const comments = parseInt(row.total_comments) || 0;
        const csat = parseFloat(row.avg_csat) || 0;
        const nps = parseFloat(row.avg_nps) || 0;

        // Location analysis
        if (!dimensions.locations[location]) {
            dimensions.locations[location] = { comments: 0, csat: 0, nps: 0, count: 0 };
        }
        dimensions.locations[location].comments += comments;
        dimensions.locations[location].csat += csat * comments;
        dimensions.locations[location].nps += nps * comments;
        dimensions.locations[location].count++;

        // Vendor analysis
        if (!dimensions.vendors[vendor]) {
            dimensions.vendors[vendor] = { comments: 0, csat: 0, nps: 0, count: 0 };
        }
        dimensions.vendors[vendor].comments += comments;
        dimensions.vendors[vendor].csat += csat * comments;
        dimensions.vendors[vendor].nps += nps * comments;
        dimensions.vendors[vendor].count++;

        // Team analysis
        if (!dimensions.teams[team]) {
            dimensions.teams[team] = { comments: 0, csat: 0, nps: 0, count: 0 };
        }
        dimensions.teams[team].comments += comments;
        dimensions.teams[team].csat += csat * comments;
        dimensions.teams[team].nps += nps * comments;
        dimensions.teams[team].count++;

        // Product Type analysis
        if (!dimensions.productTypes[productType]) {
            dimensions.productTypes[productType] = { comments: 0, csat: 0, nps: 0, count: 0 };
        }
        dimensions.productTypes[productType].comments += comments;
        dimensions.productTypes[productType].csat += csat * comments;
        dimensions.productTypes[productType].nps += nps * comments;
        dimensions.productTypes[productType].count++;

        // Channel analysis
        if (!dimensions.channels[channel]) {
            dimensions.channels[channel] = { comments: 0, csat: 0, nps: 0, count: 0 };
        }
        dimensions.channels[channel].comments += comments;
        dimensions.channels[channel].csat += csat * comments;
        dimensions.channels[channel].nps += nps * comments;
        dimensions.channels[channel].count++;
    });

    // Calculate averages and format output
    let output = '';

    // Location analysis
    const locationData = Object.entries(dimensions.locations)
        .map(([location, data]) => ({
            location,
            comments: data.comments,
            avgCsat: data.comments > 0 ? (data.csat / data.comments).toFixed(2) : '0.00',
            avgNps: data.comments > 0 ? (data.nps / data.comments).toFixed(2) : '0.00'
        }))
        .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat))
        .slice(0, 5);

    output += 'LOCATION PERFORMANCE:\n';
    locationData.forEach(loc => {
        output += `- ${loc.location}: ${loc.comments} comments, CSAT ${loc.avgCsat}, NPS ${loc.avgNps}\n`;
    });

    // Vendor analysis
    const vendorData = Object.entries(dimensions.vendors)
        .map(([vendor, data]) => ({
            vendor,
            comments: data.comments,
            avgCsat: data.comments > 0 ? (data.csat / data.comments).toFixed(2) : '0.00',
            avgNps: data.comments > 0 ? (data.nps / data.comments).toFixed(2) : '0.00'
        }))
        .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat))
        .slice(0, 5);

    output += '\nVENDOR PERFORMANCE:\n';
    vendorData.forEach(vendor => {
        output += `- ${vendor.vendor}: ${vendor.comments} comments, CSAT ${vendor.avgCsat}, NPS ${vendor.avgNps}\n`;
    });

    // Team analysis
    const teamData = Object.entries(dimensions.teams)
        .map(([team, data]) => ({
            team,
            comments: data.comments,
            avgCsat: data.comments > 0 ? (data.csat / data.comments).toFixed(2) : '0.00',
            avgNps: data.comments > 0 ? (data.nps / data.comments).toFixed(2) : '0.00'
        }))
        .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat))
        .slice(0, 5);

    output += '\nTEAM PERFORMANCE:\n';
    teamData.forEach(team => {
        output += `- ${team.team}: ${team.comments} comments, CSAT ${team.avgCsat}, NPS ${team.avgNps}\n`;
    });

    return output;
}

// Helper function to generate driver analysis data
function generateDriverAnalysisData(tableData) {
    if (!tableData || !Array.isArray(tableData)) {
        return 'No driver data available';
    }

    const drivers = {
        mainDrivers: {},
        subDrivers: {}
    };

    // Aggregate driver data
    tableData.forEach(row => {
        const mainDriver = row.main_driver || 'Unknown';
        const subDriver = row.sub_driver || 'Unknown';
        const comments = parseInt(row.total_comments) || 0;
        const csat = parseFloat(row.avg_csat) || 0;
        const nps = parseFloat(row.avg_nps) || 0;

        // Main driver analysis
        if (!drivers.mainDrivers[mainDriver]) {
            drivers.mainDrivers[mainDriver] = {
                comments: 0,
                csat: 0,
                nps: 0,
                subDrivers: new Set()
            };
        }
        drivers.mainDrivers[mainDriver].comments += comments;
        drivers.mainDrivers[mainDriver].csat += csat * comments;
        drivers.mainDrivers[mainDriver].nps += nps * comments;
        drivers.mainDrivers[mainDriver].subDrivers.add(subDriver);

        // Sub driver analysis
        const driverKey = `${mainDriver} > ${subDriver}`;
        if (!drivers.subDrivers[driverKey]) {
            drivers.subDrivers[driverKey] = { comments: 0, csat: 0, nps: 0 };
        }
        drivers.subDrivers[driverKey].comments += comments;
        drivers.subDrivers[driverKey].csat += csat * comments;
        drivers.subDrivers[driverKey].nps += nps * comments;
    });

    // Format output
    let output = '';

    // Top main drivers
    const mainDriverData = Object.entries(drivers.mainDrivers)
        .map(([driver, data]) => ({
            driver,
            comments: data.comments,
            avgCsat: data.comments > 0 ? (data.csat / data.comments).toFixed(2) : '0.00',
            avgNps: data.comments > 0 ? (data.nps / data.comments).toFixed(2) : '0.00',
            subDriverCount: data.subDrivers.size
        }))
        .sort((a, b) => b.comments - a.comments)
        .slice(0, 5);

    output += 'TOP MAIN DRIVERS:\n';
    mainDriverData.forEach(driver => {
        output += `- ${driver.driver}: ${driver.comments} comments, CSAT ${driver.avgCsat}, ${driver.subDriverCount} sub-drivers\n`;
    });

    // Top sub drivers
    const subDriverData = Object.entries(drivers.subDrivers)
        .map(([driver, data]) => ({
            driver,
            comments: data.comments,
            avgCsat: data.comments > 0 ? (data.csat / data.comments).toFixed(2) : '0.00',
            avgNps: data.comments > 0 ? (data.nps / data.comments).toFixed(2) : '0.00'
        }))
        .sort((a, b) => b.comments - a.comments)
        .slice(0, 8);

    output += '\nTOP SUB DRIVERS:\n';
    subDriverData.forEach(driver => {
        output += `- ${driver.driver}: ${driver.comments} comments, CSAT ${driver.avgCsat}\n`;
    });

    return output;
}

// Export for Node.js
if (typeof module !== 'undefined') {
    module.exports = { generateAISummary };
}