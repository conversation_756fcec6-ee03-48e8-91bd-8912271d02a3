<?php
/**
 * <PERSON><PERSON>t to enqueue missing records by PID
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set high memory and execution time limits
ini_set('memory_limit', '1G');
ini_set('max_execution_time', 3600); // 1 hour

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682cb6387da2d';

// Batch size for processing
$batch_size = 100;

// Maximum records to process
$max_records = isset($_GET['max']) ? intval($_GET['max']) : 2000;

// Log file
$log_file = 'enqueue_by_pid.log';

// Function to log messages
function log_message($message) {
    global $log_file;

    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;

    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);

    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting to enqueue missing records by PID for data_id: $data_id");

    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    log_message("Connected to database");

    // Get counts
    $countQuery = "
        SELECT
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id) as queue_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);

    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $queue_count = $counts['queue_count'];

    log_message("Initial counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, In queue: $queue_count");

    // Get missing records by PID
    log_message("Finding missing records by PID...");

    $missingQuery = "
        SELECT fd.*
        FROM feedback_data fd
        WHERE fd.data_id = :data_id
        AND NOT EXISTS (
            SELECT 1 FROM analyzed_comments ac
            WHERE ac.data_id = fd.data_id AND ac.pid = fd.pid
        )
        AND NOT EXISTS (
            SELECT 1 FROM comment_queue cq
            WHERE cq.data_id = fd.data_id AND cq.pid = fd.pid
        )
        LIMIT :limit
    ";

    $missingStmt = $conn->prepare($missingQuery);
    $missingStmt->bindParam(':data_id', $data_id);
    $missingStmt->bindParam(':limit', $max_records, PDO::PARAM_INT);
    $missingStmt->execute();
    $missingRecords = $missingStmt->fetchAll(PDO::FETCH_ASSOC);

    $total = count($missingRecords);
    log_message("Found $total missing records by PID");

    if ($total == 0) {
        log_message("No missing records found to process");
        exit(0);
    }

    // Process missing records in batches
    $enqueued = 0;
    $failed = 0;

    log_message("Processing $total records in batches of $batch_size...");

    for ($i = 0; $i < $total; $i += $batch_size) {
        $batch = array_slice($missingRecords, $i, $batch_size);
        log_message("Processing batch " . (floor($i / $batch_size) + 1) . " of " . ceil($total / $batch_size));

        foreach ($batch as $record) {
            try {
                // Insert into comment_queue
                $insertQuery = "INSERT INTO comment_queue (
                    comment, data_id, user_id, csat, nps, pid, status,
                    domain_category, resolution_comment, internal_scores, feedback_submit_date,
                    feedback_month, feedback_time, lob, vendor, location, partner,
                    dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
                ) VALUES (
                    :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending',
                    :domain_category, :resolution_comment, :internal_scores, :feedback_submit_date,
                    :feedback_month, :feedback_time, :lob, :vendor, :location, :partner,
                    :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
                )";

                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bindParam(':comment', $record['feedback_data']);
                $insertStmt->bindParam(':data_id', $record['data_id']);
                $insertStmt->bindParam(':user_id', $record['user_id']);
                $insertStmt->bindParam(':csat', $record['csat']);
                $insertStmt->bindParam(':nps', $record['nps']);
                $insertStmt->bindParam(':pid', $record['pid']);
                $insertStmt->bindParam(':domain_category', $record['domain_category']);
                $insertStmt->bindParam(':resolution_comment', $record['resolution_comment']);
                $insertStmt->bindParam(':internal_scores', $record['internal_scores']);
                $insertStmt->bindParam(':feedback_submit_date', $record['feedback_submit_date']);
                $insertStmt->bindParam(':feedback_month', $record['feedback_month']);
                $insertStmt->bindParam(':feedback_time', $record['feedback_time']);
                $insertStmt->bindParam(':lob', $record['lob']);
                $insertStmt->bindParam(':vendor', $record['vendor']);
                $insertStmt->bindParam(':location', $record['location']);
                $insertStmt->bindParam(':partner', $record['partner']);
                $insertStmt->bindParam(':dummy_1', $record['dummy_1']);
                $insertStmt->bindParam(':dummy_2', $record['dummy_2']);
                $insertStmt->bindParam(':dummy_3', $record['dummy_3']);
                $insertStmt->bindParam(':dummy_4', $record['dummy_4']);
                $insertStmt->bindParam(':dummy_5', $record['dummy_5']);

                $result = $insertStmt->execute();

                if ($result) {
                    // Verify the record was actually inserted
                    $verifyQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = :data_id AND pid = :pid";
                    $verifyStmt = $conn->prepare($verifyQuery);
                    $verifyStmt->bindParam(':data_id', $data_id);
                    $verifyStmt->bindParam(':pid', $record['pid']);
                    $verifyStmt->execute();
                    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);

                    if ($verifyResult['count'] > 0) {
                        $enqueued++;
                        if ($enqueued % 10 == 0 || $enqueued == $total) {
                            log_message("Enqueued $enqueued of $total records");
                        }

                        // Add a delay to see if the record stays in the queue
                        usleep(100000); // 0.1 seconds

                        // Check if the record is still in the queue
                        $verifyStmt->execute();
                        $verifyResult2 = $verifyStmt->fetch(PDO::FETCH_ASSOC);

                        if ($verifyResult2['count'] == 0) {
                            log_message("WARNING: Record ID: " . $record['id'] . " was removed from the queue immediately after insertion");

                            // Check if it was processed into analyzed_comments
                            $analyzedQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id AND pid = :pid";
                            $analyzedStmt = $conn->prepare($analyzedQuery);
                            $analyzedStmt->bindParam(':data_id', $data_id);
                            $analyzedStmt->bindParam(':pid', $record['pid']);
                            $analyzedStmt->execute();
                            $analyzedResult = $analyzedStmt->fetch(PDO::FETCH_ASSOC);

                            if ($analyzedResult['count'] > 0) {
                                log_message("Record ID: " . $record['id'] . " was processed and added to analyzed_comments");
                            } else {
                                log_message("WARNING: Record ID: " . $record['id'] . " was removed from the queue but not added to analyzed_comments");
                            }
                        }
                    } else {
                        $failed++;
                        log_message("Failed to verify record ID: " . $record['id'] . " in comment_queue after insertion");
                    }
                } else {
                    $failed++;
                    log_message("Failed to enqueue record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
                }
            } catch (PDOException $e) {
                $failed++;
                log_message("ERROR: Failed to enqueue record ID: " . $record['id'] . " - " . $e->getMessage());
            }
        }

        // Sleep briefly between batches to avoid overloading the database
        if ($i + $batch_size < $total) {
            log_message("Sleeping briefly between batches...");
            sleep(1);
        }
    }

    log_message("Processing completed - Enqueued: $enqueued, Failed: $failed");

    // Get updated counts
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);

    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $queue_count = $counts['queue_count'];

    log_message("Updated counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, In queue: $queue_count");

} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
