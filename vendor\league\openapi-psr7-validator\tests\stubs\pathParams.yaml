openapi: 3.0.2
info:
  title: Weather API
  version: 0.0.1
  contact:
    name: <PERSON>
    url: https://github.com/thephpleague/openapi-psr7-validator
    email: <EMAIL>

paths:
  /optional/params:
    parameters:
      - name: Request-Id
        in: header
        schema:
          type: string
      - name: page
        in: query
        schema:
          type: integer
      - name: session-id
        in: cookie
        schema:
          type: string
    get:
      summary: Read data
      operationId: read
      responses:
        204:
          description: no data
  /users/{group}:
    parameters:
      - name: group
        in: path
        required: true
        schema:
          type: string
          enum:
            - admin
            - user
    get:
      summary: Read data
      operationId: read
      responses:
        200:
          description: good read
          content:
            text/plain:
              schema:
                type: string
  /number/{id}:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    get:
      summary: Read data
      operationId: read-int
      responses:
        204:
          description: No response
  /number/{id}.{format}:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
      - name: format
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Read data
      operationId: read-int-format
      responses:
        204:
          description: No response
  /array/{id}:
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: array
          items:
            type: integer
    get:
      summary: Read data
      operationId: read-array-int
      responses:
        204:
          description: No reponse
  /arrayLabel/{id}:
    parameters:
      - name: id
        in: path
        required: true
        style: label
        schema:
          type: array
          items:
            type: integer
    get:
      summary: Read data
      operationId: read-array-int-label
      responses:
        204:
          description: No reponse
  /arrayLabelExploded/{id}:
    parameters:
      - name: id
        in: path
        required: true
        style: label
        explode: true
        schema:
          type: array
          items:
            type: integer
    get:
      summary: Read data
      operationId: read-array-int-label-explode
      responses:
        204:
          description: No reponse
  /arrayMatrix/{id}:
    parameters:
      - name: id
        in: path
        required: true
        style: matrix
        schema:
          type: array
          items:
            type: integer
    get:
      summary: Read data
      operationId: read-array-int-matrix
      responses:
        204:
          description: No reponse
  /arrayMatrixExploded/{id}:
    parameters:
      - name: id
        in: path
        required: true
        style: matrix
        explode: true
        schema:
          type: array
          items:
            type: integer
    get:
      summary: Read data
      operationId: read-array-int-matrix
      responses:
        204:
          description: No reponse