<?php

session_start();
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit();
}
require_once __DIR__ . '/config.php';
require_once 'DatabaseInteraction.php';
$db = new DatabaseInteraction();



$conn = $db->connect();

$user_id = $_SESSION['user_id'];
$selected_domain = $_GET['domain'] ?? 'All';
$selected_data_id = $_GET['data_id'] ?? 'All';
$processing = isset($_GET['processing']) && $_GET['processing'] === 'true';

$data_ids = $db->getDataIdsByUserId($user_id);
$domain_categories = $db->getUniqueDomainCategories($user_id);
$sentiments = ['Positive', 'Neutral', 'Negative'];

// Initial domain categories based on selected data_id
if ($selected_data_id !== 'All') {
    $domain_categories = $db->getDomainsByDataId($user_id, $selected_data_id);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Sentiment Analysis Dashboard</title>
    <!-- Tailwind CSS -->
    <script src="assets/js/cdn.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Chart.js with date adapter -->
    <script src="assets/js/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.1/dist/aos.js"></script>
     <!-- Load ECharts first -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="assets/js/chartjs-plugin-annotation.js"></script>

    <!-- ECharts WordCloud -->
    <script src="https://cdn.jsdelivr.net/npm/echarts-wordcloud@2.1.0/dist/echarts-wordcloud.min.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">

    <!-- Filter Coordinator -->
    <script src="assets/js/filter-coordinator.js"></script>
	<script src="assets/js/ols-analysis.js"></script>
    <script>
    tailwind.config = {
        darkMode: 'class',
        theme: {
            extend: {
                colors: {
                    positive: {
                        light: '#D1FAE5',
                        DEFAULT: '#10B981',
                        dark: '#059669'
                    },
                    negative: {
                        light: '#FEE2E2',
                        DEFAULT: '#EF4444',
                        dark: '#DC2626'
                    },
                    neutral: {
                        light: '#FEF3C7',
                        DEFAULT: '#F59E0B',
                        dark: '#D97706'
                    },
                    insight: {
                        DEFAULT: '#8B5CF6'
                    },
                    textColor: {
                        primary: '#333',
                        secondary: '#666',
                        light: '#f0f0f0',
                        dark: '#1a202c',
                    }
                }
            }
        }
    }
    </script>

<style>
    body {
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
    }

    .card {
        width: 100%;
        max-width: 100%;
        transition: all 0.3s ease;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .card:hover {
        transform: translateY(-5px);
        box-transform: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .progress-bar {
        height: 12px;
        border-radius: 4px;
        overflow: hidden;
        background-color: #E5E7EB;
    }

    .progress-fill {
        height: 100%;
        transition: width 1.5s ease-out;
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    /* Compact table styles */
    #subDriversTable {
        table-layout: fixed;
        max-width: 100%;
    }

    #subDriversTable th, #subDriversTable td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Improve L2 driver name display */
    #subDriversTable td:nth-child(2) {
        max-width: 200px;
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.2;
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    /* Make L1 driver column more compact */
    #subDriversTable td:nth-child(1) {
        max-width: 150px;
        font-size: 0.8rem;
    }

    .table-container {
        max-height: 500px;
        overflow-y: auto;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }

    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .chart-navigation {
        display: flex;
        gap: 10px;
    }

    .nav-btn {
        background-color: #fef9c3;
        color: black;
        padding: 8px 14px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: background 0.3s ease;
    }

    .nav-btn:hover {
        background-color: #FACC15;
    }

    .chart-container {
        width: 100%;
        height: 390px;
        overflow: hidden;
        position: relative;
        border: 1px solid #ddd;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px;
    }

    .chart-container canvas {
        width: 100% !important;
        height: 100% !important;
        max-height: 350px !important;
        min-height: 300px !important;
    }

    .chart-container::-webkit-scrollbar {
        width: 8px;
    }

    .chart-container::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    .chart-container::-webkit-scrollbar-track {
        background: #f0f0f0;
    }

    .sentiment-wordcloud {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 8px;
        padding: 16px;
    }

    .word {
        padding: 4px 8px;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .word:hover {
        transform: scale(1.1);
    }

    .insight-card {
        border-left: 4px solid #8B5CF6;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .dark-mode-toggle {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 50;
    }

    nav {
        background-color: #b98b04;
        color: white;
        padding: 5px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    nav .logo {
        font-size: 1.5em;
        font-weight: bold;
    }

    nav .nav-right {
        display: flex;
        align-items: center;
    }

    nav .nav-right span {
        margin-right: 10px;
    }

    nav a {
        color: white;
        text-decoration: none;
        font-weight: bold;
    }

    nav a:hover {
        color: #ffcc00;
    }

    #subDriversTable tbody tr:nth-child(odd) {
        background-color: #f9fafb;
    }

    #subDriversTable tbody tr:hover {
        background-color: #f3f4f6;
    }

    #subDriversTable th,
    #subDriversTable td {
        white-space: nowrap;
    }

    #subDriversTable thead th {
        font-weight: 600;
    }

/* Dark Mode Adjustments */
.dark body {
    background: linear-gradient(135deg, #1A202C 0%, #2D3748 100%);
}

.dark .card {
    background: #2D3748;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark .progress-bar {
    background-color: #4A5568;
    color: white;
}

.dark .chart-container {
    border: 1px solid #4A5568;
    color: white;
}

.dark .chart-container::-webkit-scrollbar-thumb {
    background: #6B7280;
    color: white;
}

.dark .chart-container::-webkit-scrollbar-track {
    background: #2D3748;
    color: white;
}

.dark .loading-overlay {
    background: rgba(45, 55, 72, 0.8);
    color: white;
}

.dark nav {
    background-color: #9A7B03;
    color: white;
}

/* ✅ Dark Mode Table Base Background Fix */
.dark #subDriversTable {
    background-color: #2D3748;
    color: white;
    border-color: #4A5568;
}

.dark #subDriversTable tbody {
    background-color: #2D3748;
}

.dark #subDriversTable tbody tr:nth-child(odd) {
    background-color: #4A5568;
    color: white;
}

.dark #subDriversTable tbody tr:nth-child(even) {
    background-color: #2D3748;
    color: white;
}

.dark #subDriversTable tbody tr:hover {
    background-color: #6B7280;
    color: white;
}

/* Dark Mode Table Headings - SPECIFIC TARGETING */
.dark #subDriversTable th,
.dark #subDriversTable thead th {
    color: white;
    background-color: #4A5568;
}

/* Dark Mode Table Data Cells - SPECIFIC TARGETING */
.dark #subDriversTable td {
    color: white;
    border-color: #4A5568;
}

/* Dark Mode Chart Heading Colors - SPECIFIC TARGETING */
.dark .chart-header h1,
.dark .chart-header h2,
.dark .chart-header h3,
.dark .chart-header h4,
.dark .chart-header h5,
.dark .chart-header h6 {
    color: white;
}

/* Dark Mode Specific Elements */
.dark .nav-btn {
    background-color: #4A5568;
    color: white;
}

.dark .nav-btn:hover {
    background-color: #6B7280;
    color: white;
}

.dark .insight-card {
    border-left-color: #8B5CF6;
    color: white;
}

/* Dark Mode Sentiment Metrics */
.dark .progress-fill {
    background-color: white;
}

.dark .positivePercentageText,
.dark .negativePercentageText,
.dark .neutralPercentageText {
    color: white;
}

.dark #positiveChange,
.dark #negativeChange,
.dark #neutralChange {
    color: white;
}

.dark #positivePercentage,
.dark #negativePercentage,
.dark #neutralPercentage {
    color: white;
}

/* Text color classes for sentiment highlighting */
.text-positive {
    color: #10B981;
}

.text-negative {
    color: #EF4444;
}

.text-neutral {
    color: #F59E0B;
}

.dark .text-positive {
    color: #34D399;
}

.dark .text-negative {
    color: #F87171;
}

.dark .text-neutral {
    color: #FBBF24;
}

.filter-section {
  position: sticky; /* Makes the element stick to the viewport */
  top: 0; /* Distance from the top of the viewport */
  background-color: #ffffff; /* Background color for visibility */
  padding: 0;
  border-bottom: 1px solid #e5e7eb;
  z-index: 1000; /* Ensures it stays above other elements */
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.dark .filter-section {
  background-color: #1a202c;
  border-bottom: 1px solid #4a5568;
}


/* Tooltip styles */
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: normal;
  z-index: 1000;
  width: max-content;
  max-width: 300px;
  text-align: center;
  margin-top: 5px;
}

[data-tooltip]:hover::before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
  z-index: 1000;
  margin-top: -5px;
}

/* Custom scrollbar styles for Key Insights Summary */
#keyInsightsSummary::-webkit-scrollbar {
  width: 6px;
}

#keyInsightsSummary::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 16px;
}

#keyInsightsSummary::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 16px;
}

#keyInsightsSummary::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark mode scrollbar styles */
.dark #keyInsightsSummary::-webkit-scrollbar-track {
  background: #2D3748;
}

.dark #keyInsightsSummary::-webkit-scrollbar-thumb {
  background: #4A5568;
}

.dark #keyInsightsSummary::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Word cloud hover styles */
#wordcloud.hovering {
  cursor: pointer;
}

/* Enhanced tooltip styles to ensure visibility and prevent flickering */
.echarts-tooltip {
  transition: none !important;
  pointer-events: all !important;
  z-index: 9999 !important;
  opacity: 1 !important;
  box-shadow: 0 0 20px rgba(52, 152, 219, 0.8) !important;
  border: 3px solid #3498db !important;
  border-radius: 8px !important;
  background-color: rgba(0, 0, 0, 0.95) !important;
}

/* Prevent animation during hover */
#wordcloud.hovering text {
  transition: none !important;
  animation: none !important;
}

/* Add visual indicator for clickable items */
#wordcloud text {
  cursor: pointer !important;
  transition: opacity 0.3s ease, filter 0.3s ease, text-shadow 0.3s ease !important;
}

#wordcloud text:hover {
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8) !important;
}

/* Style for selected verbatim */
#wordcloud text.selected-verbatim {
  text-shadow: 0 0 12px rgba(52, 152, 219, 0.9) !important;
  filter: brightness(1.3) !important;
  opacity: 1 !important;
}

/* Style for non-selected verbatims when one is selected */
#wordcloud.has-selection text:not(.selected-verbatim) {
  opacity: 0.4 !important;
  filter: blur(1px) !important;
}

/* Ensure tooltip content is visible */
.echarts-tooltip-content {
  color: white !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
}

/* Style for tooltip */
.echarts-tooltip {
  z-index: 9999 !important;
  pointer-events: all !important;
  box-shadow: 0 0 20px rgba(52, 152, 219, 0.8) !important;
  border: 3px solid #3498db !important;
  border-radius: 8px !important;
  background-color: rgba(0, 0, 0, 0.95) !important;
  transition: opacity 0.3s ease !important;
}

/* Additional rule to hide tooltips when needed */
.echarts-tooltip.force-hide {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

</style>
</head>
<body class="min-h-screen bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-200">
<!-- Main Loading Indicator -->
	<!--<div id="mainLoadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">-->
	<div id="mainLoadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-40 z-[9999] isolate" style="display: none;">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex flex-col items-center">
            <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-500"></div>
            <span class="mt-4 text-lg font-medium text-gray-700 dark:text-gray-300">Loading Dashboard...</span>
            <span class="mt-2 text-sm text-gray-500 dark:text-gray-400">Please wait while we fetch your data</span>
        </div>
    </div>

    <nav>
        <div class="logo">
            <img src="assets/images/logo.png" alt="Logo" style="max-height: 60px; width: auto; display: block;">
        </div>
        <div class="ml-10 flex items-baseline space-x-4 nav-right">
            <span class="text-white">Hello, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
            <a href="upload.php" class="text-white hover:text-indigo-300">Upload Data</a>
            <a href="logout.php" class="text-white hover:text-indigo-300">Logout</a>
        </div>
    </nav>
    <button class="dark-mode-toggle bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-800 p-3 rounded-full shadow-lg">
        <i class="fas fa-moon dark:hidden"></i>
        <i class="fas fa-sun hidden dark:block"></i>
    </button>
    <div class="container mx-auto px-4 py-4 max-w-7xl">
           <header class="mb-4 text-center" data-aos="fade-down">
            <div class="flex justify-center items-center mb-2">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mr-3">Customer Sentiment Intelligence</h1>
                <div class="inline-flex items-center bg-white dark:bg-gray-700 px-4 py-2 rounded-full shadow-sm">
                <span class="text-gray-600 dark:text-gray-300 text-sm mr-1">Score:</span>
                    <span class="text-xl font-bold text-positive" id="overallSentimentScore">0</span>
                    <span class="ml-1 cursor-help text-gray-500 dark:text-gray-400" data-tooltip="Sentiment Score is calculated as: (Sum of Actual Points) / (Sum of Possible Points) * 100. Actual points are weighted: Positive comments (+1 point each), Neutral comments (+0.5 points each), and Negative comments (-1 point each). Possible points are the total number of comments."><i class="fas fa-info-circle"></i></span>
                </div>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Discover the story behind your customer feedback</p>
            <div class="w-124 h-0.5 bg-gradient-to-r from-positive to-neutral to-negative mx-auto mt-2 rounded-full"></div>

            <?php if ($processing && $selected_data_id !== 'All'): ?>
            <div id="processingIndicator" class="mt-2 bg-yellow-100 text-yellow-800 p-2 rounded-lg shadow-md inline-flex items-center text-sm" data-data-id="<?php echo htmlspecialchars($selected_data_id); ?>">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Processing data for ID: <strong><?php echo htmlspecialchars($selected_data_id); ?></strong>. This may take a few minutes...</span>
            </div>
            <?php endif; ?>
        </header>
		<div class="filter-section dark:bg-[#2D3748] dark:text-white sticky top-0 z-50 bg-white shadow-sm">
        <div class="card mb-4 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up">
    <div class="p-3">
        <div class="flex items-center mb-2">
            <div class="bg-gray-100 dark:bg-gray-700 p-2 rounded-full mr-3">
                <i class="fas fa-filter text-gray-600 dark:text-gray-300"></i>
            </div>
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                Filter Data
                <button id="refreshButton" class="bg-yellow-100 text-black font-bold py-1 px-3 rounded hover:bg-yellow-400 dark:bg-yellow-400 dark:text-black ml-3 text-sm">
                    <i class="fas fa-sync-alt mr-1"></i> Refresh
                </button>
            </h2>
            <div class="ml-auto flex items-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Total Survey Assessed:</span>
                <div id="totalcomments" class="text-gray-600 dark:text-gray-200 text-lg font-bold">0</div>
            </div>
        </div>
        <!-- First Row: Original 3 filters -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
			 <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Select Domain Category:</label>
                <select id="domainCategoryDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Domain Categories</option>
					<?php foreach ($domain_categories as $category): ?>
                        <?php if (is_array($category)): ?>
                            <option value="<?php echo htmlspecialchars($category['domain_category']); ?>"><?php echo htmlspecialchars($category['domain_category']); ?></option>
                        <?php else: ?>
                            <option value="<?php echo htmlspecialchars($category); ?>"><?php echo htmlspecialchars($category); ?></option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Select Data ID:</label>
                <select id="dataIdDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Data IDs</option>
                    <?php foreach ($data_ids as $id): ?>
                        <option value="<?php echo htmlspecialchars($id['data_id']); ?>"><?php echo htmlspecialchars($id['data_id']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Select Sentiment:</label>
                <select id="sentimentDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Sentiments</option>
                    <?php foreach ($sentiments as $sentiment): ?>
                        <option value="<?php echo htmlspecialchars($sentiment); ?>"><?php echo htmlspecialchars($sentiment); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <!-- Second Row: New 5 filters -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Date Range:</label>
                <div class="flex gap-1">
                    <input type="date" id="startDateFilter" class="w-full p-1.5 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white" placeholder="Start Date">
                    <input type="date" id="endDateFilter" class="w-full p-1.5 text-xs border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white" placeholder="End Date">
                </div>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Product Type:</label>
                <select id="productTypeDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Products</option>
                    <!-- Options will be populated dynamically -->
                </select>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Channel Type:</label>
                <select id="channelTypeDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Channels</option>
                    <!-- Options will be populated dynamically -->
                </select>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Teams:</label>
                <select id="teamDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Teams</option>
                    <!-- Options will be populated dynamically -->
                </select>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Resolution Status:</label>
                <select id="resolutionStatusDropdown" class="w-full p-1.5 text-sm border border-gray-300 rounded focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white">
                    <option value="all">All Status</option>
                    <!-- Options will be populated dynamically -->
                </select>
            </div>
        </div>
    </div>
</div>
</div>
 <!-- Key Insights Summary -->
        <div class="card mb-6 p-4" data-aos="fade-up">
            <div class="flex items-start">
                <div class="bg-indigo-100 p-2 rounded-full mr-3">
                    <i class="fas fa-lightbulb text-indigo-600"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 dark:bg-[#2D3748] dark:text-white mb-1">Key Insights Summary</h2>
                    <div id="keyInsightsSummary" class="prose prose-sm text-gray-600 dark:bg-[#2D3748] dark:text-white text-sm max-h-[170px] overflow-y-auto pr-2 pl-1 py-2 border-l-2 border-indigo-100 dark:border-indigo-900 rounded-r">
                        <div class="flex items-center justify-center py-2">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                            <span class="ml-2">Loading insights summary...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <!-- POSITIVE CARD -->
    <div class="card border-t-4 border-positive dark:bg-[#2D3748] dark:text-white" data-aos="fade-up" data-aos-delay="100">
        <div class="p-3">
            <div class="flex justify-between items-start mb-2">
                <div>
                    <h3 class="text-gray-500 dark:text-gray-300 font-medium uppercase tracking-wider text-xs">Positive</h3>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white" id="positiveCount">0</p>
                </div>
                <div class="bg-positive-light dark:bg-green-200 p-2 rounded-full">
                    <i class="fas fa-smile text-positive text-lg"></i>
                </div>
            </div>
            <div class="progress-bar relative h-8">
                <div class="progress-fill bg-positive" id="positiveProgress" style="width: 0%"></div>
                <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs font-semibold text-black dark:text-white" id="positivePercentageText">0%</span>
            </div>
            <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-300">
                <span id="positiveChange">No data</span>
                <span id="positivePercentage">0% of Total</span>
            </div>
        </div>
    </div>

    <!-- NEGATIVE CARD -->
    <div class="card border-t-4 border-negative dark:bg-[#2D3748] dark:text-white" data-aos="fade-up" data-aos-delay="200">
        <div class="p-3">
            <div class="flex justify-between items-start mb-2">
                <div>
                    <h3 class="text-gray-500 dark:text-gray-300 font-medium uppercase tracking-wider text-xs">Negative</h3>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white" id="negativeCount">0</p>
                </div>
                <div class="bg-negative-light dark:bg-red-200 p-2 rounded-full">
                    <i class="fas fa-frown text-negative text-lg"></i>
                </div>
            </div>
            <div class="progress-bar relative h-8">
                <div class="progress-fill bg-negative" id="negativeProgress" style="width: 0%"></div>
                <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs font-semibold text-black dark:text-white" id="negativePercentageText">0%</span>
            </div>
            <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-300">
                <span id="negativeChange">No data</span>
                <span id="negativePercentage">0% of total</span>
            </div>
        </div>
    </div>

    <!-- NEUTRAL CARD -->
    <div class="card border-t-4 border-neutral dark:bg-[#2D3748] dark:text-white" data-aos="fade-up" data-aos-delay="300">
        <div class="p-3">
            <div class="flex justify-between items-start mb-2">
                <div>
                    <h3 class="text-gray-500 dark:text-gray-300 font-medium uppercase tracking-wider text-xs">Neutral</h3>
                    <p class="text-2xl font-bold text-gray-800 dark:text-white" id="neutralCount">0</p>
                </div>
                <div class="bg-neutral-light dark:bg-gray-400 p-2 rounded-full">
                    <i class="fas fa-meh text-neutral text-lg"></i>
                </div>
            </div>
            <div class="progress-bar relative h-8">
                <div class="progress-fill bg-neutral" id="neutralProgress" style="width: 0%"></div>
                <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs font-semibold text-black dark:text-white" id="neutralPercentageText">0%</span>
            </div>
            <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-300">
                <span id="neutralChange">No data</span>
                <span id="neutralPercentage">0% of total</span>
            </div>
        </div>
    </div>
</div>

		<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
    <div class="card lg:col-span-2 dark:bg-[#2D3748] dark:text-white" data-aos="fade-right">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Sentiment Trend by Feedback Response Date</h2>
                <div class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                    <div class="flex space-x-2">
                        <button id="timeRangeAll" class="time-range-btn px-3 py-1 bg-positive-light text-positive rounded-full text-xs font-medium dark:bg-green-200 dark:text-green-800" data-days="all">All</button>
                    </div>
                    <div class="flex space-x-2">
                        <button id="viewDaily" class="view-type-btn px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-xs font-medium dark:bg-blue-800 dark:text-blue-200" data-view="daily">Daily</button>
                        <button id="viewMonthly" class="view-type-btn px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium dark:bg-gray-700 dark:text-white" data-view="monthly">Monthly</button>
                        <button id="viewYearly" class="view-type-btn px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium dark:bg-gray-700 dark:text-white" data-view="yearly">Yearly</button>
                    </div>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="annotatedTimeSeriesChart"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4" id="eventContainer"></div>
        </div>
    </div>

	<div class="card dark:bg-[#2D3748] dark:text-white" data-aos="fade-left">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">Key Sentiment Drivers</h2>
            <div class="space-y-4" id="sentimentDrivers">
                <!-- Dynamic content will be loaded here -->
            </div>
        </div>
    </div>
</div>


<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
    <div class="card dark:bg-[#2D3748] dark:text-white" data-aos="fade-right">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">Sentiment Word Cloud</h2>
            <div class="chart-container">
                <div id="wordcloud" style="width: 100%; height: 400px; position: relative;"></div>
            </div>
            <div class="mt-4 text-sm text-gray-500 dark:text-gray-300">
                <p>Word size indicates frequency in customer feedback. Larger words appeared more frequently. <span class="italic">Hover over any verbatim to see exact mention count and percentage.</span></p>
            </div>
        </div>
    </div>


    <div class="card dark:bg-[#2D3748] dark:text-white" data-aos="fade-left">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">Sentiment Distribution Across Top Drivers</h2>
            <div class="chart-container">
                <canvas id="sentimentsAcrossMainDriversChart"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-2 gap-4"></div>
        </div>
    </div>
</div>




<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
    <!-- L1 Driver Sentiment Card -->
    <div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow" data-aos="fade-right">
        <div class="p-6">
            <div class="chart-header flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">L1 Driver Sentiment</h2>
                <div class="chart-navigation space-x-2">
                    <button id="prevPage" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">← Previous</button>
                    <button id="nextPage" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">Next →</button>
                </div>
            </div>
            <div class="chart-container bg-white dark:bg-gray-800 p-4 rounded">
                <canvas id="mainDriverChart"></canvas>
            </div>
        </div>
    </div>

    <!-- L2 Driver Sentiment Card -->
    <div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow" data-aos="fade-left">
        <div class="p-6">
            <div class="chart-header flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">L2 Driver Sentiment</h2>
                <div class="chart-navigation space-x-2">
                    <button id="prevPage1" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">← Previous</button>
                    <button id="nextPage1" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">Next →</button>
                </div>
            </div>
            <div class="chart-container bg-white dark:bg-gray-800 p-4 rounded">
                <canvas id="subDriverChart"></canvas>
            </div>
        </div>
    </div>
</div>
<!-- LOB and Vendor Sentiment Cards -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
    <!-- LOB Sentiment Card -->
    <div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow" data-aos="fade-right">
        <div class="p-6">
            <div class="chart-header flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Channel Sentiment Distribution</h2>
                <div class="chart-navigation space-x-2">
                    <button id="prevPageLob" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">← Previous</button>
                    <button id="nextPageLob" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">Next →</button>
                </div>
            </div>
            <div class="chart-container bg-white dark:bg-gray-800 p-4 rounded">
                <canvas id="lobChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Vendor Sentiment Card -->
    <div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow" data-aos="fade-left">
        <div class="p-6">
            <div class="chart-header flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Vendor Sentiment Distribution</h2>
                <div class="chart-navigation space-x-2">
                    <button id="prevPageVendor" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">← Previous</button>
                    <button id="nextPageVendor" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">Next →</button>
                </div>
            </div>
            <div class="chart-container bg-white dark:bg-gray-800 p-4 rounded">
                <canvas id="vendorChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Location and Partner Sentiment Cards -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
    <!-- Location Sentiment Card -->
    <div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow" data-aos="fade-right">
        <div class="p-6">
            <div class="chart-header flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Site Sentiment Distribution</h2>
                <div class="chart-navigation space-x-2">
                    <button id="prevPageLocation" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">← Previous</button>
                    <button id="nextPageLocation" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">Next →</button>
                </div>
            </div>
            <div class="chart-container bg-white dark:bg-gray-800 p-4 rounded">
                <canvas id="locationChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Partner Sentiment Card -->
    <div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow" data-aos="fade-left">
        <div class="p-6">
            <div class="chart-header flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Product Type-Sentiment Distribution</h2>
                <div class="chart-navigation space-x-2">
                    <button id="prevPagePartner" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">← Previous</button>
                    <button id="nextPagePartner" class="nav-btn border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white px-3 py-1 rounded">Next →</button>
                </div>
            </div>
            <div class="chart-container bg-white dark:bg-gray-800 p-4 rounded">
                <canvas id="partnerChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Sentiment Distribution Card -->
<div class="card w-full mb-10 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up">
    <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
            Detailed Sentiment Distribution Across Main Drivers
        </h2>
        <div class="table-container overflow-auto">
            <table id="detailedSentimentsTable" class="min-w-full border border-gray-200 dark:border-[#4A5568] text-sm">
                <thead class="bg-gray-100 dark:bg-[#4A5568]">
                    <tr>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Main Driver</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Count</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Positive</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Neutral</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Negative</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Positive %</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Neutral %</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Negative %</th>
                        <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Sentiment Score</th>
                    </tr>
                </thead>
                <tbody id="detailedSentimentsTableBody" class="bg-white dark:bg-[#2D3748] divide-y divide-gray-200 dark:divide-[#4A5568]">
                    <!-- Dynamic content will be loaded here -->
                    <!-- Example Row for context -->
                    <!--
                    <tr>
                        <td class="px-4 py-2 text-gray-900 dark:text-white">Driver 1</td>
                        <td class="px-4 py-2 text-gray-900 dark:text-white">100</td>
                        ...
                    </tr>
                    -->
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- TEMPORARILY HIDDEN: Impact Metrics Section - Remove style="display: none;" to restore -->
<div class="card mb-10 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up" style="display: none;">
    <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-300 mb-6">Impact Metrics</h2>
		<div class="flex justify-between items-center">
            <div>
                <h3 class="text-gray-500 dark:text-gray-300 font-medium uppercase tracking-wider text-sm">🎉 NPS Impact</h3>
                <div id="npsImpact" class="mt-1">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </div>
            <div>
                <h3 class="text-gray-500 dark:text-gray-300 font-medium uppercase tracking-wider text-sm">⭐ CSAT Impact</h3>
                <div id="csatImpact" class="mt-1">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END TEMPORARILY HIDDEN SECTION -->
    <div class="card mb-10 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up">
    <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">Top Positive & Negative L2 Drivers</h2>
        <div class="grid grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-green-700 mb-4">🔼 Most Positive L2 Drivers</h3>
                <table class="min-w-full bg-white dark:bg-[#2D3748] border border-gray-300 dark:border-[#4A5568] text-sm">
                    <thead class="bg-gray-100 dark:bg-[#4A5568]">
                        <tr>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">L2 Driver</th>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Positive Feedback</th>
                        </tr>
                    </thead>
                    <tbody id="topPositiveDriversTable" class="dark:divide-y dark:divide-[#4A5568]">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-red-700 mb-4">🔽 Most Negative L2 Drivers</h3>
                <table class="min-w-full bg-white dark:bg-[#2D3748] border border-gray-300 dark:border-[#4A5568] text-sm">
                    <thead class="bg-gray-100 dark:bg-[#4A5568]">
                        <tr>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">L2 Driver</th>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Negative Feedback</th>
                        </tr>
                    </thead>
                    <tbody id="topNegativeDriversTable" class="dark:divide-y dark:divide-[#4A5568]">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- END TEMPORARILY HIDDEN SECTION -->
<div class="card mb-10 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up">
    <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">L2 Drivers Ranked by CSAT/NPS Impact</h2>
        <div class="grid grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-blue-700 mb-4">📊 Highest CSAT Impact</h3>
                <table class="min-w-full bg-white dark:bg-[#2D3748] border border-gray-300 dark:border-[#4A5568] text-sm">
                    <thead class="bg-gray-100 dark:bg-[#4A5568]">
                        <tr>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">L2 Driver</th>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Average CSAT</th>
                        </tr>
                    </thead>
                    <tbody id="topCsatImpactTable" class="dark:divide-y dark:divide-[#4A5568]">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-blue-700 mb-4">⭐ Highest NPS Impact</h3>
                <table class="min-w-full bg-white dark:bg-[#2D3748] border border-gray-300 dark:border-[#4A5568] text-sm">
                    <thead class="bg-gray-100 dark:bg-[#4A5568]">
                        <tr>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">L2 Driver</th>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Average NPS</th>
                        </tr>
                    </thead>
                    <tbody id="topNpsImpactTable" class="dark:divide-y dark:divide-[#4A5568]">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="card mb-10 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up ">
    <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">Top NPS Promoters & Detractors L2 Drivers</h2>
        <div class="grid grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-green-700 mb-4">🎉 Top NPS Promoters</h3>
                <table class="min-w-full bg-white dark:bg-[#2D3748] border border-gray-300 dark:border-[#4A5568] text-sm">
                    <thead class="bg-gray-100 dark:bg-[#4A5568]">
                        <tr>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">L2 Driver</th>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Promoters</th>
                        </tr>
                    </thead>
                    <tbody id="topNpsPromotersTable" class="dark:divide-y dark:divide-[#4A5568]">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-red-700 mb-4">📉 Top NPS Detractors</h3>
                <table class="min-w-full bg-white dark:bg-[#2D3748] border border-gray-300 dark:border-[#4A5568] text-sm">
                    <thead class="bg-gray-100 dark:bg-[#4A5568]">
                        <tr>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">L2 Driver</th>
                            <th class="px-4 py-2 border-b text-left text-gray-700 dark:text-white">Detractors</th>
                        </tr>
                    </thead>
                    <tbody id="topNpsDetractorsTable" class="dark:divide-y dark:divide-[#4A5568]">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="card mb-10 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up">
    <div class="p-6">
        <div class="flex items-center mb-6">
            <div class="bg-indigo-100 dark:bg-indigo-900 p-3 rounded-full mr-4">
                <i class="fas fa-star text-indigo-600 dark:text-indigo-300"></i>
            </div>
            <div class="flex flex-col">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Top Contributor Driver | Customer Comments</h2>
                <p class="text-sm text-gray-600 dark:text-gray-400" id="topContributorInfo">
                    <!-- Dynamic content will be loaded here -->
                </p>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-green-700 mb-4">🌟 Top 3 Positive Comments</h3>
                <ul id="highlightsList" class="list-disc pl-6 dark:text-white">
                    <!-- Dynamic content will be loaded here -->
                </ul>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-red-700 mb-4">🚨 Top 3 Negative Comments</h3>
                <ul id="lowlightsList" class="list-disc pl-6 dark:text-white">
                    <!-- Dynamic content will be loaded here -->
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- OLS Regression Analysis Section Start-->
<div class="card bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white rounded-lg shadow-sm p-6 mb-6" data-aos="fade-up" data-aos-delay="600">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
            <i class="fas fa-chart-line mr-2 text-insight"></i>
            OLS Regression Analysis
        </h3>
        <button id="runOlsAnalysis" class="bg-insight hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
            <i class="fas fa-play mr-2"></i>
            Run Analysis
        </button>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- CSAT Analysis -->
        <div class="bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white border-t border-gray-200 dark:border-gray-600 rounded-lg p-4 shadow">
            <h4 class="text-md font-semibold text-gray-700 dark:text-white mb-3 flex items-center">
                <i class="fas fa-chart-bar mr-2 text-green-600"></i>
                CSAT Analysis
            </h4>
            <div id="csat-ols-content" class="min-h-64">
                <div class="text-center dark:bg-[#2D3748] text-gray-500 dark:text-gray-400">
                    Click "Run Analysis" to start
                </div>
            </div>
        </div>

        <!-- NPS Analysis -->
        <div class="bg-white text-gray-800 dark:bg-[#2D3748] dark:text-white border-t border-gray-200 dark:border-gray-600 rounded-lg p-4 shadow">
            <h4 class="text-md font-semibold text-gray-700 dark:text-white mb-3 flex items-center">
                <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                NPS Analysis
            </h4>
            <div id="nps-ols-content" class="min-h-64">
                <div class="text-center dark:bg-[#2D3748] text-gray-500 dark:text-gray-400">
                    Click "Run Analysis" to start
                </div>
            </div>
        </div>
    </div>
</div>
<!-- OLS Regression Analysis Section Ends-->
<!-- L2 Drivers Distribution Across L1 Drivers Section Starts-->
<div class="card mb-10 p-6 dark:bg-[#2D3748] dark:text-white" data-aos="fade-up">
    <div class="chart-container2">
        <h5 class="text-lg font-semibold mb-4">L2 Drivers Distribution Across L1 Drivers</h5>

        <label for="mainDriverDropdown" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select L1 Driver:
        </label>

        <select id="mainDriverDropdown" class="w-full p-3 border border-gray-300 dark:border-gray-600 dark:bg-[#1A202C] dark:text-white rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
            <option value="" class="dark:bg-[#1A202C]">Select Main Driver</option>
        </select>

        <div class="table-container mt-6 overflow-x-auto">
            <table id="subDriversTable" class="w-full bg-white dark:bg-[#2D3748] border border-gray-200 dark:border-gray-600 shadow-md text-xs">
                <thead class="bg-yellow-100 dark:bg-yellow-800 border-b border-yellow-300 dark:border-yellow-600">
                    <tr>
                        <th class="px-2 py-2 border-b text-left text-gray-800 dark:text-white" style="width: 15%;">
                            <i class="fas fa-list-ul mr-1"></i> L1 Driver
                        </th>
                        <th class="px-2 py-2 border-b text-left text-gray-800 dark:text-white" style="width: 15%;">
                            <i class="fas fa-list-alt mr-1"></i> L2 Driver
                        </th>
                        <th class="px-2 py-2 border-b text-center text-gray-800 dark:text-white" style="width: 10%;">
                            <i class="fas fa-percentage mr-1"></i> Contribution %
                        </th>
                        <th colspan="3" class="px-2 py-2 border-b text-center text-gray-800 dark:text-white" style="width: 18%;">
                            <i class="fas fa-comment-dots mr-1"></i> Sentiment Counts
                        </th>
                        <th colspan="3" class="px-2 py-2 border-b text-center text-gray-800 dark:text-white" style="width: 18%;">
                            <i class="fas fa-percentage mr-1"></i> Sentiment %
                        </th>
                        <th class="px-2 py-2 border-b text-center text-gray-800 dark:text-white" style="width: 8%;">
                            <i class="fas fa-star mr-1"></i> Avg CSAT
                        </th>
                        <th class="px-2 py-2 border-b text-center text-gray-800 dark:text-white" style="width: 8%;">
                            <i class="fas fa-chart-line mr-1"></i> Avg NPS
                        </th>
                    </tr>
                    <tr>
                        <th colspan="3" class="px-2 py-1 border-b"></th>
                        <th class="px-2 py-1 border-b text-center bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200" style="width: 6%;">Pos</th>
                        <th class="px-2 py-1 border-b text-center bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200" style="width: 6%;">Neu</th>
                        <th class="px-2 py-1 border-b text-center bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200" style="width: 6%;">Neg</th>
                        <th class="px-2 py-1 border-b text-center bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200" style="width: 6%;">Pos %</th>
                        <th class="px-2 py-1 border-b text-center bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200" style="width: 6%;">Neu %</th>
                        <th class="px-2 py-1 border-b text-center bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200" style="width: 6%;">Neg %</th>
                        <th colspan="2" class="px-2 py-1 border-b"></th>
                    </tr>
                </thead>
                <tbody class="dark:divide-y dark:divide-gray-600">
                    <!-- Dynamic content will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- L2 Drivers Distribution Across L1 Drivers Section Ends-->

<!-- NEW: AI-Enhanced Tabular Summary Section -->
<div class="card mb-6 p-4" data-aos="fade-up">
    <div class="flex items-center mb-4">
        <div class="bg-purple-100 p-2 rounded-full mr-3">
            <i class="fas fa-table text-purple-600"></i>
        </div>
        <h2 class="text-lg font-semibold text-gray-800 dark:text-white">AI-Enhanced Tabular Summary</h2>
        <div class="ml-auto flex items-center space-x-2">
            <button id="generateTabularSummaryBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                <i class="fas fa-magic mr-2"></i>Generate Tabular Summary
            </button>
            <button id="generateAISummaryBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hidden">
                <i class="fas fa-robot mr-2"></i>Generate AI Summary
            </button>
        </div>
    </div>
    
    <!-- Tabular Summary Table -->
    <div id="tabularSummaryContainer" class="hidden">
        <div class="overflow-x-auto">
            <table id="tabularSummaryTable" class="w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 shadow-md text-sm">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-3 py-2 border-b text-left text-gray-800 dark:text-white">Main Driver</th>
                        <th class="px-3 py-2 border-b text-left text-gray-800 dark:text-white">Sub Driver</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Sentiment</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Domain</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">LOB</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Vendor</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Location</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Partner</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Team</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Resolution Status</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Pain Points</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Detailed Explanation</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Suggestions</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Verbitm</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Domain Category</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white font-medium">Total Comments</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Avg CSAT</th>
                        <th class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">Avg NPS</th>
                    </tr>
                </thead>
                <tbody id="tabularSummaryTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                    <!-- Dynamic content will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- AI Summary Display -->
    <div id="aiSummaryContainer" class="hidden mt-4">
        <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 p-4 rounded-lg border border-green-200 dark:border-green-700">
            <div class="flex items-center mb-3">
                <i class="fas fa-robot text-green-600 dark:text-green-400 mr-2"></i>
                <h3 class="text-md font-semibold text-gray-800 dark:text-white">AI-Generated Summary</h3>
            </div>
            <div id="aiSummaryContent" class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                <!-- AI summary will be displayed here -->
            </div>
        </div>
    </div>
    
    <!-- Loading States -->
    <div id="tabularLoadingState" class="hidden text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
        <p class="text-gray-600 dark:text-gray-400">Generating tabular summary...</p>
    </div>
    
    <div id="aiLoadingState" class="hidden text-center py-4">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto mb-2"></div>
        <p class="text-gray-600 dark:text-gray-400">Generating AI summary...</p>
    </div>
</div>

                <footer class="text-center text-gray-500 text-sm">
    <div class="inline-flex items-center bg-white px-4 py-2 rounded-full shadow-sm mb-4">
        <i class="fas fa-database text-gray-400 mr-2"></i>
        <span>Analyzing <span id="totalCommentsFooter">0</span> customer feedback points from various sources</span>
        <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full ml-3 hidden">98% data confidence</span>
    </div>
    <p>Data updated: • Next update in 6 hours</p>
    <div class="flex justify-center space-x-4 mt-2">
        <button id="exportReportButton" class="text-positive hover:text-positive-dark flex items-center">
            <i class="fas fa-download mr-1"></i> Export Report
        </button>
        <a href="dashboard-calculations-direct.php" class="text-blue-500 hover:text-blue-700 flex items-center">
            <i class="fas fa-calculator mr-1"></i> Review Dashboard Calculations
        </a>
    </div>
    <div id="exportOptions" class="export-section bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mt-4 hidden">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="bg-blue-100 dark:bg-blue-900 p-2 rounded-full mr-3">
                    <i class="fas fa-download text-blue-600 dark:text-blue-300"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Export Data</h3>
            </div>
            <div class="flex space-x-3">
                <button id="exportCSV" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-file-csv mr-2"></i>Export CSV
                </button>
                <button id="exportExcel" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-file-excel mr-2"></i>Export Excel
                </button>
            </div>
        </div>
        <div class="mt-3 text-sm text-gray-600 dark:text-gray-400" id="exportStatus">
            Export will include all data based on your current filter selections
        </div>
    </div>
    
    <!-- Export Error Display -->
    <?php if (isset($_GET['export_error'])): ?>
    <div id="exportError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mt-4">
        <div class="flex items-center">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Export Error:</strong> <?php echo htmlspecialchars(urldecode($_GET['export_error'])); ?>
        </div>
        <div class="mt-2 text-sm">
            <p>Please try again or contact support if the problem persists.</p>
            <button onclick="document.getElementById('exportError').style.display='none'" class="text-red-600 hover:text-red-800 underline mt-1">
                Dismiss
            </button>
        </div>
    </div>
    <?php endif; ?>
</footer>

    </div>
<script>
AOS.init({
    duration: 1800,
    once: true
});

// Dark Mode Toggle
const darkModeToggle = document.querySelector('.dark-mode-toggle');
darkModeToggle.addEventListener('click', () => {
    document.documentElement.classList.toggle('dark');
    localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
    console.log('Dark mode toggled:', document.documentElement.classList.contains('dark'));
});

// Function to get chart title based on time range and view type
function getChartTitle(days, viewType) {
    if (days === 'all') {
        return `Sentiment Trend (All Time - ${viewType.charAt(0).toUpperCase() + viewType.slice(1)} View)`;
    } else {
        return `Sentiment Trend (Last ${days} Days - ${viewType.charAt(0).toUpperCase() + viewType.slice(1)} View)`;
    }
}

// Function to get axis title based on view type
function getAxisTitle(viewType) {
    switch (viewType) {
        case 'daily': return 'Date';
        case 'monthly': return 'Month';
        case 'yearly': return 'Year';
        default: return 'Date';
    }
}

// Function to format dates properly
function formatDate(dateStr, viewType) {
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr; // Return original if invalid
        }

        switch (viewType) {
            case 'monthly':
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            case 'yearly':
                return date.getFullYear().toString();
            case 'daily':
            default:
                return date.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }).replace(/\//g, '/');
        }
    } catch (e) {
        console.error('Error formatting date:', e);
        return dateStr;
    }
}

// Background processing handler
document.addEventListener('DOMContentLoaded', function() {
    const processingIndicator = document.getElementById('processingIndicator');

    if (processingIndicator) {
        const dataId = processingIndicator.getAttribute('data-data-id');
        console.log('Processing indicator found for data ID:', dataId);

        // Start the background processing
        startBackgroundProcessing(dataId);

        // Set a maximum processing time (60 minutes = 3600000 ms)
        const maxProcessingTime = 3600000;
        const startTime = Date.now();

        // Check processing status every 5 seconds
        const statusCheckInterval = setInterval(function() {
            // Check if we've exceeded the maximum processing time
            if (Date.now() - startTime > maxProcessingTime) {
                console.log('Maximum processing time exceeded, stopping checks');
                clearInterval(statusCheckInterval);

                // Update the processing indicator to show timeout
                if (processingIndicator) {
                    processingIndicator.innerHTML = `
                        <svg class="h-5 w-5 text-yellow-800 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <span>Processing is taking longer than expected for ID: <strong>${dataId}</strong>. Processing will continue in the background.</span>
                    `;
                }

                return;
            }

            checkProcessingStatus(dataId, function(isComplete, statusData) {
                console.log('Processing status check:', statusData);

                if (isComplete) {
                    // Processing is complete, refresh the page without the processing parameter
                    console.log('Processing complete, refreshing page');
                    clearInterval(statusCheckInterval);

                    // Create a new URL without the processing and data_id parameters
                    const url = new URL(window.location.href);
                    url.searchParams.delete('processing');
                    url.searchParams.delete('data_id');

                    // Refresh the page with the clean URL
                    window.location.href = url.toString();
                } else {
                    // Update the processing indicator with progress information
                    if (processingIndicator && statusData) {
                        // Check if there's an error status
                        if (statusData.status === 'error') {
                            let statusText = `Processing data for ID: <strong>${dataId}</strong>. `;
                            statusText += `An error occurred, but processing will continue in the background. `;
                            statusText += `You can refresh the page later to see results.`;

                            // Update the indicator text
                            const textSpan = processingIndicator.querySelector('span');
                            if (textSpan) {
                                textSpan.innerHTML = statusText;
                            }
                            return;
                        }

                        const summaryExists = statusData.summary_exists;
                        const pendingCount = statusData.pending_count;
                        const totalCount = statusData.total_count;
                        const analyzedCount = statusData.analyzed_count;

                        let statusText = `Processing data for ID: <strong>${dataId}</strong>. `;

                        if (summaryExists) {
                            statusText += `Summary generated. `;
                        }

                        if (totalCount > 0) {
                            const progress = Math.round((analyzedCount / totalCount) * 100);
                            statusText += `Analyzing comments: ${analyzedCount}/${totalCount} (${progress}%)`;
                        } else {
                            statusText += `This may take a few minutes...`;
                        }

                        // Update the indicator text
                        const textSpan = processingIndicator.querySelector('span');
                        if (textSpan) {
                            textSpan.innerHTML = statusText;
                        }
                    }
                }
            });
        }, 5000);
    }
});

// Function to start the background processing
function startBackgroundProcessing(dataId) {
    console.log('Starting background processing for data ID:', dataId);

    fetch(`process_data.php?data_id=${dataId}`)
        .then(response => {
            console.log('Background processing response status:', response.status);

            // Check for HTTP errors
            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            // Check if the response is valid JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Response is not valid JSON');
            }
        })
        .then(data => {
            console.log('Background processing started:', data);
        })
        .catch(error => {
            console.error('Error starting background processing:', error);
            // Continue processing even if there's an error
        });
}

// Function to check if processing is complete
function checkProcessingStatus(dataId, callback) {
    console.log('Checking processing status for data ID:', dataId);

    // Check if the summary exists in the database
    fetch(`check_processing.php?data_id=${dataId}`)
        .then(response => {
            console.log('Check processing response status:', response.status);

            // Check for HTTP errors
            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            // Check if the response is valid JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Response is not valid JSON');
            }
        })
        .then(data => {
            console.log('Processing status data:', data);

            // Check if the response has an error status
            if (data && data.status === 'error') {
                console.log('Processing status check returned an error:', data.message);
                // Continue processing even if there's an error
                callback(false, data);
                return;
            }

            // Check if processing is complete
            if (data && data.status === 'complete') {
                callback(true, data);
            } else if (data && data.status === 'processing') {
                // Still processing
                callback(false, data);
            } else {
                // Handle error or unknown status
                console.log('Processing status is not complete or processing:', data);

                // If we've been checking for a while, assume processing is done
                const currentTime = Date.now();
                if (currentTime - startTime > maxProcessingTime / 2) {
                    console.log('Processing has been running for a while, assuming it might be complete');
                    callback(true, data);
                } else {
                    callback(false, data);
                }
            }
        })
        .catch(error => {
            console.error('Error checking processing status:', error);
            // Continue checking even if there's an error
            callback(false, null);
        });
}

// Initialize chart variables
let annotatedTimeSeriesChart;
let sentimentsAcrossMainDriversChart;
let mainDriverChart;
let subDriverChart;
let wordCloudChart;
let lobChart;
let vendorChart;
let locationChart;
let partnerChart;

function setLoading(state) {
    console.log(`Setting loading state to: ${state}`);

    // Disable/enable filter controls during loading
    const filterControls = [
        'domainCategoryDropdown',
        'dataIdDropdown',
        'sentimentDropdown',
        'refreshButton'
    ];

    filterControls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.disabled = state;
            if (state) {
                element.style.opacity = '0.6';
                element.style.cursor = 'not-allowed';
            } else {
                element.style.opacity = '1';
                element.style.cursor = 'pointer';
            }
        }
    });

    // Add loading overlays to chart containers
    document.querySelectorAll('.chart-container').forEach(el => {
        if (state) {
            if (!el.querySelector('.loading-overlay')) {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="flex flex-col items-center justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                        <span class="mt-2 text-sm text-gray-600">Loading data...</span>
                    </div>
                `;
                el.appendChild(overlay);
            }
        } else {
            const overlay = el.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }
    });

    // Show/hide main loading indicator
    const mainLoadingIndicator = document.getElementById('mainLoadingIndicator');
    if (mainLoadingIndicator) {
        mainLoadingIndicator.style.display = state ? 'flex' : 'none';
    }
}

// Performance optimization: Cache for API responses
window.dashboardCache = window.dashboardCache || new Map();
window.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache

function getCacheKey(filters) {
    return JSON.stringify(filters);
}

function getCachedData(cacheKey) {
    const cached = window.dashboardCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < window.cacheTimeout) {
        console.log('Using cached data for:', cacheKey);
        return cached.data;
    }
    return null;
}

function setCachedData(cacheKey, data) {
    window.dashboardCache.set(cacheKey, {
        data: data,
        timestamp: Date.now()
    });

    // Limit cache size to prevent memory issues
    if (window.dashboardCache.size > 50) {
        const firstKey = window.dashboardCache.keys().next().value;
        window.dashboardCache.delete(firstKey);
    }
}

// Debounce function to prevent rapid API calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimized fetchData function with caching and batching
const debouncedFetchData = debounce(fetchData, 300);

function fetchData() {
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;
    const days = window.currentTimeRange || '7';
    const viewType = window.currentViewType || 'daily';

    // Get new filter values
    const startDate = document.getElementById('startDateFilter').value;
    const endDate = document.getElementById('endDateFilter').value;
    const productType = document.getElementById('productTypeDropdown').value;
    const channelType = document.getElementById('channelTypeDropdown').value;
    const team = document.getElementById('teamDropdown').value;
    const resolutionStatus = document.getElementById('resolutionStatusDropdown').value;

    const filters = {
        dataId: dataId || 'all',
        sentiment: sentiment || 'all',
        domain_category: domains || 'all',
        days: days,
        view_type: viewType,
        start_date: startDate || '',
        end_date: endDate || '',
        product_type: productType || 'all',
        channel_type: channelType || 'all',
        team: team || 'all',
        resolution_status: resolutionStatus || 'all'
    };

    console.log('Fetching data with filters:', filters);

    // Update the UI to reflect the current filter state
    document.getElementById('domainCategoryDropdown').value = domains;
    document.getElementById('dataIdDropdown').value = dataId;
    document.getElementById('sentimentDropdown').value = sentiment;
    document.getElementById('startDateFilter').value = startDate;
    document.getElementById('endDateFilter').value = endDate;
    document.getElementById('productTypeDropdown').value = productType;
    document.getElementById('channelTypeDropdown').value = channelType;
    document.getElementById('teamDropdown').value = team;
    document.getElementById('resolutionStatusDropdown').value = resolutionStatus;

    // Check cache first
    const cacheKey = getCacheKey(filters);
    const cachedData = getCachedData(cacheKey);

    if (cachedData) {
        updateAllComponents(cachedData);
        return;
    }

    setLoading(true);

    // Create URLSearchParams object and properly encode domain category
    const params = new URLSearchParams();
    params.append('data_id', dataId || 'all');
    params.append('sentiment', sentiment || 'all');
    params.append('domain_category', domains || 'all');
    params.append('days', days);
    params.append('view_type', viewType);

    // Add new filter parameters
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (productType && productType !== 'all') params.append('product_type', productType);
    if (channelType && channelType !== 'all') params.append('channel_type', channelType);
    if (team && team !== 'all') params.append('team', team);
    if (resolutionStatus && resolutionStatus !== 'all') params.append('resolution_status', resolutionStatus);

    const queryString = params.toString();

    // Performance optimization: Use single batch API call instead of 23 separate calls
    fetchBatchedData(queryString, filters, cacheKey);
}

// New optimized batch data fetching function
function fetchBatchedData(queryString, filters, cacheKey) {
    // Single API call to get all dashboard data
    fetch(`data.php?type=dashboard-batch&${queryString}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(batchData => {
            // Cache the response
            setCachedData(cacheKey, batchData);

            // Update all components
            updateAllComponents(batchData);
        })
        .catch(error => {
            console.error('Error fetching batch data:', error);
            setLoading(false);

            // Fallback to individual API calls if batch fails
            console.log('Falling back to individual API calls...');
            fetchDataFallback(queryString, filters, cacheKey);
        });
}

// Fallback function for individual API calls
function fetchDataFallback(queryString, filters, cacheKey) {
    // Track successful requests
    let successfulRequests = 0;
    const totalRequests = 23;

    const handleResponse = (response, errorMessage) => {
        if (!response.ok) {
            throw new Error(errorMessage);
        }
        successfulRequests++;
        return response.json();
    };

    Promise.all([
        fetch(`data.php?type=comments&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch comments data')),
        fetch(`data.php?type=sentiments&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch sentiments data')),
        fetch(`data.php?type=historical-sentiments&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch historical sentiments')),
        fetch(`data.php?type=time-series-sentiments&${queryString}&days=${window.currentTimeRange || 7}`)
            .then(r => handleResponse(r, 'Failed to fetch time series data')),
        fetch(`data.php?type=main-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch main drivers')),
        fetch(`data.php?type=sentiments-across-main-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch sentiments across drivers')),
        fetch(`data.php?type=word-cloud&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch word cloud data')),
        fetch(`data.php?type=main-drivers-sentiment&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch main drivers sentiment')),
        fetch(`data.php?type=sub-drivers-sentiment&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch sub drivers sentiment')),
        fetch(`data.php?type=detailed-sentiments&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch detailed sentiments')),
        fetch(`data.php?type=top-positive-sub-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch top positive drivers')),
        fetch(`data.php?type=top-negative-sub-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch top negative drivers')),
        fetch(`data.php?type=top-csat-impact-sub-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch CSAT impact drivers')),
        fetch(`data.php?type=top-nps-impact-sub-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch NPS impact drivers')),
        fetch(`data.php?type=top-nps-promoter-sub-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch NPS promoter drivers')),
        fetch(`data.php?type=top-nps-detractor-sub-drivers&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch NPS detractor drivers')),
        fetch(`data.php?type=feedback-comments-summary&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch feedback summary')),
        fetch(`data.php?type=csat-impact&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch CSAT impact')),
        fetch(`data.php?type=nps-impact&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch NPS impact')),
        fetch(`data.php?type=lob-sentiment&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch LOB sentiment data')),
        fetch(`data.php?type=vendor-sentiment&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch Vendor sentiment data')),
        fetch(`data.php?type=location-sentiment&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch Location sentiment data')),
        fetch(`data.php?type=partner-sentiment&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch Partner sentiment data')),
        fetch(`data.php?type=consolidated-summary&${queryString}`)
            .then(r => handleResponse(r, 'Failed to fetch consolidated summary'))
    ])
    .then(dataResponses => {
        const batchData = {
            commentsData: dataResponses[0],
            sentimentsData: dataResponses[1],
            historicalSentimentsData: dataResponses[2],
            timeSeriesData: dataResponses[3],
            mainDriversData: dataResponses[4],
            sentimentsAcrossDriversData: dataResponses[5],
            wordCloudData: dataResponses[6],
            mainDriversSentimentData: dataResponses[7],
            subDriversSentimentData: dataResponses[8],
            detailedSentimentsData: dataResponses[9],
            topPositiveSubDrivers: dataResponses[10],
            topNegativeSubDrivers: dataResponses[11],
            topCsatImpactSubDrivers: dataResponses[12],
            topNpsImpactSubDrivers: dataResponses[13],
            topNpsPromoterSubDrivers: dataResponses[14],
            topNpsDetractorSubDrivers: dataResponses[15],
            feedbackCommentsSummary: dataResponses[16],
            csatImpactData: dataResponses[17],
            npsImpactData: dataResponses[18],
            lobSentimentData: dataResponses[19],
            vendorSentimentData: dataResponses[20],
            locationSentimentData: dataResponses[21],
            partnerSentimentData: dataResponses[22],
            consolidatedSummaryData: dataResponses[23]
        };

        // Cache the response
        setCachedData(cacheKey, batchData);

        // Update all components
        updateAllComponents(batchData);
    })
    .catch(error => {
        console.error('Error fetching or updating data:', error);
        setLoading(false);
        // Show error message to user
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative';
        errorDiv.innerHTML = `
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">${error.message}</span>
        `;
        document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.container').firstChild);
        setTimeout(() => errorDiv.remove(), 5000);
    });
}

// Optimized function to update all dashboard components
function updateAllComponents(data) {
    console.log('Updating all dashboard components with data:', data);

    // Update UI components with fallbacks for missing data
    updateBasicMetrics(
        data.commentsData || { total: 0 },
        data.sentimentsData || { Positive: 0, Negative: 0, Neutral: 0 },
        data.historicalSentimentsData || { Positive: 0, Negative: 0, Neutral: 0 }
    );

    updateCharts(
        data.timeSeriesData || [],
        data.mainDriversData || {},
        data.sentimentsAcrossDriversData || {},
        data.wordCloudData || [],
        data.mainDriversSentimentData || {},
        data.subDriversSentimentData || {}
    );

    updateDetailedSentimentsTable(data.detailedSentimentsData || []);
    updateTopPositiveNegativeDrivers(data.topPositiveSubDrivers || [], data.topNegativeSubDrivers || []);
    updateCsatNpsImpactDrivers(data.topCsatImpactSubDrivers || [], data.topNpsImpactSubDrivers || []);
    updateNpsPromoterDetractorDrivers(data.topNpsPromoterSubDrivers || [], data.topNpsDetractorSubDrivers || []);
    updateFeedbackCommentsSummary(data.feedbackCommentsSummary || { highlights: [], lowlights: [], keywords: [] });
    updateCsatNpsImpactSummary(
        data.csatImpactData || { csat_impact: 0, positive_factors: [], negative_factors: [] },
        data.npsImpactData || { nps_impact: 0, positive_factors: [], negative_factors: [] }
    );

    // Handle summary data
    const dataId = document.getElementById('dataIdDropdown').value;
    if (dataId !== 'all' && data.consolidatedSummaryData && !data.consolidatedSummaryData.is_consolidated) {
        updateKeyInsightsSummary(data.consolidatedSummaryData);
    } else {
        updateKeyInsightsSummary(data.consolidatedSummaryData || { summary: "No summary available", is_consolidated: true });
    }

    // Validate and update sentiment charts
    const validLobData = data.lobSentimentData && typeof data.lobSentimentData === 'object' && !data.lobSentimentData.error ? data.lobSentimentData : {};
    const validVendorData = data.vendorSentimentData && typeof data.vendorSentimentData === 'object' && !data.vendorSentimentData.error ? data.vendorSentimentData : {};
    const validLocationData = data.locationSentimentData && typeof data.locationSentimentData === 'object' && !data.locationSentimentData.error ? data.locationSentimentData : {};
    const validPartnerData = data.partnerSentimentData && typeof data.partnerSentimentData === 'object' && !data.partnerSentimentData.error ? data.partnerSentimentData : {};

    // Update charts with validated data
    updateLobSentimentChart(validLobData);
    updateVendorSentimentChart(validVendorData);
    updateLocationSentimentChart(validLocationData);
    updatePartnerSentimentChart(validPartnerData);

    setLoading(false);
    console.log('All UI components updated successfully');
}

function updateBasicMetrics(commentsData, sentimentsData, historicalSentimentsData) {
    console.log('Updating basic metrics...');
    console.log('Comments data:', commentsData);
    console.log('Sentiments data:', sentimentsData);
    console.log('Historical sentiments data:', historicalSentimentsData);

    document.getElementById('totalcomments').textContent = commentsData.total || 0;
    document.getElementById('totalCommentsFooter').textContent = commentsData.total || 0;

    const positiveCount = sentimentsData.Positive || 0;
    const negativeCount = sentimentsData.Negative || 0;
    const neutralCount = sentimentsData.Neutral || 0;

    const historicalPositiveCount = historicalSentimentsData?.Positive || 0;
    const historicalNegativeCount = historicalSentimentsData?.Negative || 0;
    const historicalNeutralCount = historicalSentimentsData?.Neutral || 0;

    const totalSentiments = positiveCount + negativeCount + neutralCount;
    window.totalSentiments = totalSentiments; // Store total sentiments globally

    document.getElementById('positiveCount').textContent = positiveCount;
    document.getElementById('negativeCount').textContent = negativeCount;
    document.getElementById('neutralCount').textContent = neutralCount;

    const positiveWidth = totalSentiments > 0 ? (positiveCount / totalSentiments * 100).toFixed(2) : 0;
    const negativeWidth = totalSentiments > 0 ? (negativeCount / totalSentiments * 100).toFixed(2) : 0;
    const neutralWidth = totalSentiments > 0 ? (neutralCount / totalSentiments * 100).toFixed(2) : 0;

    document.getElementById('positiveProgress').style.width = `${positiveWidth}%`;
    document.getElementById('negativeProgress').style.width = `${negativeWidth}%`;
    document.getElementById('neutralProgress').style.width = `${neutralWidth}%`;

    document.getElementById('positivePercentageText').textContent = `${positiveWidth}%`;
    document.getElementById('negativePercentageText').textContent = `${negativeWidth}%`;
    document.getElementById('neutralPercentageText').textContent = `${neutralWidth}%`;

    document.getElementById('positivePercentage').textContent = `${positiveWidth}% of total`;
    document.getElementById('negativePercentage').textContent = `${negativeWidth}% of total`;
    document.getElementById('neutralPercentage').textContent = `${neutralWidth}% of total`;

    // Calculate weighted actual points
    const actualPoints = (positiveCount * 1) + (neutralCount * 0.5) + (negativeCount * -1);

    // Calculate possible points (if all were positive)
    const possiblePoints = totalSentiments;

    // Calculate sentiment score as (Sum of Actual Points) / (Sum of Possible Points) * 100
    let sentimentScore = 0;
    if (possiblePoints > 0) {
        sentimentScore = ((actualPoints / possiblePoints) * 100).toFixed(2);
    }

    // Set the sentiment score text
    const overallScoreElement = document.getElementById('overallSentimentScore');
    overallScoreElement.textContent = sentimentScore + '%';

    // Set the color based on the score
    if (parseFloat(sentimentScore) >= 25) {
        overallScoreElement.className = 'text-xl font-bold text-positive'; // Green for positive
    } else if (parseFloat(sentimentScore) >= 0) {
        overallScoreElement.className = 'text-xl font-bold text-neutral'; // Yellow for neutral
    } else if (parseFloat(sentimentScore) >= -25) {
        overallScoreElement.className = 'text-xl font-bold text-warning'; // Orange for warning
    } else {
        overallScoreElement.className = 'text-xl font-bold text-negative'; // Red for negative
    }

    // Calculate comparison values
    const positiveChange = positiveCount - historicalPositiveCount;
    const negativeChange = negativeCount - historicalNegativeCount;
    const neutralChange = neutralCount - historicalNeutralCount;

    document.getElementById('positiveChange').textContent = positiveChange >= 0 ? `+${positiveChange}` : `${positiveChange}`;
    document.getElementById('negativeChange').textContent = negativeChange >= 0 ? `+${negativeChange}` : `${negativeChange}`;
    document.getElementById('neutralChange').textContent = neutralChange >= 0 ? `+${neutralChange}` : `${neutralChange}`;

    console.log('Overall Sentiment Score:', document.getElementById('overallSentimentScore').textContent);
}

function updateCharts(timeSeriesData, mainDriversData, sentimentsAcrossDriversData,
                     wordCloudData, mainDriversSentimentData, subDriversSentimentData) {
    // Default to 7 days for time series
    window.currentTimeRange = window.currentTimeRange || 7;
    updateTimeSeriesChart(timeSeriesData, window.currentTimeRange);
    updateMainDriversChart(mainDriversData, sentimentsAcrossDriversData);
    updateWordCloud(wordCloudData);
    updateMainDriverSentimentChart(mainDriversSentimentData);
    updateSubDriverSentimentChart(subDriversSentimentData);
}

function updateTimeSeriesChart(data, days = 7, viewType = 'daily') {
    console.log('Updating time series chart with data:', data, 'days:', days, 'viewType:', viewType);

    // Check if data is empty or null
    if (!data || (typeof data === 'object' && Object.keys(data).length === 0) ||
        (Array.isArray(data) && data.length === 0)) {
        console.warn('No time series data available');
        const ctx = document.getElementById('annotatedTimeSeriesChart');
        if (ctx) {
            ctx.style.display = 'none';

            // Avoid duplicating messages
            if (!ctx.parentNode.querySelector('.no-data-message')) {
                const message = document.createElement('div');
                message.className = 'no-data-message text-gray-500 dark:text-gray-400 text-center py-4';
                message.textContent = 'No timeline data available for the selected filters';
                ctx.parentNode.insertBefore(message, ctx.nextSibling);
            }
        }
        return;
    }

    // Show chart if it was hidden
    const ctx = document.getElementById('annotatedTimeSeriesChart');
    if (ctx) {
        ctx.style.display = 'block';
        const message = ctx.parentNode.querySelector('.no-data-message');
        if (message) message.remove();
    }

    // Process data based on format
    let processedData = [];

    // If data is an object with date keys (from PHP)
    if (!Array.isArray(data) && typeof data === 'object') {
        processedData = Object.entries(data).map(([date, values]) => ({
            date: date,
            displayDate: values.display_date || formatDate(date, viewType),
            Positive: parseInt(values.Positive) || 0,
            Negative: parseInt(values.Negative) || 0,
            Neutral: parseInt(values.Neutral) || 0
        }));
    }
    // If data is an array of objects
    else if (Array.isArray(data)) {
        processedData = data.map(item => ({
            date: item.date,
            displayDate: item.display_date || formatDate(item.date, viewType),
            Positive: parseInt(item.Positive) || 0,
            Negative: parseInt(item.Negative) || 0,
            Neutral: parseInt(item.Neutral) || 0
        }));
    }

    // If we still have no processed data, show a message
    if (processedData.length === 0) {
        console.warn('No valid time series data found');
        const ctx = document.getElementById('annotatedTimeSeriesChart');
        if (ctx) {
            ctx.style.display = 'none';
            if (!ctx.parentNode.querySelector('.no-data-message')) {
                const message = document.createElement('div');
                message.className = 'no-data-message text-gray-500 dark:text-gray-400 text-center py-4';
                message.textContent = 'No timeline data available for the selected filters';
                ctx.parentNode.insertBefore(message, ctx.nextSibling);
            }
        }
        return;
    }

    // Sort data chronologically by date
    processedData.sort((a, b) => new Date(a.date) - new Date(b.date));

    // Extract data for the chart
    const formattedDates = processedData.map(item => item.displayDate);
    const trendPositive = processedData.map(item => item.Positive);
    const trendNegative = processedData.map(item => item.Negative);
    const trendNeutral = processedData.map(item => item.Neutral);

    // Chart configuration
    const chartConfig = {
        type: 'line',
        data: {
            labels: formattedDates,
            datasets: [
                {
                    label: 'Positive',
                    data: trendPositive,
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.2)',
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Negative',
                    data: trendNegative,
                    borderColor: '#EF4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.2)',
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Neutral',
                    data: trendNeutral,
                    borderColor: '#F59E0B',
                    backgroundColor: 'rgba(245, 158, 11, 0.2)',
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(tooltipItems) {
                            return 'Date: ' + tooltipItems[0].label;
                        },
                        label: function(context) {
                            return context.dataset.label + ': ' + context.raw;
                        }
                    }
                },
                title: {
                    display: true,
                    text: getChartTitle(days, viewType),
                    font: {
                        size: 14
                    }
                }
            },
            scales: {
                x: {
                    type: 'category',
                    time: { unit: 'day' },
                    title: {
                        display: true,
                        text: getAxisTitle(viewType)
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Count'
                    }
                }
            }
        }
    };

    // Update or create the chart
    if (annotatedTimeSeriesChart) {
        annotatedTimeSeriesChart.data = chartConfig.data;
        annotatedTimeSeriesChart.options = chartConfig.options;
        annotatedTimeSeriesChart.update();
    } else if (ctx) {
        annotatedTimeSeriesChart = new Chart(ctx.getContext('2d'), chartConfig);
    }
}
function updateMainDriversChart(mainDriversData, sentimentsAcrossDriversData) {
    console.log('Updating main drivers chart...');
    const sortedDrivers = Object.entries(mainDriversData).sort((a, b) => b[1] - a[1]).slice(0, 5);
    const driverLabels = sortedDrivers.map(entry => entry[0]);
    const driverCounts = sortedDrivers.map(entry => entry[1]);
    const sentimentColors = { Positive: '#10B981', Neutral: '#F59E0B', Negative: '#EF4444' };
    const sentimentDatasets = {};
    driverLabels.forEach(driver => {
        ['Positive', 'Neutral', 'Negative'].forEach(sentiment => {
            const count = sentimentsAcrossDriversData[driver]?.[sentiment] || 0;
            if (!sentimentDatasets[sentiment]) {
                sentimentDatasets[sentiment] = {
                    label: sentiment,
                    data: [],
                    backgroundColor: sentimentColors[sentiment],
                    stack: 'stack1'
                };
            }
            sentimentDatasets[sentiment].data.push(count);
        });
    });
    if (sentimentsAcrossMainDriversChart) {
        sentimentsAcrossMainDriversChart.data.labels = driverLabels;
        sentimentsAcrossMainDriversChart.data.datasets = Object.values(sentimentDatasets);
        sentimentsAcrossMainDriversChart.update();
    } else {
        sentimentsAcrossMainDriversChart = new Chart(document.getElementById('sentimentsAcrossMainDriversChart').getContext('2d'), {
            type: 'bar',
            data: {
                labels: driverLabels,
                datasets: Object.values(sentimentDatasets)
            },
            options: {
                responsive: true,
                indexAxis: 'y',
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' },
                    title: { display: true, text: 'Sentiment Distribution Across Drivers' }
                },
                scales: {
                    x: { stacked: true, beginAtZero: true },
                    y: { stacked: true, ticks: { autoSkip: false } }
                }
            }
        });
    }
    const sentimentDriversContainer = document.getElementById('sentimentDrivers');
    sentimentDriversContainer.innerHTML = '';
    driverLabels.forEach((driver, index) => {
        const card = document.createElement('div');
        card.className = 'insight-card bg-white dark:bg-gray-700 text-gray-800 dark:text-white p-4 rounded-lg shadow-sm';
        const contributionPercentage = window.totalSentiments > 0 ? ((driverCounts[index] / window.totalSentiments) * 100).toFixed(2) : 0;
        card.innerHTML = `
            <div class="flex items-start">
                <div class="bg-blue-100 dark:bg-blue-200 text-blue-600 p-2 rounded-full mr-3">
                    <i class="fa fa-lightbulb text-xs"></i>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 dark:text-white">${driver}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Driver contributed ${contributionPercentage}% of feedback</p>
                </div>
            </div>
        `;
        sentimentDriversContainer.appendChild(card);
    });
}

// Function to update word cloud
/* function updateWordCloud(data) {
    console.log('Updating word cloud with data:', data);
    const wordFrequency = {};
    const stopWords = ['the', 'a', 'is', 'and', 'in', 'of', 'to', 'it', 'that', 'this', 'for', 'on', 'with', 'as', 'by', 'an', 'be', 'are', 'was', 'were', 'has', 'have', 'had', 'not', 'or', 'but', 'if', 'then', 'so', 'such', 'can', 'will', 'would', 'shall', 'should', 'may', 'might', 'must', 'do', 'does', 'did', 'done', 'up', 'out', 'about', 'into', 'over', 'after', 'before', 'between', 'through', 'during', 'without', 'under', 'around', 'among', 'at', 'from', 'like', 'than', 'too', 'very', 'just', 'only', 'own', 'same', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'];
    data.forEach(comment => {
        if (comment) {
            const words = comment.toLowerCase().split(/[\s,.\?\!]+/).filter(word => word.length >= 3 && !stopWords.includes(word));
            words.forEach(word => {
                wordFrequency[word] = (wordFrequency[word] || 0) + 1;
            });
        }
    });
    const wordCloudData = Object.entries(wordFrequency)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 100) // Limit to top 100 words
        .map(([name, value]) => ({
            name,
            value,
            textStyle: {
                color: `rgb(${Math.floor(Math.random() * 200)}, ${Math.floor(Math.random() * 200)}, ${Math.floor(Math.random() * 200)})`
            }
        }));
    const wordCloudContainer = document.getElementById('wordcloud');
    if (wordCloudChart) {
        wordCloudChart.dispose();
    }
    wordCloudChart = echarts.init(wordCloudContainer, null, {
        renderer: 'canvas',
        useDirtyRect: true,
        passive: true
    });
    wordCloudChart.setOption({
        animation: false,
        series: [{
            type: 'wordCloud',
            shape: 'circle',
            left: 'center',
            top: 'center',
            width: '100%',
            height: '100%',
            sizeRange: [12, 60],
            rotationRange: [-90, 90],
            rotationStep: 45,
            gridSize: 12,
            drawOutOfBound: false,
            textStyle: {
                fontFamily: 'sans-serif',
                fontWeight: 'bold'
            },
            emphasis: {
                focus: 'self',
                textStyle: {
                    shadowBlur: 10,
                    shadowColor: '#333'
                }
            },
            data: wordCloudData
        }]
    });
}
 */

 // Function to update word cloud with complete verbatims
function updateWordCloud(data) {
    console.log('Updating word cloud with data:', data);
    const verbatimFrequency = {};

    // Common filler words to clean from the beginning of verbatims
    const fillerStarts = ['the ', 'a ', 'an ', 'i ', 'my ', 'it ', 'they ', 'he ', 'she ', 'we ', 'you ', 'your ', 'this ', 'that ', 'these ', 'those '];

    // Additional common words to remove from verbatims for better focus
    const commonWords = ['and', 'the', 'was', 'with', 'for', 'that', 'this', 'have', 'had', 'has', 'very', 'just'];

    // Common punctuation to clean up
    const punctuationRegex = /[.,!?;:()[\]{}""'']/g;

    data.forEach(comment => {
        if (comment) {
            // Split by semicolons to handle multiple verbatims in one comment
            const verbatims = comment.split(';');

            verbatims.forEach(verbatim => {
                // Clean and normalize the verbatim
                let cleanVerbatim = verbatim.trim().toLowerCase();

                // Skip empty verbatims
                if (!cleanVerbatim) return;

                // Skip very short verbatims (less than 3 characters)
                if (cleanVerbatim.length < 3) return;

                // Remove quotes if they wrap the entire verbatim
                if ((cleanVerbatim.startsWith('"') && cleanVerbatim.endsWith('"')) ||
                    (cleanVerbatim.startsWith("'") && cleanVerbatim.endsWith("'"))) {
                    cleanVerbatim = cleanVerbatim.substring(1, cleanVerbatim.length - 1).trim();
                }

                // Remove common filler words from the beginning
                let wordChanged = true;
                while (wordChanged) {
                    wordChanged = false;
                    for (const filler of fillerStarts) {
                        if (cleanVerbatim.startsWith(filler)) {
                            cleanVerbatim = cleanVerbatim.substring(filler.length).trim();
                            wordChanged = true;
                            break;
                        }
                    }
                }

                // Clean up punctuation (replace with spaces to maintain word separation)
                cleanVerbatim = cleanVerbatim.replace(punctuationRegex, ' ').replace(/\s+/g, ' ').trim();

                // Skip if too short after cleaning
                if (cleanVerbatim.length < 3) return;

                // Limit verbatim length to prevent extremely long phrases
                if (cleanVerbatim.length > 50) {
                    cleanVerbatim = cleanVerbatim.substring(0, 50) + '...';
                }

                // Count the verbatim frequency
                verbatimFrequency[cleanVerbatim] = (verbatimFrequency[cleanVerbatim] || 0) + 1;
            });
        }
    });

    // Calculate total mentions for percentage calculations
    const totalMentions = Object.values(verbatimFrequency).reduce((sum, val) => sum + val, 0);

    // Store verbatim frequency in a global variable to ensure it's accessible in tooltip formatter
    window.verbatimFrequencyData = verbatimFrequency;
    window.totalMentionsCount = totalMentions;

    const wordCloudData = Object.entries(verbatimFrequency)
        .sort((a, b) => b[1] - a[1]) // Sort by frequency (descending)
        .slice(0, 200) // Increased to top 200 verbatims
        .map(([name, value], index) => {
            // Calculate color based on frequency rank (top items get more vibrant colors)
            // This ensures visual priority for the most frequent verbatims
            const rankPercentage = 1 - (index / 200); // 0 to 1 where 1 is highest rank
            const colorIntensity = Math.floor(100 + (rankPercentage * 155)); // 100-255 range

            // Calculate font weight based on frequency rank
            const fontWeight = index < 20 ? 'bolder' :
                              index < 50 ? 'bold' : 'normal';

            // Calculate percentage for this verbatim
            const percentage = ((value / totalMentions) * 100).toFixed(1);

            return {
                name,
                value: Math.pow(value, 1.2), // Apply slight exponential scaling to emphasize frequency differences
                // Store original frequency and percentage as custom properties
                frequency: value,
                percentage: percentage,
                textStyle: {
                    color: `rgb(${Math.floor(Math.random() * colorIntensity)},
                              ${Math.floor(Math.random() * colorIntensity)},
                              ${Math.floor(Math.random() * colorIntensity)})`,
                    fontWeight: fontWeight
                }
            };
        });

    const wordCloudContainer = document.getElementById('wordcloud');
    if (wordCloudChart) {
        wordCloudChart.dispose();
    }
    // Make sure the container is visible and has proper dimensions
    wordCloudContainer.style.visibility = 'visible';
    wordCloudContainer.style.height = '400px';

    // Add direct DOM event listeners to the container for more reliable hover detection
    wordCloudContainer.addEventListener('mouseenter', function() {
        window.isHoveringWordCloud = true;
        wordCloudContainer.classList.add('hovering');

        // Pause any animations when hovering
        if (wordCloudChart) {
            wordCloudChart.setOption({
                animation: false
            });
        }
    });

    wordCloudContainer.addEventListener('mouseleave', function() {
        window.isHoveringWordCloud = false;
        window.currentHoveredItem = null;
        wordCloudContainer.classList.remove('hovering');

        // Force tooltip to hide by setting alwaysShowContent to false first
        if (wordCloudChart) {
            wordCloudChart.setOption({
                tooltip: {
                    alwaysShowContent: false
                },
                animation: true
            });

            // Hide any lingering tooltips with multiple attempts
            for (let delay of [0, 50, 100]) {
                setTimeout(() => {
                    wordCloudChart.dispatchAction({
                        type: 'hideTip'
                    });
                }, delay);
            }
        }

        // Also try to hide any lingering tooltip elements directly
        const tooltips = document.querySelectorAll('.echarts-tooltip');
        tooltips.forEach(tooltip => {
            tooltip.style.display = 'none';
            tooltip.style.opacity = '0';
        });
    });

    // Initialize the chart with proper renderer options
    wordCloudChart = echarts.init(wordCloudContainer, null, {
        renderer: 'canvas',
        useDirtyRect: false, // Disable dirty rect to ensure proper rendering
        passive: false // Disable passive mode to ensure event handling works properly
    });
    // Set up the word cloud chart
    wordCloudChart.setOption({
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationThreshold: 300,
        series: [{
            type: 'wordCloud',
            shape: 'circle',
            left: 'center',
            top: 'center',
            width: '100%',
            height: '100%',
            sizeRange: [8, 36], // Further reduced font size range to fit even more verbatims
            rotationRange: [-30, 30], // Further reduced rotation for better readability
            rotationStep: 10, // Smaller rotation step for more natural appearance
            gridSize: 4, // Even smaller grid size to allow maximum verbatims to fit
            drawOutOfBound: false,
            layoutAnimation: true, // Enable layout animation
            // Add animation to make verbatims move around
            autoSize: {
                enable: true,
                minSize: 6
            },
            // Add more noticeable movement animation
            nodeMotion: {
                enable: true,
                speed: 1.0, // Increased speed for more visible movement
                random: true, // Random movement direction
                damping: 0.8, // Lower damping for more movement
                swing: 0.3, // Increased swing factor for more noticeable movement
                avoidOverlap: false // Allow some overlap for more dynamic movement
            },
            // Enhanced tooltip configuration for better visibility
            tooltip: {
                show: true,
                trigger: 'item', // Ensure tooltip triggers on item hover
                confine: true, // Keep tooltip within chart bounds
                enterable: true, // Allow mouse to enter tooltip to prevent flickering
                triggerOn: 'mousemove', // Show on mouse move
                alwaysShowContent: true, // Force tooltip to always show when hovering
                showDelay: 0, // Show immediately
                hideDelay: 0, // No delay before hiding
                transitionDuration: 0, // No transition animation
                backgroundColor: 'rgba(0,0,0,0.9)', // Darker background for better contrast
                borderColor: '#3498db', // Blue border
                borderWidth: 2, // Thicker border
                padding: 12, // More padding
                textStyle: {
                    color: '#fff',
                    fontSize: 16, // Larger font size
                    fontWeight: 'bold' // Bold text
                },
                extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.8); z-index: 9999 !important; pointer-events: all !important;',
                position: function (pos, params, dom, rect, size) {
                    // Get the container dimensions
                    const container = document.getElementById('wordcloud');
                    if (container) {
                        const containerRect = container.getBoundingClientRect();

                        // Calculate tooltip dimensions
                        const tooltipWidth = dom.offsetWidth;
                        const tooltipHeight = dom.offsetHeight;

                        // Position tooltip in center of container for better visibility
                        // This fixed position helps prevent the tooltip from "chasing" the cursor
                        return [
                            // Center horizontally
                            containerRect.width / 2 - tooltipWidth / 2,
                            // Position near the top but with some space
                            Math.min(100, containerRect.height / 4)
                        ];
                    }

                    // Fallback to fixed position relative to cursor
                    return [pos[0] - 100, pos[1] - 120];
                },
                formatter: function(params) {
                    // Get frequency from the data item's custom properties
                    // or fall back to global variable if not available
                    let frequency = params.data.frequency;
                    let percentage = params.data.percentage;

                    // Fallback to global variables if needed
                    if (frequency === undefined && window.verbatimFrequencyData) {
                        frequency = window.verbatimFrequencyData[params.name] || 0;
                        percentage = window.totalMentionsCount ?
                            ((frequency / window.totalMentionsCount) * 100).toFixed(1) : '0.0';
                    }

                    return `<div style="text-align:center; padding: 15px; min-width: 280px; max-width: 380px;">
                        <div style="font-size: 20px; font-weight: bold; color: #3498db; margin-bottom: 15px; text-decoration: underline; text-shadow: 0 0 5px rgba(52, 152, 219, 0.5);">"${params.name}"</div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px; background: rgba(255,255,255,0.15); padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                            <span style="font-size: 17px;">Mentions:</span>
                            <span style="font-size: 17px; font-weight: bold; color: #2ecc71;">${frequency}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px; background: rgba(255,255,255,0.15); padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                            <span style="font-size: 17px;">Percentage:</span>
                            <span style="font-size: 17px; font-weight: bold; color: #f39c12;">${percentage}%</span>
                        </div>
                        <div style="margin-top: 15px; font-size: 14px; color: #ccc; font-style: italic;">Click again to close</div>
                    </div>`;
                },
                backgroundColor: 'rgba(0, 0, 0, 0.95)',
                borderColor: '#3498db',
                borderWidth: 3,
                padding: [15, 20],
                textStyle: {
                    color: '#fff',
                    fontSize: 16,
                    lineHeight: 24,
                    fontWeight: 'bold'
                },
                extraCssText: 'box-shadow: 0 0 20px rgba(52, 152, 219, 0.8); z-index: 9999 !important; pointer-events: all !important; border-radius: 8px;'
            },
            textStyle: {
                fontFamily: 'sans-serif',
                fontWeight: 'bold',
                // Add quotes around each verbatim
                formatter: function(value) {
                    // Add quotes around the text if it doesn't already have them
                    if (!value.startsWith('"') && !value.endsWith('"')) {
                        return '"' + value + '"';
                    }
                    return value;
                }
            },
            emphasis: {
                focus: 'self',
                scaleSize: 1.2, // Scale up text slightly on hover
                textStyle: {
                    shadowBlur: 10,
                    shadowColor: '#333',
                    fontWeight: 'bolder'
                }
            },
            data: wordCloudData
        }]
    });

    // Store the current data for refreshing
    window.currentWordCloudData = wordCloudData;

    // Track the currently selected item
    window.selectedVerbatim = null;

    // Add click event handler for word cloud items
    wordCloudChart.on('click', function(params) {
        const container = document.getElementById('wordcloud');

        // Get all text elements in the word cloud
        const textElements = container.querySelectorAll('text');

        // Store the clicked item data
        const clickedItem = {
            dataIndex: params.dataIndex,
            name: params.name,
            value: params.value,
            frequency: params.data.frequency,
            percentage: params.data.percentage
        };

        // Check if we're clicking on the same item that's already selected
        if (window.selectedVerbatim && window.selectedVerbatim.name === params.name) {
            // Clicking on the same item - deselect it

            // Remove selection styling
            textElements.forEach(text => {
                text.classList.remove('selected-verbatim');
            });
            container.classList.remove('has-selection');

            // Hide the tooltip
            wordCloudChart.setOption({
                tooltip: {
                    alwaysShowContent: false
                }
            });

            wordCloudChart.dispatchAction({
                type: 'hideTip'
            });

            // Clear the selected item
            window.selectedVerbatim = null;

            // Re-enable animations
            wordCloudChart.setOption({
                animation: true,
                series: [{
                    nodeMotion: {
                        enable: true,
                        speed: 1.0,
                        damping: 0.8,
                        swing: 0.3
                    }
                }]
            });
        } else {
            // Clicking on a new item or first selection

            // Store the newly selected item
            window.selectedVerbatim = clickedItem;

            // Stop animations when an item is selected
            wordCloudChart.setOption({
                animation: false,
                series: [{
                    nodeMotion: {
                        enable: false
                    }
                }]
            });

            // Add selection styling
            container.classList.add('has-selection');

            // Find the text element for the selected verbatim and add the selected class
            setTimeout(() => {
                textElements.forEach(text => {
                    // Remove selected class from all elements first
                    text.classList.remove('selected-verbatim');

                    // Check if this is the selected verbatim
                    if (text.textContent.includes(params.name)) {
                        text.classList.add('selected-verbatim');
                    }
                });
            }, 50);

            // Show tooltip for the selected item
            for (let delay of [0, 50, 100, 200]) {
                setTimeout(function() {
                    if (window.selectedVerbatim) {
                        wordCloudChart.dispatchAction({
                            type: 'showTip',
                            seriesIndex: 0,
                            dataIndex: params.dataIndex,
                            name: params.name
                        });

                        // Force tooltip to always show
                        wordCloudChart.setOption({
                            tooltip: {
                                alwaysShowContent: true
                            }
                        });
                    }
                }, delay);
            }
        }
    });

    // Add mouseover effect for hover indication (without showing tooltip)
    wordCloudChart.on('mouseover', function(params) {
        // Only add hover effect if no item is selected
        if (!window.selectedVerbatim) {
            const container = document.getElementById('wordcloud');
            if (container) {
                container.classList.add('hovering');
            }
        }
    });

    wordCloudChart.on('mouseout', function(params) {
        // Only remove hover effect if no item is selected
        if (!window.selectedVerbatim) {
            const container = document.getElementById('wordcloud');
            if (container) {
                container.classList.remove('hovering');
            }
        }
    });

    // Set up periodic refresh to keep animation going and show different verbatims
    if (window.wordCloudRefreshInterval) {
        clearInterval(window.wordCloudRefreshInterval);
    }

    // Add a document-level click handler to deselect verbatim when clicking outside the word cloud
    if (window.documentClickHandler) {
        document.removeEventListener('click', window.documentClickHandler);
    }

    window.documentClickHandler = function(e) {
        // Check if the click is outside the word cloud
        const wordCloudElement = document.getElementById('wordcloud');
        if (wordCloudElement && !wordCloudElement.contains(e.target)) {
            // Click is outside the word cloud, deselect any selected verbatim
            if (wordCloudChart && window.selectedVerbatim) {
                // Get all text elements in the word cloud
                const textElements = wordCloudElement.querySelectorAll('text');

                // Remove selection styling
                textElements.forEach(text => {
                    text.classList.remove('selected-verbatim');
                });
                wordCloudElement.classList.remove('has-selection');

                // Force tooltip to hide
                wordCloudChart.setOption({
                    tooltip: {
                        alwaysShowContent: false
                    }
                });

                wordCloudChart.dispatchAction({
                    type: 'hideTip'
                });

                // Clear the selected item
                window.selectedVerbatim = null;

                // Re-enable animations
                wordCloudChart.setOption({
                    animation: true,
                    series: [{
                        nodeMotion: {
                            enable: true,
                            speed: 1.0,
                            damping: 0.8,
                            swing: 0.3
                        }
                    }]
                });

                // Hide any lingering tooltip elements directly
                const tooltips = document.querySelectorAll('.echarts-tooltip');
                tooltips.forEach(tooltip => {
                    tooltip.style.display = 'none';
                    tooltip.style.opacity = '0';
                });
            }
        }
    };

    document.addEventListener('click', window.documentClickHandler);

    // Add scroll event listener to maintain selected verbatim during scrolling
    if (window.scrollHandler) {
        window.removeEventListener('scroll', window.scrollHandler);
    }

    window.scrollHandler = function() {
        // If no verbatim is selected, nothing to do
        if (!window.selectedVerbatim) return;

        // If a verbatim is selected, ensure its tooltip stays visible
        if (wordCloudChart) {
            // Refresh the tooltip for the selected verbatim
            setTimeout(() => {
                if (window.selectedVerbatim) {
                    wordCloudChart.dispatchAction({
                        type: 'showTip',
                        seriesIndex: 0,
                        name: window.selectedVerbatim.name
                    });

                    // Ensure the tooltip stays visible
                    wordCloudChart.setOption({
                        tooltip: {
                            alwaysShowContent: true
                        }
                    });
                }
            }, 100);
        }
    };

    window.addEventListener('scroll', window.scrollHandler, { passive: true });

    // Refresh the word cloud every 20 seconds
    // but only when no verbatim is selected
    window.wordCloudRefreshInterval = setInterval(() => {
        // Skip refresh if a verbatim is currently selected
        if (window.selectedVerbatim) {
            console.log('Skipping word cloud refresh because a verbatim is selected');
            return;
        }

        if (window.currentWordCloudData && window.currentWordCloudData.length > 0) {
            // Shuffle the data slightly to show different verbatims
            const shuffledData = [...window.currentWordCloudData];

            // Randomize the order a bit
            for (let i = shuffledData.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffledData[i], shuffledData[j]] = [shuffledData[j], shuffledData[i]];
            }

            // Apply small random variations to the values to change the layout
            const variedData = shuffledData.map(item => {
                const variation = 1 + (Math.random() * 0.15 - 0.075); // Reduced to ±7.5% variation for smoother transitions
                return {
                    ...item,
                    // Only modify the display value, preserve original frequency and percentage
                    value: item.value * variation,
                    // Ensure frequency and percentage are preserved
                    frequency: item.frequency || (window.verbatimFrequencyData ? window.verbatimFrequencyData[item.name] : 0),
                    percentage: item.percentage || (window.verbatimFrequencyData && window.totalMentionsCount ?
                        (((window.verbatimFrequencyData[item.name] || 0) / window.totalMentionsCount) * 100).toFixed(1) : '0.0')
                };
            });

            // Update the chart with the varied data
            wordCloudChart.setOption({
                series: [{
                    data: variedData
                }]
            });
        }
    }, 20000); // 20 second refresh interval
}
// Function to update main driver sentiment chart
function updateMainDriverSentimentChart(data) {
    console.log('Updating L1 driver sentiment chart with data:', data);

    // Store the original data globally so we can access it when navigating pages
    window.mainDriverData = data;

    // Get current filter values
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Store current filter values globally
    window.currentFilters = {
        domains: domains || 'all',
        dataId: dataId || 'all',
        sentiment: sentiment || 'all'
    };

    let mainDriverLabels = Object.keys(data);
    let mainDriverPositive = mainDriverLabels.map(driver => data[driver]?.Positive || 0);
    let mainDriverNeutral = mainDriverLabels.map(driver => data[driver]?.Neutral || 0);
    let mainDriverNegative = mainDriverLabels.map(driver => data[driver]?.Negative || 0);

    const totalRecords = mainDriverLabels.length;
    const recordsPerPage = 10;

    // Initialize currentPage if it doesn't exist yet
    if (typeof window.mainDriverCurrentPage === 'undefined') {
        window.mainDriverCurrentPage = 0;
    }

    let currentPage = window.mainDriverCurrentPage;

    function getPaginatedData(page) {
        const start = page * recordsPerPage;
        const end = Math.min(start + recordsPerPage, totalRecords);
        return {
            labels: mainDriverLabels.slice(start, end),
            positive: mainDriverPositive.slice(start, end),
            neutral: mainDriverNeutral.slice(start, end),
            negative: mainDriverNegative.slice(start, end)
        };
    }

    function updateChart(page) {
        const { labels, positive, neutral, negative } = getPaginatedData(page);
        const ctx = document.getElementById('mainDriverChart').getContext('2d');
        if (mainDriverChart) {
            mainDriverChart.data.labels = labels;
            mainDriverChart.data.datasets[0].data = positive;
            mainDriverChart.data.datasets[1].data = neutral;
            mainDriverChart.data.datasets[2].data = negative;
            mainDriverChart.update();
        } else {
            mainDriverChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        { label: 'Positive', data: positive, backgroundColor: '#10B981' },
                        { label: 'Neutral', data: neutral, backgroundColor: '#F59E0B' },
                        { label: 'Negative', data: negative, backgroundColor: '#EF4444' }
                    ]
                },
                options: {
                    responsive: true,
                    indexAxis: 'y',
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' },
                        title: { display: true, text: 'Sentiment Distribution by L1 Driver' }
                    },
                    scales: {
                        x: { stacked: true, beginAtZero: true },
                        y: { stacked: true, ticks: { autoSkip: false } }
                    }
                }
            });
        }
    }

    // Remove previous event listeners to avoid duplicates
    const nextButton = document.getElementById('nextPage');
    const prevButton = document.getElementById('prevPage');

    // Clone and replace the buttons to remove old event listeners
    const nextButtonClone = nextButton.cloneNode(true);
    const prevButtonClone = prevButton.cloneNode(true);
    nextButton.parentNode.replaceChild(nextButtonClone, nextButton);
    prevButton.parentNode.replaceChild(prevButtonClone, prevButton);

    // Add new event listeners
    document.getElementById('nextPage').addEventListener('click', () => {
        if ((window.mainDriverCurrentPage + 1) * recordsPerPage < totalRecords) {
            window.mainDriverCurrentPage++;
            updateChart(window.mainDriverCurrentPage);
            console.log('L1 driver chart updated to page:', window.mainDriverCurrentPage + 1);
        }
    });

    document.getElementById('prevPage').addEventListener('click', () => {
        if (window.mainDriverCurrentPage > 0) {
            window.mainDriverCurrentPage--;
            updateChart(window.mainDriverCurrentPage);
            console.log('L1 driver chart updated to page:', window.mainDriverCurrentPage + 1);
        }
    });

    updateChart(window.mainDriverCurrentPage);
}

function updateSubDriverSentimentChart(data) {
    console.log('Updating L2 driver sentiment chart with data:', data);

    // Store the original data globally so we can access it when navigating pages
    window.subDriverData = data;

    // Get current filter values
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Store current filter values globally
    window.currentFilters = {
        domains: domains || 'all',
        dataId: dataId || 'all',
        sentiment: sentiment || 'all'
    };

    let subDriverLabels = Object.keys(data);
    let subDriverPositive = subDriverLabels.map(driver => data[driver]?.Positive || 0);
    let subDriverNeutral = subDriverLabels.map(driver => data[driver]?.Neutral || 0);
    let subDriverNegative = subDriverLabels.map(driver => data[driver]?.Negative || 0);

    const totalRecords = subDriverLabels.length;
    const recordsPerPage = 10;

    // Initialize currentPage if it doesn't exist yet
    if (typeof window.subDriverCurrentPage === 'undefined') {
        window.subDriverCurrentPage = 0;
    }

    let currentPage = window.subDriverCurrentPage;

    function getPaginatedData(page) {
        const start = page * recordsPerPage;
        const end = Math.min(start + recordsPerPage, totalRecords);
        return {
            labels: subDriverLabels.slice(start, end),
            positive: subDriverPositive.slice(start, end),
            neutral: subDriverNeutral.slice(start, end),
            negative: subDriverNegative.slice(start, end)
        };
    }

    function updateChart(page) {
        const { labels, positive, neutral, negative } = getPaginatedData(page);
        const ctx = document.getElementById('subDriverChart').getContext('2d');
        if (subDriverChart) {
            subDriverChart.data.labels = labels;
            subDriverChart.data.datasets[0].data = positive;
            subDriverChart.data.datasets[1].data = neutral;
            subDriverChart.data.datasets[2].data = negative;
            subDriverChart.update();
        } else {
            subDriverChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        { label: 'Positive', data: positive, backgroundColor: '#10B981' },
                        { label: 'Neutral', data: neutral, backgroundColor: '#F59E0B' },
                        { label: 'Negative', data: negative, backgroundColor: '#EF4444' }
                    ]
                },
                options: {
                    responsive: true,
                    indexAxis: 'y',
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' },
                        title: { display: true, text: 'Sentiment Distribution by L2 Driver' }
                    },
                    scales: {
                        x: { stacked: true, beginAtZero: true },
                        y: { stacked: true, ticks: { autoSkip: false } }
                    }
                }
            });
        }
    }

    // Remove previous event listeners to avoid duplicates
    const nextButton = document.getElementById('nextPage1');
    const prevButton = document.getElementById('prevPage1');

    // Clone and replace the buttons to remove old event listeners
    const nextButtonClone = nextButton.cloneNode(true);
    const prevButtonClone = prevButton.cloneNode(true);
    nextButton.parentNode.replaceChild(nextButtonClone, nextButton);
    prevButton.parentNode.replaceChild(prevButtonClone, prevButton);

    // Add new event listeners
    document.getElementById('nextPage1').addEventListener('click', () => {
        if ((window.subDriverCurrentPage + 1) * recordsPerPage < totalRecords) {
            window.subDriverCurrentPage++;
            updateChart(window.subDriverCurrentPage);
            console.log('L2 driver chart updated to page:', window.subDriverCurrentPage + 1);
        }
    });

    document.getElementById('prevPage1').addEventListener('click', () => {
        if (window.subDriverCurrentPage > 0) {
            window.subDriverCurrentPage--;
            updateChart(window.subDriverCurrentPage);
            console.log('L2 driver chart updated to page:', window.subDriverCurrentPage + 1);
        }
    });

    updateChart(window.subDriverCurrentPage);
}


function updateDetailedSentimentsTable(data) {
    console.log('Updating detailed sentiments table with data:', data);
    const tableBody = document.getElementById('detailedSentimentsTableBody');
    tableBody.innerHTML = '';

    if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('No detailed sentiments data available');
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="9" class="px-4 py-2 text-center">No data available</td>`;
        tableBody.appendChild(row);
        return;
    }

    data.forEach(item => {
        const row = document.createElement('tr');

        // Get sentiment icon based on score using the percentage-based calculation
        let sentimentIcon = '';
        const score = item.sentiment_score || 0;

        // Adjust thresholds for the percentage-based score
        if (score >= 25) {
            sentimentIcon = '🟢'; // Green for positive scores >= 25%
        } else if (score >= 0) {
            sentimentIcon = '🟡'; // Yellow for positive scores between 0% and 25%
        } else if (score >= -25) {
            sentimentIcon = '🟠'; // Orange for scores between -25% and 0%
        } else {
            sentimentIcon = '🔴'; // Red for negative scores < -25%
        }

        row.innerHTML = `
            <td class="px-4 py-2 border-b">${item.driver || 'Unknown'}</td>
            <td class="px-4 py-2 border-b">${item.count || 0}</td>
            <td class="px-4 py-2 border-b">${item.positive_count || 0}</td>
            <td class="px-4 py-2 border-b">${item.neutral_count || 0}</td>
            <td class="px-4 py-2 border-b">${item.negative_count || 0}</td>
            <td class="px-4 py-2 border-b">${item.positive_percentage || 0}%</td>
            <td class="px-4 py-2 border-b">${item.neutral_percentage || 0}%</td>
            <td class="px-4 py-2 border-b">${item.negative_percentage || 0}%</td>
            <td class="px-4 py-2 border-b">${sentimentIcon} ${parseFloat(item.sentiment_score || 0).toFixed(2)}%</td>
        `;
        tableBody.appendChild(row);
    });
}

// Removed unused getSentimentIcon function

function updateTopPositiveNegativeDrivers(positiveDrivers, negativeDrivers) {
	console.log('Updating top positive and negative sub-drivers tables...');
    console.log('Positive drivers:', positiveDrivers);
    console.log('Negative drivers:', negativeDrivers);
    const positiveTableBody = document.getElementById('topPositiveDriversTable');
    positiveTableBody.innerHTML = '';
    positiveDrivers.forEach(driver => {
        const row = document.createElement('tr');
        row.innerHTML = `<td class="px-4 py-2 border-b">${driver.sub_driver}</td><td class="px-4 py-2 border-b">${driver.positive}</td>`;
        positiveTableBody.appendChild(row);
    });

    const negativeTableBody = document.getElementById('topNegativeDriversTable');
    negativeTableBody.innerHTML = '';
    negativeDrivers.forEach(driver => {
        const row = document.createElement('tr');
        row.innerHTML = `<td class="px-4 py-2 border-b">${driver.sub_driver}</td><td class="px-4 py-2 border-b">${driver.negative}</td>`;
        negativeTableBody.appendChild(row);
    });
}

function updateCsatNpsImpactDrivers(csatDrivers, npsDrivers) {
	console.log('Updating CSAT/NPS impact drivers tables...');
    console.log('CSAT drivers:', csatDrivers);
    console.log('NPS drivers:', npsDrivers);

	const csatTableBody = document.getElementById('topCsatImpactTable');
    csatTableBody.innerHTML = '';
    csatDrivers.forEach(driver => {
        const row = document.createElement('tr');
        // Format average_csat to 2 decimal places
        const formattedCsat = parseFloat(driver.average_csat).toFixed(2);
        row.innerHTML = `<td class="px-4 py-2 border-b">${driver.sub_driver}</td><td class="px-4 py-2 border-b">${formattedCsat}</td>`;
        csatTableBody.appendChild(row);
    });

    const npsTableBody = document.getElementById('topNpsImpactTable');
    npsTableBody.innerHTML = '';
    npsDrivers.forEach(driver => {
        const row = document.createElement('tr');
        // Format average_nps to 2 decimal places
        const formattedNps = parseFloat(driver.average_nps).toFixed(2);
        row.innerHTML = `<td class="px-4 py-2 border-b">${driver.sub_driver}</td><td class="px-4 py-2 border-b">${formattedNps}</td>`;
        npsTableBody.appendChild(row);
    });
}

function updateNpsPromoterDetractorDrivers(promoterDrivers, detractorDrivers) {
	console.log('Updating NPS promoters/detractors tables...');
    console.log('Promoters:', promoterDrivers);
    console.log('Detractors:', detractorDrivers);

	const promoterTableBody = document.getElementById('topNpsPromotersTable');
    promoterTableBody.innerHTML = '';
    promoterDrivers.forEach(driver => {
        const row = document.createElement('tr');
        row.innerHTML = `<td class="px-4 py-2 border-b">${driver.sub_driver}</td><td class="px-4 py-2 border-b">${driver.promoter}</td>`;
        promoterTableBody.appendChild(row);
    });

    const detractorTableBody = document.getElementById('topNpsDetractorsTable');
    detractorTableBody.innerHTML = '';
    detractorDrivers.forEach(driver => {
        const row = document.createElement('tr');
        row.innerHTML = `<td class="px-4 py-2 border-b">${driver.sub_driver}</td><td class="px-4 py-2 border-b">${driver.detractor}</td>`;
        detractorTableBody.appendChild(row);
    });
}

function updateCsatNpsImpactSummary(csatImpactData, npsImpactData) {
    // Update CSAT impact
    const csatImpactElement = document.getElementById('csatImpact');
    csatImpactElement.innerHTML = `
        <p class="text-3xl font-bold" style="color: ${csatImpactData.csat_impact >= 0 ? 'green' : 'red'};">${csatImpactData.csat_impact}%</p>
        <h4 class="text-sm font-bold  mt-2" style="color: Green;">Positive Factors:</h4>
        <ul class="list-disc pl-6">
            ${csatImpactData.positive_factors.map(factor => `<li>${factor}</li>`).join('')}
        </ul>
        <h4 class="text-sm font-bold  mt-2" style="color: red;">Negative Factors:</h4>
        <ul class="list-disc pl-6">
            ${csatImpactData.negative_factors.map(factor => `<li>${factor}</li>`).join('')}
        </ul>
    `;

    // Update NPS impact
    const npsImpactElement = document.getElementById('npsImpact');
    npsImpactElement.innerHTML = `
        <p class="text-3xl font-bold" style="color: ${npsImpactData.nps_impact >= 0 ? 'green' : 'red'};">${npsImpactData.nps_impact}%</p>
        <h4 class="text-sm font-bold  mt-2" style="color: Green;">Positive Factors:</h4>
        <ul class="list-disc pl-6">
            ${npsImpactData.positive_factors.map(factor => `<li>${factor}</li>`).join('')}
        </ul>
        <h4 class="text-sm font-bold  mt-2" style="color: red;">Negative Factors:</h4>
        <ul class="list-disc pl-6">
            ${npsImpactData.negative_factors.map(factor => `<li>${factor}</li>`).join('')}
        </ul>
    `;
}

function updateFeedbackCommentsSummary(feedbackData) {
    console.log('Updating feedback comments summary with data:', feedbackData);

    // Update the top contributor info
    const topContributorInfo = document.getElementById('topContributorInfo');
    if (feedbackData.top_contributor && feedbackData.contribution_percentage) {
        topContributorInfo.innerHTML = `
            <span class="font-medium">${feedbackData.top_contributor}</span> is the top contributing factor
            (${feedbackData.contribution_percentage}% of all feedback)
        `;
    } else {
        topContributorInfo.innerHTML = 'No significant contributing factors identified';
    }

    // Update positive comments (highlights)
    const highlightsList = document.getElementById('highlightsList');
    highlightsList.innerHTML = '';

    if (feedbackData.highlights && feedbackData.highlights.length > 0) {
        feedbackData.highlights.forEach(highlight => {
            const li = document.createElement('li');
            li.innerHTML = emphasizeKeywords(htmlspecialchars(highlight), feedbackData.keywords);
            highlightsList.appendChild(li);
        });
    } else {
        const li = document.createElement('li');
        li.innerHTML = 'No positive comments available for this factor';
        highlightsList.appendChild(li);
    }

    // Update negative comments (lowlights)
    const lowlightsList = document.getElementById('lowlightsList');
    lowlightsList.innerHTML = '';

    if (feedbackData.lowlights && feedbackData.lowlights.length > 0) {
        feedbackData.lowlights.forEach(lowlight => {
            const li = document.createElement('li');
            li.innerHTML = emphasizeKeywords(htmlspecialchars(lowlight), feedbackData.keywords);
            lowlightsList.appendChild(li);
        });
    } else {
        const li = document.createElement('li');
        li.innerHTML = 'No negative comments available for this factor';
        lowlightsList.appendChild(li);
    }
}

function updateKeyInsightsSummary(summaryData) {
    console.log('Updating key insights summary with data:', summaryData);

    const keyInsightsSummary = document.getElementById('keyInsightsSummary');

    if (!summaryData || !summaryData.summary) {
        keyInsightsSummary.innerHTML = '<p>No summary data available.</p>';
        return;
    }

    // Different formatting approach based on whether it's a consolidated summary or individual
    if (summaryData.is_consolidated) {
        // Format the consolidated summary text with proper HTML
        let formattedSummary = summaryData.summary;

        // Convert newlines to <br> tags
        formattedSummary = formattedSummary.replace(/\n/g, '<br>');

        // Highlight positive and negative terms
        formattedSummary = formattedSummary.replace(/\b(positive|good|excellent|great|improved|increase|higher|better)\b/gi,
            '<span class="font-medium text-positive">$1</span>');
        formattedSummary = formattedSummary.replace(/\b(negative|bad|poor|worse|decreased|lower|issues|problems)\b/gi,
            '<span class="font-medium text-negative">$1</span>');

        // Format bullet points
        formattedSummary = formattedSummary.replace(/•\s*(.*?)(?=<br>|$)/g,
            '<span class="flex"><span class="mr-2">•</span><span>$1</span></span>');

        // Add stats if available
        let statsHtml = '';
        if (summaryData.stats) {
            const stats = summaryData.stats;
            statsHtml = `
                <div class="mt-4 bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-sm">
                    <div class="flex flex-wrap justify-between">
                        <div class="px-2 py-1">
                            <span class="font-semibold">Total Comments:</span> ${stats.total_comments}
                        </div>
                        <div class="px-2 py-1">
                            <span class="font-semibold">Datasets:</span> ${stats.datasets_count}
                        </div>
                        <div class="px-2 py-1">
                            <span class="font-semibold">Sentiment:</span>
                            <span class="text-positive">${stats.positive_percentage}% Positive</span> |
                            <span class="text-neutral">${stats.neutral_percentage}% Neutral</span> |
                            <span class="text-negative">${stats.negative_percentage}% Negative</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // Set the HTML content for consolidated summary
        keyInsightsSummary.innerHTML = `
            <div class="text-gray-700 dark:text-gray-300">
                ${formattedSummary}
                ${statsHtml}
                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400 italic">This is a consolidated summary across multiple datasets.</div>
            </div>
        `;
    } else {
        // Format individual data_id summary with proper table formatting
        let summary = summaryData.summary;

        // First, clean up any existing formatting issues
        summary = summary.replace(/dark:/g, '');
        summary = summary.replace(/dark-/g, '');
        summary = summary.replace(/text-blue-400 font-semibold mt-3 mb-2">/g, '');

        // Format numbered section headers (like "1. Summary:", "2. Top Pain Points:", etc.)
        summary = summary.replace(/(\d+)\.\s*([^:\n]+):/g, '<h3 class="text-blue-600 dark:text-blue-400 font-semibold mt-4 mb-2">$1. $2:</h3>');

        // Format VERIFICATION section specifically
        summary = summary.replace(/(VERIFICATION):/g, '<h3 class="text-blue-600 dark:text-blue-400 font-semibold mt-4 mb-2">$1:</h3>');

        // Remove highlighting for sentiment terms - client wants black text only
        // Just make important terms bold instead of colored
        summary = summary.replace(/\b(positive|good|excellent|great|improved|increase|higher|better)\b/gi,
            '<span class="font-medium">$1</span>');

        const negativeTermsRegex = /(?<!<span[^>]*>)\b(negative|bad|poor|worse|decreased|lower|issues|problems|concerns|complaints|dissatisfaction)\b(?![^<]*<\/span>)/gi;
        summary = summary.replace(negativeTermsRegex, '<span class="font-medium">$1</span>');

        const neutralTermsRegex = /(?<!<span[^>]*>)\b(neutral|average|moderate|unchanged|consistent)\b(?![^<]*<\/span>)/gi;
        summary = summary.replace(neutralTermsRegex, '<span class="font-medium">$1</span>');

        // Format bullet points with dashes or asterisks
        summary = summary.replace(/^- ([^\n]+)/gm,
            '<div class="flex ml-4 mb-1"><span class="mr-2">•</span><span>$1</span></div>');

        // Handle numbered lists in VERIFICATION section
        summary = summary.replace(/^(\d+)\.\s+([^\n]+)/gm, 
            '<div class="ml-4 mb-1">$1. $2</div>');

        // Format ACPT tables
        summary = formatACPTTables(summary);

        // Set the HTML content for individual summary
        keyInsightsSummary.innerHTML = `
            <div class="text-gray-700 dark:text-gray-300 p-2">
                ${summary}
            </div>
        `;
    }
}

// Helper function to format ACPT tables
function formatACPTTables(summary) {
    // Handle ACPT Table (first table) - more flexible pattern
    const acptTablePattern = /(\d+\.\s*ACPT Table:)([\s\S]*?)(?=\d+\.\s*ACPT-Aligned|VERIFICATION:|$)/;
    const acptTableMatch = summary.match(acptTablePattern);

    if (acptTableMatch) {
        const tableTitle = acptTableMatch[1];
        const tableContent = acptTableMatch[2];
        
        const tableHtml = parseAndFormatTable(tableContent, [
            'ACPT Category',
            'Issue Description', 
            'Count',
            'Sample Comment'
        ]);

        const formattedTable = `<h3 class="text-blue-600 dark:text-blue-400 font-semibold mt-4 mb-2">${tableTitle}</h3>${tableHtml}`;
        summary = summary.replace(acptTableMatch[0], formattedTable);
    }

    // Handle ACPT-Aligned Sub Driver & Sentiment Table (second table) - more flexible pattern
    const subDriverTablePattern = /(\d+\.\s*ACPT-Aligned Sub Driver & Sentiment Table:)([\s\S]*?)(?=VERIFICATION:|$)/;
    const subDriverTableMatch = summary.match(subDriverTablePattern);

    if (subDriverTableMatch) {
        const tableTitle = subDriverTableMatch[1];
        const tableContent = subDriverTableMatch[2];
        
        const tableHtml = parseAndFormatTable(tableContent, [
            'ACPT Category',
            'L1 Category',
            'Sub Driver (L2)',
            'Sentiment',
            'Count',
            'Sample Comment'
        ]);

        const formattedTable = `<h3 class="text-blue-600 dark:text-blue-400 font-semibold mt-4 mb-2">${tableTitle}</h3>${tableHtml}`;
        summary = summary.replace(subDriverTableMatch[0], formattedTable);
    }

    return summary;
}

// Helper function to parse and format table data
function parseAndFormatTable(tableContent, headers) {
    const tableRows = [];
    let totalCount = 0;

    console.log('Table content to parse:', tableContent); // Debug log

    // Split content into lines and look for table rows
    const lines = tableContent.split('\n');

    for (let line of lines) {
        line = line.trim();
        
        // Skip empty lines and separator lines (dashes and pipes only)
        if (!line || line.match(/^[\|\-\s]+$/) || line.match(/^\|[\s\-\|]*\|$/)) {
            continue;
        }

        // Check if this line contains pipe characters (table row)
        if (line.includes('|')) {
            const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
            
            console.log('Parsed cells:', cells); // Debug log
            
            // Skip header row - more comprehensive check
            if (cells.length >= headers.length && 
                !cells[0].toLowerCase().includes('category') && 
                !cells[0].toLowerCase().includes('acpt') &&
                !cells[1].toLowerCase().includes('description') &&
                !cells[1].toLowerCase().includes('category') &&
                !cells[0].toLowerCase().includes('total')) {
                
                // Ensure we have enough cells for the table
                while (cells.length < headers.length) {
                    cells.push('');
                }
                
                // Extract count if it's a number
                const countIndex = headers.indexOf('Count');
                if (countIndex !== -1 && countIndex < cells.length) {
                    const count = parseInt(cells[countIndex]);
                    if (!isNaN(count)) {
                        totalCount += count;
                    }
                }

                tableRows.push(cells.slice(0, headers.length)); // Only take as many cells as headers
            }
            // Handle TOTAL row separately - look for total in first cell
            else if (cells.length > 0 && cells[0].toLowerCase().includes('total')) {
                // Find the count in TOTAL row
                for (let cell of cells) {
                    const count = parseInt(cell);
                    if (!isNaN(count) && count > totalCount) {
                        totalCount = count; // Use the explicit total from the table
                        break;
                    }
                }
            }
        }
    }

    console.log('Final parsed rows:', tableRows); // Debug log
    console.log('Total count:', totalCount); // Debug log

    // If no rows found, return empty table message
    if (tableRows.length === 0) {
        return `
            <div class="overflow-x-auto mt-3 mb-4">
                <div class="text-center text-gray-500 py-4">No table data available</div>
            </div>
        `;
    }

    // Create table HTML
    let tableHtml = `
        <div class="overflow-x-auto mt-3 mb-4">
            <table class="min-w-full bg-white border border-gray-300">
                <thead>
                    <tr>`;

    // Add headers
    headers.forEach(header => {
        tableHtml += `<th class="px-4 py-2 border-b text-left text-blue-600 font-medium">${header}</th>`;
    });

    tableHtml += `
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">`;

    // Add data rows
    tableRows.forEach(row => {
        tableHtml += `<tr class="hover:bg-gray-50">`;
        
        // Add each cell
        for (let i = 0; i < headers.length; i++) {
            const cellValue = row[i] || '';
            tableHtml += `<td class="px-4 py-2 text-black">${cellValue}</td>`;
        }
        
        tableHtml += `</tr>`;
    });

    // Add total row if we have a count
    if (totalCount > 0) {
        tableHtml += `
            <tr class="bg-gray-50 font-medium">
                <td colspan="${headers.length}" class="px-4 py-2">
                    Total Count = ${totalCount}
                </td>
            </tr>`;
    }

    tableHtml += `
                </tbody>
            </table>
        </div>`;

    return tableHtml;
}

function emphasizeKeywords(text, keywords) {
    console.log('Emphasizing keywords in text:', text, 'with keywords:', keywords);
    keywords.forEach(keyword => {
        const regex = new RegExp(keyword, 'gi');
        text = text.replace(regex, match => `<strong>${match}</strong>`);
    });
    return text;
}

function htmlspecialchars(text) {
    console.log('Sanitizing text:', text);
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function fetchMainDrivers() {
	console.log('Fetching main drivers...');
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Update the UI to reflect the current filter state
    document.getElementById('domainCategoryDropdown').value = domains;
    document.getElementById('dataIdDropdown').value = dataId;
    document.getElementById('sentimentDropdown').value = sentiment;

    fetch(`data.php?type=main-drivers&data_id=${dataId}&sentiment=${sentiment}&domain_category=${encodeURIComponent(domains)}`)
        .then(response => response.json())
        .then(data => {
            const mainDriverDropdown = document.getElementById('mainDriverDropdown');
            mainDriverDropdown.innerHTML = '<option value="">Select Main Driver</option>';

            // Check if we have any main drivers
            if (Object.keys(data).length > 0) {
                const drivers = Object.keys(data);
                drivers.forEach(driver => {
                    const option = document.createElement('option');
                    option.value = driver;
                    option.textContent = driver;
                    mainDriverDropdown.appendChild(option);
                });
                console.log(`Added ${drivers.length} main drivers to dropdown`);

                // Set the first main driver as default selection
                const firstDriver = drivers[0];
                mainDriverDropdown.value = firstDriver;
                console.log(`Setting default main driver to: ${firstDriver}`);

                // Trigger updateTable to display data for the first driver
                setTimeout(() => {
                    updateTable();
                }, 100);
            } else {
                console.log('No main drivers found for the current filter combination');
            }
        })
        .catch(error => console.error('Error fetching main drivers:', error));
}

function updateTable() {
	console.log('Updating L2 drivers distribution table...');
    const mainDriver = document.getElementById('mainDriverDropdown').value;
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Get new filter parameters
    const startDate = document.getElementById('startDateFilter').value;
    const endDate = document.getElementById('endDateFilter').value;
    const productType = document.getElementById('productTypeDropdown').value;
    const channelType = document.getElementById('channelTypeDropdown').value;
    const team = document.getElementById('teamDropdown').value;
    const resolutionStatus = document.getElementById('resolutionStatusDropdown').value;

    // Update the UI to reflect the current filter state
    document.getElementById('domainCategoryDropdown').value = domains;
    document.getElementById('dataIdDropdown').value = dataId;
    document.getElementById('sentimentDropdown').value = sentiment;

    // Clear the table if no main driver is selected
    if (!mainDriver) {
        const tbody = document.getElementById('subDriversTable').getElementsByTagName('tbody')[0];
        tbody.innerHTML = '';
        return;
    }

    // Build query parameters with all filters
    const params = new URLSearchParams();
    params.append('type', 'sub-drivers-contribution');
    params.append('data_id', dataId);
    params.append('sentiment', sentiment);
    params.append('domain_category', domains);

    // Add new filter parameters
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (productType && productType !== 'all') params.append('product_type', productType);
    if (channelType && channelType !== 'all') params.append('channel_type', channelType);
    if (team && team !== 'all') params.append('team', team);
    if (resolutionStatus && resolutionStatus !== 'all') params.append('resolution_status', resolutionStatus);

    fetch(`data.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('subDriversTable').getElementsByTagName('tbody')[0];
            tbody.innerHTML = '';

            if (Array.isArray(data[mainDriver])) {
                // Calculate total count for contribution percentage
                const totalCount = data[mainDriver]
				.sort((a, b) => b.total_count - a.total_count) // Sort in descending order
				.reduce((acc, sd) => acc + sd.total_count, 0);

                // Process and display each sub-driver
                data[mainDriver].forEach(subDriver => {
                    // Format contribution to 1 decimal place
                    const contribution = ((subDriver.total_count / totalCount) * 100).toFixed(1);

                    // Format percentages to 1 decimal place
                    const positivePercentage = parseFloat(subDriver.positive_percentage).toFixed(1);
                    const neutralPercentage = parseFloat(subDriver.neutral_percentage).toFixed(1);
                    const negativePercentage = parseFloat(subDriver.negative_percentage).toFixed(1);

                    // Format CSAT and NPS to always show 2 decimal places
                    const avgCsat = parseFloat(subDriver.avg_csat).toFixed(2);
                    const avgNps = parseFloat(subDriver.avg_nps).toFixed(2);

                    // Create table row
                    const row = document.createElement('tr');

                    // Set row background based on sentiment distribution
                    if (subDriver.positive_percentage > 60) {
                        row.classList.add('bg-green-50', 'dark:bg-green-900', 'dark:bg-opacity-20');
                    } else if (subDriver.negative_percentage > 60) {
                        row.classList.add('bg-red-50', 'dark:bg-red-900', 'dark:bg-opacity-20');
                    }

                    row.innerHTML = `
                        <td class="px-2 py-1 border-b" title="${mainDriver}">${mainDriver}</td>
                        <td class="px-2 py-1 border-b" title="${subDriver.sub_driver}">${subDriver.sub_driver}</td>
                        <td class="px-2 py-1 border-b text-center">${contribution}%</td>
                        <td class="px-2 py-1 border-b text-center bg-green-50 dark:bg-green-900 dark:bg-opacity-20">${subDriver.positive_count}</td>
                        <td class="px-2 py-1 border-b text-center bg-yellow-50 dark:bg-yellow-900 dark:bg-opacity-20">${subDriver.neutral_count}</td>
                        <td class="px-2 py-1 border-b text-center bg-red-50 dark:bg-red-900 dark:bg-opacity-20">${subDriver.negative_count}</td>
                        <td class="px-2 py-1 border-b text-center bg-green-50 dark:bg-green-900 dark:bg-opacity-20">${positivePercentage}%</td>
                        <td class="px-2 py-1 border-b text-center bg-yellow-50 dark:bg-yellow-900 dark:bg-opacity-20">${neutralPercentage}%</td>
                        <td class="px-2 py-1 border-b text-center bg-red-50 dark:bg-red-900 dark:bg-opacity-20">${negativePercentage}%</td>
                        <td class="px-2 py-1 border-b text-center">${avgCsat}</td>
                        <td class="px-2 py-1 border-b text-center">${avgNps}</td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                // Display a message if no data is available
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan="11" class="px-2 py-3 text-center text-gray-500">No data available for ${mainDriver}</td>`;
                tbody.appendChild(row);
            }
        })
        .catch(error => console.error('Error fetching sub-drivers contribution data:', error));
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Document loaded. Initializing dashboard...');

    // Initialize with default values using optimized functions
    debouncedFetchData();
    fetchMainDrivers();

    // Clean up intervals when page is unloaded
    window.addEventListener('beforeunload', function() {
        if (window.wordCloudRefreshInterval) {
            clearInterval(window.wordCloudRefreshInterval);
        }
    });

    const domainCategoryDropdown = document.getElementById('domainCategoryDropdown');
    const dataIdDropdown = document.getElementById('dataIdDropdown');
    const sentimentDropdown = document.getElementById('sentimentDropdown');
    const mainDriverDropdown = document.getElementById('mainDriverDropdown');

    // Variable to track if we're updating dropdowns programmatically
    let isUpdatingFilters = false;

    // Update data IDs when domain category changes
    domainCategoryDropdown.addEventListener('change', async () => {
        if (isUpdatingFilters) return;

        const domainCategory = domainCategoryDropdown.value;
        console.log('Domain category changed to:', domainCategory);

        // Show loading indicator for Key Insights Summary
        document.getElementById('keyInsightsSummary').innerHTML = `
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                <span class="ml-2">Loading insights summary...</span>
            </div>
        `;

        try {
            isUpdatingFilters = true;
            // Save current data ID selection
            const currentDataId = dataIdDropdown.value;
            // Save current sentiment selection
            const currentSentimentSelection = sentimentDropdown.value;

            if (domainCategory === 'all') {
                // If 'All Domain Categories' is selected, fetch all data IDs
                const response = await fetch(`data.php?type=data-ids`);
                if (!response.ok) throw new Error('Failed to fetch data IDs');
                const dataIds = await response.json();

                // Update data ID dropdown
                dataIdDropdown.innerHTML = '<option value="all">All Data IDs</option>';
                dataIds.forEach(id => {
                    const option = document.createElement('option');
                    option.value = id.data_id;
                    option.textContent = id.data_id;
                    if (id.data_id === currentDataId) {
                        option.selected = true;
                    }
                    dataIdDropdown.appendChild(option);
                });
            } else {
                // Fetch data IDs for selected domain category
                const response = await fetch(`data.php?type=data-ids&domain_category=${encodeURIComponent(domainCategory)}`);
                if (!response.ok) throw new Error('Failed to fetch data IDs');
                const dataIds = await response.json();

                // Update data ID dropdown while preserving current selection if possible
                dataIdDropdown.innerHTML = '<option value="all">All Data IDs</option>';
                let foundCurrentDataId = false;
                dataIds.forEach(id => {
                    const option = document.createElement('option');
                    option.value = id.data_id;
                    option.textContent = id.data_id;
                    if (id.data_id === currentDataId) {
                        option.selected = true;
                        foundCurrentDataId = true;
                    }
                    dataIdDropdown.appendChild(option);
                });

                // If the current data ID is not available in the new domain category,
                // reset to 'all' to avoid invalid filter combinations
                if (!foundCurrentDataId && currentDataId !== 'all') {
                    dataIdDropdown.value = 'all';
                }
            }

            // Always preserve sentiment selection
            sentimentDropdown.value = currentSentimentSelection;

            isUpdatingFilters = false;
            // Fetch new data with updated filters using debounced function
            debouncedFetchData();
            // Also update main drivers for the new filter combination
            fetchMainDrivers();
            // Update L2 drivers table
            updateTable();
        } catch (error) {
            console.error('Error updating data IDs:', error);
            isUpdatingFilters = false;
        }
    });

    // Update domain categories when data_id changes
    dataIdDropdown.addEventListener('change', async () => {
        if (isUpdatingFilters) return;

        const dataId = dataIdDropdown.value;
        console.log('Data ID changed to:', dataId);

        try {
            isUpdatingFilters = true;
            // Save current domain selection
            const currentDomainSelection = domainCategoryDropdown.value;
            // Save current sentiment selection
            const currentSentimentSelection = sentimentDropdown.value;

            // Important: Don't update the domain categories dropdown at all
            // This preserves the current domain selection in the UI

            // Always preserve sentiment selection
            sentimentDropdown.value = currentSentimentSelection;

            // Show loading indicator for Key Insights Summary
            document.getElementById('keyInsightsSummary').innerHTML = `
                <div class="flex items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-2">Loading insights summary...</span>
                </div>
            `;

            isUpdatingFilters = false;
            // Fetch new data with updated filters using debounced function
            debouncedFetchData();
            // Also update main drivers for the new filter combination
            fetchMainDrivers();
            // Update L2 drivers table
            updateTable();
        } catch (error) {
            console.error('Error updating domain categories:', error);
            isUpdatingFilters = false;
        }
    });

    // Handle sentiment changes
    sentimentDropdown.addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('Sentiment changed to:', sentimentDropdown.value);
        debouncedFetchData();
        // Also update main drivers for the new sentiment filter
        fetchMainDrivers();
        // Update L2 drivers table
        updateTable();
    });

    // Handle main driver changes
    mainDriverDropdown.addEventListener('change', () => {
        console.log('Main driver changed to:', mainDriverDropdown.value);
        updateTable();
    });

    // Handle new filter changes
    document.getElementById('startDateFilter').addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('Start date changed to:', document.getElementById('startDateFilter').value);
        debouncedFetchData();
        fetchMainDrivers();
        updateTable();
    });

    document.getElementById('endDateFilter').addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('End date changed to:', document.getElementById('endDateFilter').value);
        debouncedFetchData();
        fetchMainDrivers();
        updateTable();
    });

    document.getElementById('productTypeDropdown').addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('Product type changed to:', document.getElementById('productTypeDropdown').value);
        debouncedFetchData();
        fetchMainDrivers();
        updateTable();
    });

    document.getElementById('channelTypeDropdown').addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('Channel type changed to:', document.getElementById('channelTypeDropdown').value);
        debouncedFetchData();
        fetchMainDrivers();
        updateTable();
    });

    document.getElementById('teamDropdown').addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('Team changed to:', document.getElementById('teamDropdown').value);
        debouncedFetchData();
        fetchMainDrivers();
        updateTable();
    });

    document.getElementById('resolutionStatusDropdown').addEventListener('change', () => {
        if (isUpdatingFilters) return;
        console.log('Resolution status changed to:', document.getElementById('resolutionStatusDropdown').value);
        debouncedFetchData();
        fetchMainDrivers();
        updateTable();
    });

    // Functions to populate new filter dropdowns
    async function populateFilterDropdowns() {
        try {
            // Populate Product Type dropdown
            const partnersResponse = await fetch('data.php?type=filter-options-partners');
            if (partnersResponse.ok) {
                const partners = await partnersResponse.json();
                const productDropdown = document.getElementById('productTypeDropdown');
                productDropdown.innerHTML = '<option value="all">All Products</option>';
                partners.forEach(partner => {
                    const option = document.createElement('option');
                    option.value = partner.partner;
                    option.textContent = partner.partner;
                    productDropdown.appendChild(option);
                });
            }

            // Populate Channel Type dropdown
            const lobsResponse = await fetch('data.php?type=filter-options-lobs');
            if (lobsResponse.ok) {
                const lobs = await lobsResponse.json();
                const channelDropdown = document.getElementById('channelTypeDropdown');
                channelDropdown.innerHTML = '<option value="all">All Channels</option>';
                lobs.forEach(lob => {
                    const option = document.createElement('option');
                    option.value = lob.lob;
                    option.textContent = lob.lob;
                    channelDropdown.appendChild(option);
                });
            }

            // Populate Teams dropdown
            const teamsResponse = await fetch('data.php?type=filter-options-teams');
            if (teamsResponse.ok) {
                const teams = await teamsResponse.json();
                const teamDropdown = document.getElementById('teamDropdown');
                teamDropdown.innerHTML = '<option value="all">All Teams</option>';
                teams.forEach(team => {
                    const option = document.createElement('option');
                    option.value = team.team;
                    option.textContent = team.team;
                    teamDropdown.appendChild(option);
                });
            }

            // Populate Resolution Status dropdown
            const statusesResponse = await fetch('data.php?type=filter-options-resolution-statuses');
            if (statusesResponse.ok) {
                const statuses = await statusesResponse.json();
                const statusDropdown = document.getElementById('resolutionStatusDropdown');
                statusDropdown.innerHTML = '<option value="all">All Status</option>';
                statuses.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.status;
                    option.textContent = status.status;
                    statusDropdown.appendChild(option);
                });
            }

            // Set date range defaults
            const dateRangeResponse = await fetch('data.php?type=filter-options-date-range');
            if (dateRangeResponse.ok) {
                const dateRange = await dateRangeResponse.json();
                if (dateRange.min_date) {
                    document.getElementById('startDateFilter').min = dateRange.min_date;
                }
                if (dateRange.max_date) {
                    document.getElementById('endDateFilter').max = dateRange.max_date;
                }
            }

            console.log('All filter dropdowns populated successfully');
        } catch (error) {
            console.error('Error populating filter dropdowns:', error);
        }
    }

    // Add refresh button handler
    document.getElementById('refreshButton').addEventListener('click', async function() {
        console.log('Refreshing data...');
        isUpdatingFilters = true;

        // Show loading indicator for Key Insights Summary
        document.getElementById('keyInsightsSummary').innerHTML = `
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                <span class="ml-2">Loading insights summary...</span>
            </div>
        `;

        try {
            // Use filter coordinator to clear all filters if available
            if (window.filterCoordinator) {
                console.log('Using filter coordinator to clear filters');
                window.filterCoordinator.clearAllFilters();

                // Clear filter coordinator cache
                window.filterCoordinator.clearCache();

                // Wait a moment for filter coordinator to complete
                await new Promise(resolve => setTimeout(resolve, 500));
            } else {
                console.log('Filter coordinator not available, using manual reset');

                // Manual reset as fallback
                dataIdDropdown.value = 'all';
                domainCategoryDropdown.value = 'all';
                sentimentDropdown.value = 'all';

                // Reset new filters
                document.getElementById('startDateFilter').value = '';
                document.getElementById('endDateFilter').value = '';
                document.getElementById('productTypeDropdown').value = 'all';
                document.getElementById('channelTypeDropdown').value = 'all';
                document.getElementById('teamDropdown').value = 'all';
                document.getElementById('resolutionStatusDropdown').value = 'all';

                // Fetch all domain categories
                const domainResponse = await fetch('data.php?type=domain-categories');
                if (!domainResponse.ok) throw new Error('Failed to fetch domain categories');
                const domains = await domainResponse.json();

                // Rebuild domain category dropdown
                domainCategoryDropdown.innerHTML = '<option value="all">All Domain Categories</option>';
                domains.forEach(domain => {
                    const option = document.createElement('option');
                    option.value = domain;
                    option.textContent = domain;
                    domainCategoryDropdown.appendChild(option);
                });

                // Fetch all data IDs
                const dataResponse = await fetch('data.php?type=data-ids');
                if (!dataResponse.ok) throw new Error('Failed to fetch data IDs');
                const dataIds = await dataResponse.json();

                // Rebuild data ID dropdown
                dataIdDropdown.innerHTML = '<option value="all">All Data IDs</option>';
                dataIds.forEach(id => {
                    const option = document.createElement('option');
                    option.value = id.data_id;
                    option.textContent = id.data_id;
                    dataIdDropdown.appendChild(option);
                });

                // Repopulate new filter dropdowns
                await populateFilterDropdowns();
            }

            isUpdatingFilters = false;

            // Clear dashboard cache for fresh data
            if (window.dashboardCache) {
                window.dashboardCache.clear();
            }

            // Fetch fresh data using optimized function
            debouncedFetchData();

            // Update main drivers
            fetchMainDrivers();

            console.log('All filters reset successfully');
        } catch (error) {
            console.error('Error resetting filters:', error);
            isUpdatingFilters = false;
        }
    });

    // Initialize global variables for time range and view type
    window.currentTimeRange = 'all';  // Default to 'all' for time range
    window.currentViewType = 'daily';

    // Fetch time series data on page load
    setTimeout(() => {
        fetchTimeSeriesData();
    }, 500);

    // Populate filter dropdowns on page load
    setTimeout(() => {
        populateFilterDropdowns();
    }, 100);

    // Add event listeners for time range buttons
    document.querySelectorAll('.time-range-btn').forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            document.querySelectorAll('.time-range-btn').forEach(btn => {
                btn.classList.remove('bg-positive-light', 'text-positive', 'dark:bg-green-200', 'dark:text-green-800');
                btn.classList.add('bg-gray-100', 'text-gray-600', 'dark:bg-gray-700', 'dark:text-white');
            });

            // Add active class to clicked button
            this.classList.remove('bg-gray-100', 'text-gray-600', 'dark:bg-gray-700', 'dark:text-white');
            this.classList.add('bg-positive-light', 'text-positive', 'dark:bg-green-200', 'dark:text-green-800');

            // Get the days value from the button's data attribute
            const days = this.getAttribute('data-days');
            window.currentTimeRange = days;

            // Fetch time series data with the new parameters
            fetchTimeSeriesData();
        });
    });

    // Add event listeners for view type buttons
    document.querySelectorAll('.view-type-btn').forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            document.querySelectorAll('.view-type-btn').forEach(btn => {
                btn.classList.remove('bg-blue-100', 'text-blue-600', 'dark:bg-blue-800', 'dark:text-blue-200');
                btn.classList.add('bg-gray-100', 'text-gray-600', 'dark:bg-gray-700', 'dark:text-white');
            });

            // Add active class to clicked button
            this.classList.remove('bg-gray-100', 'text-gray-600', 'dark:bg-gray-700', 'dark:text-white');
            this.classList.add('bg-blue-100', 'text-blue-600', 'dark:bg-blue-800', 'dark:text-blue-200');

            // Get the view type from the button's data attribute
            const viewType = this.getAttribute('data-view');
            window.currentViewType = viewType;

            // Fetch time series data with the new parameters
            fetchTimeSeriesData();
        });
    });

    // Function to fetch time series data with current parameters
    function fetchTimeSeriesData() {
        const domains = document.getElementById('domainCategoryDropdown').value;
        const dataId = document.getElementById('dataIdDropdown').value;
        const sentiment = document.getElementById('sentimentDropdown').value;
        const days = window.currentTimeRange;
        const viewType = window.currentViewType;

        // Get new filter values
        const startDate = document.getElementById('startDateFilter').value;
        const endDate = document.getElementById('endDateFilter').value;
        const productType = document.getElementById('productTypeDropdown').value;
        const channelType = document.getElementById('channelTypeDropdown').value;
        const team = document.getElementById('teamDropdown').value;
        const resolutionStatus = document.getElementById('resolutionStatusDropdown').value;

        // Build query parameters
        const params = new URLSearchParams();
        params.append('type', 'time-series-sentiments');
        params.append('data_id', dataId || 'all');
        params.append('sentiment', sentiment || 'all');
        params.append('domain_category', domains || 'all');
        params.append('days', days);
        params.append('view_type', viewType);

        // Add new filter parameters
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (productType && productType !== 'all') params.append('product_type', productType);
        if (channelType && channelType !== 'all') params.append('channel_type', channelType);
        if (team && team !== 'all') params.append('team', team);
        if (resolutionStatus && resolutionStatus !== 'all') params.append('resolution_status', resolutionStatus);

        fetch(`data.php?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                updateTimeSeriesChart(data, days, viewType);
            })
            .catch(error => console.error('Error fetching time series data:', error));
    }


});

function throttle(func, limit) {
    let lastFunc;
    let lastRan;
    return function () {
        const context = this;
        const args = arguments;
        if (!lastRan) {
            func.apply(context, args);
            lastRan = Date.now();
        } else {
            clearTimeout(lastFunc);
            lastFunc = setTimeout(function () {
                if (Date.now() - lastRan >= limit) {
                    func.apply(context, args);
                    lastRan = Date.now();
                }
            }, limit - (Date.now() - lastRan));
        }
    };
}

// Helper function to get chart title based on view type and days
function getChartTitle(days, viewType) {
    if (days === 'all') {
        return `Sentiment Trend (All Time - ${viewType.charAt(0).toUpperCase() + viewType.slice(1)} View)`;
    } else {
        return `Sentiment Trend (Last ${days} Days - ${viewType.charAt(0).toUpperCase() + viewType.slice(1)} View)`;
    }
}

// Helper function to get x-axis title based on view type
function getAxisTitle(viewType) {
    switch(viewType) {
        case 'daily':
            return 'Date (Based on Response Submit Date)';
        case 'monthly':
            return 'Month/Year (Based on Response Submit Date)';
        case 'yearly':
            return 'Year (Based on Response Submit Date)';
        default:
            return 'Date (Based on Response Submit Date)';
    }
}

// Function to update LOB Sentiment Chart
function updateLobSentimentChart(data) {
    console.log('Updating LOB sentiment chart with data:', data);

    // Store the original data globally so we can access it when navigating pages
    window.lobData = data || {};

    // Get current filter values
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Initialize variables for chart data
    let lobLabels = [];
    let lobPositive = [];
    let lobNeutral = [];
    let lobNegative = [];

    // Ensure data is an object with content
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        console.warn('No LOB sentiment data available');
        // Display a message in the chart container
        const container = document.querySelector('#lobChart').closest('.chart-container');
        if (container) {
            // Clear any existing message but keep the canvas
            const existingMessage = container.querySelector('.text-center.text-gray-500');
            if (existingMessage) {
                existingMessage.remove();
            }

            // Make sure canvas exists
            if (!container.querySelector('#lobChart')) {
                container.innerHTML = '<canvas id="lobChart"></canvas>';
            }

            // Add the message
            const message = document.createElement('div');
            message.className = 'text-center text-gray-500 py-4';
            message.textContent = 'No LOB data available for the selected filters';
            container.appendChild(message);
        }
        return;
    }

    // Process the data for the chart
    console.log('Processing LOB data keys:', Object.keys(data));

    try {
        // Create an array of objects with LOB data and total counts
        const lobItems = Object.keys(data).map(lob => {
            const positive = data[lob]?.Positive || 0;
            const neutral = data[lob]?.Neutral || 0;
            const negative = data[lob]?.Negative || 0;
            const total = positive + neutral + negative;
            return { lob, positive, neutral, negative, total };
        });

        // Sort by total count in descending order
        const sortedLobs = lobItems.sort((a, b) => b.total - a.total);

        // Extract the sorted data into separate arrays
        lobLabels = sortedLobs.map(item => item.lob);
        lobPositive = sortedLobs.map(item => item.positive);
        lobNeutral = sortedLobs.map(item => item.neutral);
        lobNegative = sortedLobs.map(item => item.negative);

        console.log('Sorted LOB data successfully');
    } catch (error) {
        console.error('Error sorting LOB data:', error);
        // Fallback to unsorted data
        lobLabels = Object.keys(data);
        lobPositive = lobLabels.map(lob => data[lob]?.Positive || 0);
        lobNeutral = lobLabels.map(lob => data[lob]?.Neutral || 0);
        lobNegative = lobLabels.map(lob => data[lob]?.Negative || 0);
        console.log('Using unsorted LOB data due to error');
    }

    const totalRecords = lobLabels.length;
    const recordsPerPage = 10;

    // Initialize currentPage if it doesn't exist yet
    if (typeof window.lobCurrentPage === 'undefined') {
        window.lobCurrentPage = 0;
    }

    let currentPage = window.lobCurrentPage;

    function getPaginatedData(page) {
        const start = page * recordsPerPage;
        const end = Math.min(start + recordsPerPage, totalRecords);
        return {
            labels: lobLabels.slice(start, end),
            positive: lobPositive.slice(start, end),
            neutral: lobNeutral.slice(start, end),
            negative: lobNegative.slice(start, end)
        };
    }

    function updateChart(page) {
        // Get paginated data for the current page
        const { labels, positive, neutral, negative } = getPaginatedData(page);

        // Get the canvas element
        const chartCanvas = document.getElementById('lobChart');
        if (!chartCanvas) {
            console.error('Cannot find lobChart canvas element');
            return;
        }

        const ctx = chartCanvas.getContext('2d');

        // Clear the canvas first
        ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);

        // Destroy existing chart if it exists
        if (window.lobChart && typeof window.lobChart.destroy === 'function') {
            window.lobChart.destroy();
        }

        // Set window.lobChart to null before creating a new one
        window.lobChart = null;

        // Create a new chart
        window.lobChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    { label: 'Positive', data: positive, backgroundColor: '#10B981' },
                    { label: 'Neutral', data: neutral, backgroundColor: '#F59E0B' },
                    { label: 'Negative', data: negative, backgroundColor: '#EF4444' }
                ]
            },
            options: {
                responsive: true,
                indexAxis: 'y',
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'center',
                        labels: {
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    title: {
                        display: true,
                        text: 'Sentiment Distribution by Channel',
                        font: {
                            size: 14
                        },
                        padding: {
                            bottom: 10
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        beginAtZero: true,
                        grid: {
                            color: '#e5e5e5'
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        stacked: true,
                        ticks: {
                            autoSkip: false,
                            padding: 5
                        },
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    }
                },
                layout: {
                    padding: {
                        left: 10,
                        right: 10,
                        top: 0,
                        bottom: 10
                    }
                }
            }
        });
    }

    // Remove previous event listeners to avoid duplicates
    const nextButton = document.getElementById('nextPageLob');
    const prevButton = document.getElementById('prevPageLob');

    // Clone and replace the buttons to remove old event listeners
    const nextButtonClone = nextButton.cloneNode(true);
    const prevButtonClone = prevButton.cloneNode(true);
    nextButton.parentNode.replaceChild(nextButtonClone, nextButton);
    prevButton.parentNode.replaceChild(prevButtonClone, prevButton);

    // Add new event listeners
    document.getElementById('nextPageLob').addEventListener('click', () => {
        if ((window.lobCurrentPage + 1) * recordsPerPage < totalRecords) {
            window.lobCurrentPage++;
            updateChart(window.lobCurrentPage);
            console.log('LOB chart updated to page:', window.lobCurrentPage + 1);
        }
    });

    document.getElementById('prevPageLob').addEventListener('click', () => {
        if (window.lobCurrentPage > 0) {
            window.lobCurrentPage--;
            updateChart(window.lobCurrentPage);
            console.log('LOB chart updated to page:', window.lobCurrentPage + 1);
        }
    });

    updateChart(window.lobCurrentPage);
}

// Function to update Vendor Sentiment Chart
function updateVendorSentimentChart(data) {
    console.log('Updating Vendor sentiment chart with data:', data);

    // Store the original data globally so we can access it when navigating pages
    window.vendorData = data || {};

    // Get current filter values
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Initialize variables for chart data
    let vendorLabels = [];
    let vendorPositive = [];
    let vendorNeutral = [];
    let vendorNegative = [];

    // Ensure data is an object with content
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        console.warn('No Vendor sentiment data available');
        // Display a message in the chart container
        const container = document.querySelector('#vendorChart').closest('.chart-container');
        if (container) {
            // Clear any existing message but keep the canvas
            const existingMessage = container.querySelector('.text-center.text-gray-500');
            if (existingMessage) {
                existingMessage.remove();
            }

            // Make sure canvas exists
            if (!container.querySelector('#vendorChart')) {
                container.innerHTML = '<canvas id="vendorChart"></canvas>';
            }

            // Add the message
            const message = document.createElement('div');
            message.className = 'text-center text-gray-500 py-4';
            message.textContent = 'No Vendor data available for the selected filters';
            container.appendChild(message);
        }
        return;
    }

    // Process the data for the chart
    console.log('Processing Vendor data keys:', Object.keys(data));

    try {
        // Create an array of objects with Vendor data and total counts
        const vendorItems = Object.keys(data).map(vendor => {
            const positive = data[vendor]?.Positive || 0;
            const neutral = data[vendor]?.Neutral || 0;
            const negative = data[vendor]?.Negative || 0;
            const total = positive + neutral + negative;
            return { vendor, positive, neutral, negative, total };
        });

        // Sort by total count in descending order
        const sortedVendors = vendorItems.sort((a, b) => b.total - a.total);

        // Extract the sorted data into separate arrays
        vendorLabels = sortedVendors.map(item => item.vendor);
        vendorPositive = sortedVendors.map(item => item.positive);
        vendorNeutral = sortedVendors.map(item => item.neutral);
        vendorNegative = sortedVendors.map(item => item.negative);

        console.log('Sorted Vendor data successfully');
    } catch (error) {
        console.error('Error sorting Vendor data:', error);
        // Fallback to unsorted data
        vendorLabels = Object.keys(data);
        vendorPositive = vendorLabels.map(vendor => data[vendor]?.Positive || 0);
        vendorNeutral = vendorLabels.map(vendor => data[vendor]?.Neutral || 0);
        vendorNegative = vendorLabels.map(vendor => data[vendor]?.Negative || 0);
        console.log('Using unsorted Vendor data due to error');
    }

    const totalRecords = vendorLabels.length;
    const recordsPerPage = 10;

    // Initialize currentPage if it doesn't exist yet
    if (typeof window.vendorCurrentPage === 'undefined') {
        window.vendorCurrentPage = 0;
    }

    let currentPage = window.vendorCurrentPage;

    function getPaginatedData(page) {
        const start = page * recordsPerPage;
        const end = Math.min(start + recordsPerPage, totalRecords);
        return {
            labels: vendorLabels.slice(start, end),
            positive: vendorPositive.slice(start, end),
            neutral: vendorNeutral.slice(start, end),
            negative: vendorNegative.slice(start, end)
        };
    }

    function updateChart(page) {
        const { labels, positive, neutral, negative } = getPaginatedData(page);
        const chartCanvas = document.getElementById('vendorChart');

        if (!chartCanvas) {
            console.error('Cannot find vendorChart canvas element');
            return;
        }

        const ctx = chartCanvas.getContext('2d');

        // Clear the canvas first
        ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);

        // Destroy existing chart if it exists
        if (window.vendorChart && typeof window.vendorChart.destroy === 'function') {
            window.vendorChart.destroy();
        }

        // Set window.vendorChart to null before creating a new one
        window.vendorChart = null;

        // Create a new chart
        window.vendorChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    { label: 'Positive', data: positive, backgroundColor: '#10B981' },
                    { label: 'Neutral', data: neutral, backgroundColor: '#F59E0B' },
                    { label: 'Negative', data: negative, backgroundColor: '#EF4444' }
                ]
            },
            options: {
                responsive: true,
                indexAxis: 'y',
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'center',
                        labels: {
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    title: {
                        display: true,
                        text: 'Sentiment Distribution by Vendor',
                        font: {
                            size: 14
                        },
                        padding: {
                            bottom: 10
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        beginAtZero: true,
                        grid: {
                            color: '#e5e5e5'
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        stacked: true,
                        ticks: {
                            autoSkip: false,
                            padding: 5
                        },
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    }
                },
                layout: {
                    padding: {
                        left: 10,
                        right: 10,
                        top: 0,
                        bottom: 10
                    }
                }
            }
        });
    }

    // Remove previous event listeners to avoid duplicates
    const nextButton = document.getElementById('nextPageVendor');
    const prevButton = document.getElementById('prevPageVendor');

    // Clone and replace the buttons to remove old event listeners
    const nextButtonClone = nextButton.cloneNode(true);
    const prevButtonClone = prevButton.cloneNode(true);
    nextButton.parentNode.replaceChild(nextButtonClone, nextButton);
    prevButton.parentNode.replaceChild(prevButtonClone, prevButton);

    // Add new event listeners
    document.getElementById('nextPageVendor').addEventListener('click', () => {
        if ((window.vendorCurrentPage + 1) * recordsPerPage < totalRecords) {
            window.vendorCurrentPage++;
            updateChart(window.vendorCurrentPage);
            console.log('Vendor chart updated to page:', window.vendorCurrentPage + 1);
        }
    });

    document.getElementById('prevPageVendor').addEventListener('click', () => {
        if (window.vendorCurrentPage > 0) {
            window.vendorCurrentPage--;
            updateChart(window.vendorCurrentPage);
            console.log('Vendor chart updated to page:', window.vendorCurrentPage + 1);
        }
    });

    updateChart(window.vendorCurrentPage);
}

// Function to update Location Sentiment Chart
function updateLocationSentimentChart(data) {
    console.log('Updating Location sentiment chart with data:', data);

    // Store the original data globally so we can access it when navigating pages
    window.locationData = data || {};

    // Get current filter values
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Ensure data is an object
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        console.warn('No Location sentiment data available');
        // Display a message in the chart container
        const container = document.querySelector('#locationChart').closest('.chart-container');
        if (container) {
            // Clear any existing content first
            container.innerHTML = '<canvas id="locationChart"></canvas>';

            // Add the message
            const message = document.createElement('div');
            message.className = 'text-center text-gray-500 py-4';
            message.textContent = 'No Location data available for the selected filters';
            container.appendChild(message);
        }
        return;
    }

    // Ensure we have valid data to work with
    console.log('Processing Location data keys:', Object.keys(data));

    // Declare variables outside the try/catch block
    let locationLabels, locationPositive, locationNeutral, locationNegative;

    // Fallback to original data structure if our sorting logic fails
    try {
        let sortedLocations = Object.keys(data).map(location => {
            // Use optional chaining and nullish coalescing to safely access data
            const positive = data[location]?.Positive ?? 0;
            const neutral = data[location]?.Neutral ?? 0;
            const negative = data[location]?.Negative ?? 0;
            const total = positive + neutral + negative;
            return { location, positive, neutral, negative, total };
        }).sort((a, b) => b.total - a.total);

        // Extract sorted arrays
        locationLabels = sortedLocations.map(item => item.location);
        locationPositive = sortedLocations.map(item => item.positive);
        locationNeutral = sortedLocations.map(item => item.neutral);
        locationNegative = sortedLocations.map(item => item.negative);
    } catch (error) {
        console.error('Error sorting Location data:', error);
        // Fallback to unsorted data
        locationLabels = Object.keys(data);
        locationPositive = locationLabels.map(location => data[location]?.Positive || 0);
        locationNeutral = locationLabels.map(location => data[location]?.Neutral || 0);
        locationNegative = locationLabels.map(location => data[location]?.Negative || 0);
    }

    const totalRecords = locationLabels.length;
    const recordsPerPage = 10;

    // Initialize currentPage if it doesn't exist yet
    if (typeof window.locationCurrentPage === 'undefined') {
        window.locationCurrentPage = 0;
    }

    let currentPage = window.locationCurrentPage;

    function getPaginatedData(page) {
        const start = page * recordsPerPage;
        const end = Math.min(start + recordsPerPage, totalRecords);
        return {
            labels: locationLabels.slice(start, end),
            positive: locationPositive.slice(start, end),
            neutral: locationNeutral.slice(start, end),
            negative: locationNegative.slice(start, end)
        };
    }

    function updateChart(page) {
        const { labels, positive, neutral, negative } = getPaginatedData(page);
        const chartCanvas = document.getElementById('locationChart');

        if (!chartCanvas) {
            console.error('Cannot find locationChart canvas element');
            return;
        }

        const ctx = chartCanvas.getContext('2d');

        // Clear the canvas first
        ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);

        // Destroy existing chart if it exists
        if (window.locationChart && typeof window.locationChart.destroy === 'function') {
            window.locationChart.destroy();
        }

        // Set window.locationChart to null before creating a new one
        window.locationChart = null;

        // Create a new chart
        window.locationChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    { label: 'Positive', data: positive, backgroundColor: '#10B981' },
                    { label: 'Neutral', data: neutral, backgroundColor: '#F59E0B' },
                    { label: 'Negative', data: negative, backgroundColor: '#EF4444' }
                ]
            },
            options: {
                responsive: true,
                indexAxis: 'y',
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'center',
                        labels: {
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    title: {
                        display: true,
                        text: 'Sentiment Distribution by Site',
                        font: {
                            size: 14
                        },
                        padding: {
                            bottom: 10
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        beginAtZero: true,
                        grid: {
                            color: '#e5e5e5'
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        stacked: true,
                        ticks: {
                            autoSkip: false,
                            padding: 5
                        },
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    }
                },
                layout: {
                    padding: {
                        left: 10,
                        right: 10,
                        top: 0,
                        bottom: 10
                    }
                }
            }
        });
    }

    // Remove previous event listeners to avoid duplicates
    const nextButton = document.getElementById('nextPageLocation');
    const prevButton = document.getElementById('prevPageLocation');

    // Clone and replace the buttons to remove old event listeners
    const nextButtonClone = nextButton.cloneNode(true);
    const prevButtonClone = prevButton.cloneNode(true);
    nextButton.parentNode.replaceChild(nextButtonClone, nextButton);
    prevButton.parentNode.replaceChild(prevButtonClone, prevButton);

    // Add new event listeners
    document.getElementById('nextPageLocation').addEventListener('click', () => {
        if ((window.locationCurrentPage + 1) * recordsPerPage < totalRecords) {
            window.locationCurrentPage++;
            updateChart(window.locationCurrentPage);
            console.log('Location chart updated to page:', window.locationCurrentPage + 1);
        }
    });

    document.getElementById('prevPageLocation').addEventListener('click', () => {
        if (window.locationCurrentPage > 0) {
            window.locationCurrentPage--;
            updateChart(window.locationCurrentPage);
            console.log('Location chart updated to page:', window.locationCurrentPage + 1);
        }
    });

    updateChart(window.locationCurrentPage);
}

// Function to update Partner Sentiment Chart
function updatePartnerSentimentChart(data) {
    console.log('Updating Partner sentiment chart with data:', data);

    // Store the original data globally so we can access it when navigating pages
    window.partnerData = data || {};

    // Get current filter values
    const domains = document.getElementById('domainCategoryDropdown').value;
    const dataId = document.getElementById('dataIdDropdown').value;
    const sentiment = document.getElementById('sentimentDropdown').value;

    // Ensure data is an object
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        console.warn('No Partner sentiment data available');
        // Display a message in the chart container
        const container = document.querySelector('#partnerChart').closest('.chart-container');
        if (container) {
            // Clear any existing content first
            container.innerHTML = '<canvas id="partnerChart"></canvas>';

            // Add the message
            const message = document.createElement('div');
            message.className = 'text-center text-gray-500 py-4';
            message.textContent = 'No Partner data available for the selected filters';
            container.appendChild(message);
        }
        return;
    }

    // Ensure we have valid data to work with
    console.log('Processing Partner data keys:', Object.keys(data));

    // Declare variables outside the try/catch block
    let partnerLabels, partnerPositive, partnerNeutral, partnerNegative;

    // Fallback to original data structure if our sorting logic fails
    try {
        let sortedPartners = Object.keys(data).map(partner => {
            // Use optional chaining and nullish coalescing to safely access data
            const positive = data[partner]?.Positive ?? 0;
            const neutral = data[partner]?.Neutral ?? 0;
            const negative = data[partner]?.Negative ?? 0;
            const total = positive + neutral + negative;
            return { partner, positive, neutral, negative, total };
        }).sort((a, b) => b.total - a.total);

        // Extract sorted arrays
        partnerLabels = sortedPartners.map(item => item.partner);
        partnerPositive = sortedPartners.map(item => item.positive);
        partnerNeutral = sortedPartners.map(item => item.neutral);
        partnerNegative = sortedPartners.map(item => item.negative);
    } catch (error) {
        console.error('Error sorting Partner data:', error);
        // Fallback to unsorted data
        partnerLabels = Object.keys(data);
        partnerPositive = partnerLabels.map(partner => data[partner]?.Positive || 0);
        partnerNeutral = partnerLabels.map(partner => data[partner]?.Neutral || 0);
        partnerNegative = partnerLabels.map(partner => data[partner]?.Negative || 0);
    }

    const totalRecords = partnerLabels.length;
    const recordsPerPage = 10;

    // Initialize currentPage if it doesn't exist yet
    if (typeof window.partnerCurrentPage === 'undefined') {
        window.partnerCurrentPage = 0;
    }

    let currentPage = window.partnerCurrentPage;

    function getPaginatedData(page) {
        const start = page * recordsPerPage;
        const end = Math.min(start + recordsPerPage, totalRecords);
        return {
            labels: partnerLabels.slice(start, end),
            positive: partnerPositive.slice(start, end),
            neutral: partnerNeutral.slice(start, end),
            negative: partnerNegative.slice(start, end)
        };
    }

    function updateChart(page) {
        const { labels, positive, neutral, negative } = getPaginatedData(page);
        const chartCanvas = document.getElementById('partnerChart');

        if (!chartCanvas) {
            console.error('Cannot find partnerChart canvas element');
            return;
        }

        const ctx = chartCanvas.getContext('2d');

        // Clear the canvas first
        ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);

        // Destroy existing chart if it exists
        if (window.partnerChart && typeof window.partnerChart.destroy === 'function') {
            window.partnerChart.destroy();
        }

        // Set window.partnerChart to null before creating a new one
        window.partnerChart = null;

        // Create a new chart
        window.partnerChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    { label: 'Positive', data: positive, backgroundColor: '#10B981' },
                    { label: 'Neutral', data: neutral, backgroundColor: '#F59E0B' },
                    { label: 'Negative', data: negative, backgroundColor: '#EF4444' }
                ]
            },
            options: {
                responsive: true,
                indexAxis: 'y',
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'center',
                        labels: {
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    title: {
                        display: true,
                        text: 'Sentiment Distribution by Product type',
                        font: {
                            size: 14
                        },
                        padding: {
                            bottom: 10
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        beginAtZero: true,
                        grid: {
                            color: '#e5e5e5'
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        stacked: true,
                        ticks: {
                            autoSkip: false,
                            padding: 5
                        },
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        }
                    }
                },
                layout: {
                    padding: {
                        left: 10,
                        right: 10,
                        top: 0,
                        bottom: 10
                    }
                }
            }
        });
    }

    // Remove previous event listeners to avoid duplicates
    const nextButton = document.getElementById('nextPagePartner');
    const prevButton = document.getElementById('prevPagePartner');

    // Clone and replace the buttons to remove old event listeners
    const nextButtonClone = nextButton.cloneNode(true);
    const prevButtonClone = prevButton.cloneNode(true);
    nextButton.parentNode.replaceChild(nextButtonClone, nextButton);
    prevButton.parentNode.replaceChild(prevButtonClone, prevButton);

    // Add new event listeners
    document.getElementById('nextPagePartner').addEventListener('click', () => {
        if ((window.partnerCurrentPage + 1) * recordsPerPage < totalRecords) {
            window.partnerCurrentPage++;
            updateChart(window.partnerCurrentPage);
            console.log('Partner chart updated to page:', window.partnerCurrentPage + 1);
        }
    });

    document.getElementById('prevPagePartner').addEventListener('click', () => {
        if (window.partnerCurrentPage > 0) {
            window.partnerCurrentPage--;
            updateChart(window.partnerCurrentPage);
            console.log('Partner chart updated to page:', window.partnerCurrentPage + 1);
        }
    });

    updateChart(window.partnerCurrentPage);
}

document.addEventListener('DOMContentLoaded', function() {
    // Export functionality
    document.getElementById('exportReportButton').addEventListener('click', function() {
        const exportOptions = document.getElementById('exportOptions');
        exportOptions.classList.toggle('hidden');
    });

    document.getElementById('exportCSV').addEventListener('click', () => exportData('csv'));
    document.getElementById('exportExcel').addEventListener('click', () => exportData('excel'));

    // OLS Analysis Integration
    document.getElementById('runOlsAnalysis').addEventListener('click', async function() {
        // Collecting filter values from correct dashboard element IDs
        const filters = {
            start_date: document.getElementById('startDateFilter')?.value || '',
            end_date: document.getElementById('endDateFilter')?.value || '',
            product_type: document.getElementById('productTypeDropdown')?.value || '',
            channel_type: document.getElementById('channelTypeDropdown')?.value || '',
            team: document.getElementById('teamDropdown')?.value || '',
            resolution_status: document.getElementById('resolutionStatusDropdown')?.value || '',
            domain_category: document.getElementById('domainCategoryDropdown')?.value || '',
            data_id: document.getElementById('dataIdDropdown')?.value || '',
            sentiment: document.getElementById('sentimentDropdown')?.value || ''
        };

        // Show loading indicators
        window.olsAnalysis.showLoading('csat-ols-content', 'Analyzing CSAT data...');
        window.olsAnalysis.showLoading('nps-ols-content', 'Analyzing NPS data...');

        // Determine if dark mode is active
        const isDarkMode = document.documentElement.classList.contains('dark');

        try {
            const csatData = await window.olsAnalysis.runCSATAnalysis(filters, isDarkMode);
            window.olsAnalysis.displayResults('csat-ols-content', csatData, 'CSAT');
        } catch (err) {
            window.olsAnalysis.showError('csat-ols-content', err.message);
        }

        try {
            const npsData = await window.olsAnalysis.runNPSAnalysis(filters, isDarkMode);
            window.olsAnalysis.displayResults('nps-ols-content', npsData, 'NPS');
        } catch (err) {
            window.olsAnalysis.showError('nps-ols-content', err.message);
        }
    });
});

// Export functionality
function exportData(format) {
    const filters = {
        domain_category: document.getElementById('domainCategoryDropdown').value,
        data_id: document.getElementById('dataIdDropdown').value,
        sentiment: document.getElementById('sentimentDropdown').value,
        start_date: document.getElementById('startDateFilter').value,
        end_date: document.getElementById('endDateFilter').value,
        partner: document.getElementById('productTypeDropdown').value,
        lob: document.getElementById('channelTypeDropdown').value,
        dummy_1: document.getElementById('teamDropdown').value,
        dummy_5: document.getElementById('resolutionStatusDropdown').value
    };
    
    showExportProgress(true, format);
    
    const params = new URLSearchParams();
    params.append('action', 'export');
    params.append('format', format);
    
    Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key] !== 'all') {
            params.append(key, filters[key]);
        }
    });
    
    window.location.href = `export.php?${params.toString()}`;
    
    setTimeout(() => showExportProgress(false, format), 3000);
}

function showExportProgress(show, format) {
    const statusDiv = document.getElementById('exportStatus');
    const buttons = ['exportCSV', 'exportExcel'];
    
    if (show) {
        statusDiv.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>Generating ${format.toUpperCase()} export...`;
        buttons.forEach(id => {
            document.getElementById(id).disabled = true;
        });
    } else {
        statusDiv.innerHTML = 'Export will include all data based on your current filter selections';
        buttons.forEach(id => {
            document.getElementById(id).disabled = false;
        });
    }
}

// Tabular Summary Functionality
document.addEventListener('DOMContentLoaded', function() {
    const generateTabularSummaryBtn = document.getElementById('generateTabularSummaryBtn');
    const generateAISummaryBtn = document.getElementById('generateAISummaryBtn');
    const tabularSummaryContainer = document.getElementById('tabularSummaryContainer');
    const aiSummaryContainer = document.getElementById('aiSummaryContainer');
    const tabularLoadingState = document.getElementById('tabularLoadingState');
    const aiLoadingState = document.getElementById('aiLoadingState');
    const tabularSummaryTableBody = document.getElementById('tabularSummaryTableBody');
    const aiSummaryContent = document.getElementById('aiSummaryContent');

    // Store the original tabular data for AI summary generation
    let currentTabularData = [];

    // Generate Tabular Summary
    generateTabularSummaryBtn.addEventListener('click', async function() {
        try {
            // Show loading state
            tabularLoadingState.classList.remove('hidden');
            tabularSummaryContainer.classList.add('hidden');
            aiSummaryContainer.classList.add('hidden');
            generateTabularSummaryBtn.disabled = true;

            // Collect current filter values (correct mapping)
            const filters = {
                domain_category: document.getElementById('domainCategoryDropdown').value !== 'all' ? document.getElementById('domainCategoryDropdown').value : '',
                data_id: document.getElementById('dataIdDropdown').value !== 'all' ? document.getElementById('dataIdDropdown').value : '',
                sentiment: document.getElementById('sentimentDropdown').value !== 'all' ? document.getElementById('sentimentDropdown').value : '',
                start_date: document.getElementById('startDateFilter').value,
                end_date: document.getElementById('endDateFilter').value,
                partner: document.getElementById('productTypeDropdown').value !== 'all' ? document.getElementById('productTypeDropdown').value : '', // Product Type
                lob: document.getElementById('channelTypeDropdown').value !== 'all' ? document.getElementById('channelTypeDropdown').value : '', // Channel Type
                dummy_1: document.getElementById('teamDropdown').value !== 'all' ? document.getElementById('teamDropdown').value : '', // Teams
                dummy_5: document.getElementById('resolutionStatusDropdown').value !== 'all' ? document.getElementById('resolutionStatusDropdown').value : '' // Resolution Status
            };

            // Make AJAX request to get tabular summary
            const response = await fetch('data_tabular_summary.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(filters)
            });

            const result = await response.json();

            if (result.success) {
                // Render the tabular summary
                renderTabularSummary(result.data);
                
                // Show the table and AI summary button
                tabularSummaryContainer.classList.remove('hidden');
                generateAISummaryBtn.classList.remove('hidden');
                
                console.log('Tabular summary generated successfully:', result.data);
            } else {
                throw new Error(result.error || 'Failed to generate tabular summary');
            }
        } catch (error) {
            console.error('Error generating tabular summary:', error);
            alert('Error generating tabular summary: ' + error.message);
        } finally {
            // Hide loading state and re-enable button
            tabularLoadingState.classList.add('hidden');
            generateTabularSummaryBtn.disabled = false;
        }
    });

    // Generate AI Summary
    generateAISummaryBtn.addEventListener('click', async function() {
        try {
            // Show loading state
            aiLoadingState.classList.remove('hidden');
            generateAISummaryBtn.disabled = true;

            // Get the current tabular data
            const tableData = getCurrentTableData();
            
            if (!tableData || tableData.length === 0) {
                throw new Error('No tabular data available for AI summary');
            }

            // Generate AI summary (simulated for now)
            const aiSummary = await generateAISummary(tableData);
            
            // Display the AI summary
            aiSummaryContent.innerHTML = aiSummary;
            aiSummaryContainer.classList.remove('hidden');
            
            console.log('AI summary generated successfully');
        } catch (error) {
            console.error('Error generating AI summary:', error);
            alert('Error generating AI summary: ' + error.message);
        } finally {
            // Hide loading state and re-enable button
            aiLoadingState.classList.add('hidden');
            generateAISummaryBtn.disabled = false;
        }
    });

    // Function to render tabular summary
    function renderTabularSummary(data) {
        tabularSummaryTableBody.innerHTML = '';

        // Handle new enhanced summary structure
        let summaryData = data;
        if (data && data.detailed_summary) {
            summaryData = data.detailed_summary;
            // Store the complete enhanced data for AI summary generation
            currentTabularData = data;
        } else {
            // Store the data for AI summary generation (backward compatibility)
            currentTabularData = data || [];
        }

        if (!summaryData || summaryData.length === 0) {
            tabularSummaryTableBody.innerHTML = '<tr><td colspan="18" class="text-center py-4 text-gray-500">No data available for the selected filters</td></tr>';
            return;
        }

        summaryData.forEach(row => {
            const tr = document.createElement('tr');
            tr.className = 'hover:bg-gray-50 dark:hover:bg-gray-700';
            
            tr.innerHTML = `
                <td class="px-3 py-2 border-b text-gray-800 dark:text-white">${row.main_driver || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-gray-800 dark:text-white">${row.sub_driver || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center">
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${
                        row.sentiment === 'Positive' ? 'bg-green-100 text-green-800' :
                        row.sentiment === 'Negative' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                    }">${row.sentiment || 'N/A'}</span>
                </td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.domain || row.domain_category || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.lob || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.vendor || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.location || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.partner || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.team || row.dummy_1 || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.resolution_status || row.dummy_5 || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.painpointscustomerfrustrations || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.detailedexplanationofthecomment || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.suggestionsforimprovement || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.verbitm || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.domain_category || 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white font-medium">${row.total_comments || 0}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.avg_csat ? parseFloat(row.avg_csat).toFixed(1) : 'N/A'}</td>
                <td class="px-3 py-2 border-b text-center text-gray-800 dark:text-white">${row.avg_nps ? parseFloat(row.avg_nps).toFixed(1) : 'N/A'}</td>
            `;
            
            tabularSummaryTableBody.appendChild(tr);
        });
    }

    // Function to get current table data
    function getCurrentTableData() {
        return currentTabularData;
    }

    // Function to generate AI summary using enhanced logic
    async function generateAISummary(enhancedData) {
        // Handle both old and new data structures
        let tableData, overallStats, topIssues, vendorPerformance;

        if (enhancedData && enhancedData.detailed_summary) {
            // New enhanced structure
            tableData = enhancedData.detailed_summary;
            overallStats = enhancedData.overall_statistics;
            topIssues = enhancedData.top_issues;
            vendorPerformance = enhancedData.vendor_performance;
        } else {
            // Backward compatibility with old structure
            tableData = enhancedData || [];
            overallStats = null;
            topIssues = null;
            vendorPerformance = null;
        }

        if (!Array.isArray(tableData) || tableData.length === 0) {
            return '<p>No data available for summary.</p>';
        }

        const totalComments = overallStats ? overallStats.total_records :
                             tableData.reduce((sum, row) => sum + (row.total_comments || 0), 0);

        if (totalComments === 0) {
            return '<p>No feedback data to analyze.</p>';
        }

        const avgCSAT = overallStats ? overallStats.overall_avg_csat :
                       tableData.reduce((sum, row) => sum + ((row.avg_csat || 0) * (row.total_comments || 0)), 0) / totalComments;
        const avgNPS = overallStats ? overallStats.overall_avg_nps :
                      tableData.reduce((sum, row) => sum + ((row.avg_nps || 0) * (row.total_comments || 0)), 0) / totalComments;

        // Use the enhanced prompt builder that works with summary data
        let prompt = buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS);

        // No truncation - the whole point is to use summary data instead of raw data

        // Call backend to get AI summary
        try {
            const response = await fetch('ai_summary.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt })
            });
            const data = await response.json();

            if (data.status === 'success') {
                return data.summary_html;
            } else {
                return `<p>Error: ${data.message}</p>`;
            }
        } catch (err) {
            return `<p>Error: ${err.message}</p>`;
        }
    }



    function generateAdvancedAnalytics(tableData, totalComments, avgCSAT, avgNPS) {
        const analytics = {
            vendors: {},
            painPoints: {},
            domains: {},
            resolutions: {}
        };

        tableData.forEach((row, index) => {

            const vendor = row.vendor || 'Unknown';
            const pain = row.painpointscustomerfrustrations || 'Unknown';
            const domain = row.domain_category || 'Unknown';
            const resolution = row.resolution_status || 'Unknown';

            // Vendor analytics
            if (!analytics.vendors[vendor]) analytics.vendors[vendor] = { comments: 0, csatSum: 0, delta: 0 };
            analytics.vendors[vendor].comments += row.total_comments || 0;
            analytics.vendors[vendor].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

            // Pain points analytics
            if (!analytics.painPoints[pain]) analytics.painPoints[pain] = { freq: 0, csatSum: 0, severity: 0 };
            analytics.painPoints[pain].freq += row.total_comments || 0;
            analytics.painPoints[pain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);

            // Domain analytics
            if (!analytics.domains[domain]) analytics.domains[domain] = { comments: 0, csatSum: 0, resolved: 0, total: 0 };
            analytics.domains[domain].comments += row.total_comments || 0;
            analytics.domains[domain].csatSum += (row.avg_csat || 0) * (row.total_comments || 0);
            analytics.domains[domain].total += row.total_comments || 0;
            if ((row.resolution_status || '').toLowerCase() === 'resolved') {
                analytics.domains[domain].resolved += row.total_comments || 0;
            }

            // Resolution analytics
            if (!analytics.resolutions[resolution]) analytics.resolutions[resolution] = { comments: 0 };
            analytics.resolutions[resolution].comments += row.total_comments || 0;
        });

        // Calculate vendor deltas
        for (let v in analytics.vendors) {
            const avg = analytics.vendors[v].csatSum / analytics.vendors[v].comments;
            analytics.vendors[v].delta = parseFloat((avg - avgCSAT).toFixed(2));
        }

        // Calculate pain point severity
        for (let p in analytics.painPoints) {
            const data = analytics.painPoints[p];
            const avg = data.csatSum / data.freq;
            data.severity = parseFloat((data.freq * (5 - avg)).toFixed(2));
        }

        return analytics;
    }

    function buildRecordInfo(tableData, totalComments, avgCSAT, avgNPS) {
        const vendors = new Set(tableData.map(r => r.vendor || 'Unknown')).size;
        const locations = new Set(tableData.map(r => r.location || 'Unknown')).size;
        return {
            totalComments,
            avgCSAT: avgCSAT.toFixed(1),
            avgNPS: avgNPS.toFixed(1),
            vendors,
            locations
        };
    }


});

// New enhanced prompt builder using summary data
function buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS) {
    // Build comprehensive data sections for AI analysis
    const overallSection = overallStats ? `
OVERALL STATISTICS:
- Total Records: ${overallStats.total_records}
- Average CSAT: ${overallStats.overall_avg_csat} (Scale: 1-5)
- Average NPS: ${overallStats.overall_avg_nps} (Scale: 1-5)
- Unique Drivers: ${overallStats.unique_drivers}
- Unique Vendors: ${overallStats.unique_vendors}
- Unique Locations: ${overallStats.unique_locations}
- Sentiment Distribution: ${overallStats.total_positive} Positive (${overallStats.positive_percentage}%), ${overallStats.total_negative} Negative (${overallStats.negative_percentage}%), ${overallStats.total_neutral} Neutral
` : '';

    const topIssuesSection = topIssues && topIssues.length > 0 ? `
TOP ISSUES BY FREQUENCY:
${topIssues.map(issue => `- ${issue.main_driver} > ${issue.sub_driver}: ${issue.frequency} mentions, CSAT ${issue.avg_csat}, NPS ${issue.avg_nps}, ${issue.negative_count} negative`).join('\n')}
` : '';

    const vendorSection = vendorPerformance && vendorPerformance.length > 0 ? `
VENDOR PERFORMANCE ANALYSIS:
${vendorPerformance.map(vendor => `- ${vendor.vendor}: ${vendor.total_comments} comments, CSAT ${vendor.avg_csat}, NPS ${vendor.avg_nps}, ${vendor.negative_feedback} negative (${vendor.negative_percentage}%)`).join('\n')}
` : '';

    // Build detailed summary section (limit to top 20 for prompt efficiency)
    const detailedSection = tableData && tableData.length > 0 ? `
DETAILED BREAKDOWN BY CATEGORY:
${tableData.slice(0, 20).map(row => `- ${row.main_driver}/${row.sub_driver} (${row.sentiment}): ${row.total_comments} comments, CSAT ${row.csat_score || row.avg_csat}, NPS ${row.nps_score || row.avg_nps}, Vendor: ${row.vendor}, Location: ${row.location}`).join('\n')}
` : '';

    return `You are an expert customer experience analyst. Analyze this comprehensive feedback dataset and provide a detailed executive summary.

${overallSection}
${topIssuesSection}
${vendorSection}
${detailedSection}

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections with actual calculated data
4. Use the provided data structure
5. Maintain professional tone throughout
6. Calculate actual numbers from the data provided above

PART 1: EXECUTIVE SUMMARY
Generate exactly this structure with actual data:

<h3><strong>📊 Executive Summary</strong></h3>
<p><strong>Dataset Overview:</strong></p>
<p>Analyzed customer feedback across with an average CSAT of <strong>${avgCSAT.toFixed(1)}</strong> and NPS of <strong>${avgNPS.toFixed(1)}</strong>. Overall customer satisfaction appears to be [analyze based on scores], with a significant number of comments indicating [analyze sentiment patterns].</p>

<h4><strong>🔍 Top Pain Points:</strong></h4>
<ul>
[List top 5 pain points from the data with actual frequencies and impact]
</ul>

<h4><strong>🎯 Unresolved Agent Lack of empathy, Delayed response, Inadequate resolution:</strong></h4>
<ul>
[Analyze agent-related issues from the data]
</ul>

<h4><strong>⚡ Poor interaction with the agent:</strong></h4>
<ul>
[Analyze interaction quality issues]
</ul>

<h4><strong>🏆 Best Performing Vendor:</strong></h4>
<ul>
[Identify best performing vendor with actual CSAT/NPS scores]
</ul>

<h4><strong>📈 Needs Improvement:</strong></h4>
<ul>
[Identify areas needing improvement with specific metrics]
</ul>

<h4><strong>🎖️ Actionable Recommendations:</strong></h4>
<ul>
[Provide specific recommendations based on the data analysis]
</ul>

<h4><strong>🔄 Priority:</strong></h4>
<ul>
[List priority actions based on frequency and impact]
</ul>

<h4><strong>📊 Impact Patterns:</strong></h4>
<ul>
[Analyze patterns in the data]
</ul>

<h4><strong>🎯 Trend Watch:</strong></h4>
<ul>
[Identify trends from the data]
</ul>

PART 2: DIAGNOSTIC REPORT (Data-Driven ACPT Breakdown)

<h3><strong>📋 Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
[Provide actual summary statistics from the data]
</ul>

<h4><strong>🔍 Top Issues or Praises</strong></h4>
<ul>
[List actual top issues from the frequency data]
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
[Calculate actual counts from the data for Agent, Customer, Process, Technology categories]
</table>

<h4><strong>🔍 ACPT Sub-driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT</th><th>L1 Category</th><th>Sub-driver</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
[Populate with actual data from the detailed breakdown]
</table>

<h4><strong>📊 Data Validation</strong></h4>
<p>Sum of ACPT Table = [Calculate actual sum] [Verify and confirm]</p>
<p>Sum of Sub-driver Table = [Calculate actual sum] [Verify and confirm]</p>
<p>% feedback categories = [Calculate actual percentages]</p>

<h4><strong>🔍 Insight Analysis</strong></h4>
<p>[Provide insights based on actual data patterns, correlations between CSAT/NPS and issues, vendor performance analysis]</p>

<h4><strong>📊 Root Cause Hypothesis</strong></h4>
<p>[Analyze root causes based on the frequency and severity of issues in the data]</p>

<h4><strong>🎯 Trend Watch</strong></h4>
<p>[Identify trends from the actual data provided]</p>

Remember: Use ONLY the data provided above. Calculate actual numbers, don't use placeholders. Ensure all tables contain real data from the analysis.`;
}

function buildEnhancedPrompt(analytics, info, avgCSAT, avgNPS) {
    // Build vendor performance data (limit to top 10)
    const vendorData = Object.entries(analytics.vendors)
        .map(([vendor, data]) => ({
            vendor,
            comments: data.comments,
            avgCsat: (data.csatSum / data.comments).toFixed(1),
            delta: data.delta
        }))
        .sort((a, b) => b.delta - a.delta)
        .slice(0, 10);

    // Build pain points data (limit to top 10)
    const painPointsData = Object.entries(analytics.painPoints)
        .map(([pain, data]) => ({
            pain,
            frequency: data.freq,
            avgCsat: (data.csatSum / data.freq).toFixed(1),
            severity: data.severity
        }))
        .sort((a, b) => b.severity - a.severity)
        .slice(0, 10);

    // Build domains data (limit to top 10)
    const domainsData = Object.entries(analytics.domains)
        .map(([domain, data]) => ({
            domain,
            comments: data.comments,
            avgCsat: (data.csatSum / data.comments).toFixed(1),
            resolutionRate: ((data.resolved / data.total) * 100).toFixed(1)
        }))
        .sort((a, b) => parseFloat(b.avgCsat) - parseFloat(a.avgCsat))
        .slice(0, 10);

    return `You are an expert AI feedback analyst. Generate a comprehensive analysis in TWO distinct parts with exact HTML formatting. Follow the structure EXACTLY as specified below.

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections
4. Use the provided data structure
5. Maintain professional tone throughout

DATASET OVERVIEW:
- Total Comments: ${info.totalComments}
- Average CSAT: ${info.avgCSAT} (Scale: 1-5)
- Average NPS: ${info.avgNPS} (Scale: 1-5)
- Vendors Analyzed: ${info.vendors}
- Locations Covered: ${info.locations}

VENDOR PERFORMANCE DATA:
${vendorData.map(v => `- ${v.vendor}: ${v.comments} comments, CSAT ${v.avgCsat}, Delta ${v.delta}`).join('\n')}

PAIN POINTS DATA:
${painPointsData.map(p => `- ${p.pain}: ${p.frequency} mentions, CSAT ${p.avgCsat}, Severity ${p.severity}`).join('\n')}

DOMAINS DATA:
${domainsData.map(d => `- ${d.domain}: ${d.comments} comments, CSAT ${d.avgCsat}, Resolution Rate ${d.resolutionRate}%`).join('\n')}

---

<h3><strong>Part 1: Executive Summary</strong></h3>

<h4><strong>Executive Overview</strong></h4>
<p>Analyzed ${info.totalComments} customer feedback entries with an average CSAT of ${info.avgCSAT} ${parseFloat(info.avgCSAT) >= 4 ? '<span style="color:green;">▲</span>' : '<span style="color:red;">▼</span>'} and NPS of ${info.avgNPS}. [Provide 2-3 sentences of high-level insights about overall customer satisfaction trends]</p>

<h4><strong>Top Pain Points</strong></h4>
<ul>
<li><strong>${painPointsData[0]?.pain || 'Data Processing'}:</strong> ${painPointsData[0]?.frequency || 0} mentions (Severity: ${painPointsData[0]?.severity || 0})</li>
<li><strong>${painPointsData[1]?.pain || 'Service Quality'}:</strong> ${painPointsData[1]?.frequency || 0} mentions (Severity: ${painPointsData[1]?.severity || 0})</li>
<li><strong>${painPointsData[2]?.pain || 'Response Time'}:</strong> ${painPointsData[2]?.frequency || 0} mentions (Severity: ${painPointsData[2]?.severity || 0})</li>
</ul>

<h4><strong>Performance Insights</strong></h4>
<p><strong>Best Performing Vendor:</strong> ${vendorData[0]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[0]?.delta || 0} <span style="color:green;">▲</span>)</p>
<p><strong>Needs Improvement:</strong> ${vendorData[vendorData.length - 1]?.vendor || 'N/A'} (CSAT Δ: ${vendorData[vendorData.length - 1]?.delta || 0} <span style="color:red;">▼</span>)</p>

<h4><strong>Actionable Recommendations</strong></h4>
<ul>
<li><strong>Priority 1:</strong> Address "${painPointsData[0]?.pain || 'top pain point'}" immediately - highest severity score</li>
<li><strong>Priority 2:</strong> Investigate underperforming vendor: ${vendorData[vendorData.length - 1]?.vendor || 'N/A'}</li>
<li><strong>Priority 3:</strong> Implement feedback loop for domains with CSAT below 3.0</li>
</ul>

<h4><strong>Success Patterns</strong></h4>
<p>Vendor "${vendorData[0]?.vendor || 'N/A'}" demonstrates superior performance with ${vendorData[0]?.delta || 0} points above average CSAT. [Analyze what makes this vendor successful]</p>

<h4><strong>Root Cause Hypothesis</strong></h4>
<p>[Provide 2-3 sentences hypothesizing the underlying causes of the top pain points based on the data patterns]</p>

<h4><strong>Trend Watch</strong></h4>
<p>[Highlight 2-3 emerging patterns or risks based on the data distribution and performance metrics]</p>

---

<h3><strong>Part 2: Diagnostic Report (Data-Driven ACPT Breakdown)</strong></h3>

<h4><strong>📊 Dataset Summary</strong></h4>
<ul>
<li>Total Comments: ${info.totalComments}</li>
<li>Average CSAT: ${info.avgCSAT}</li>
<li>Average NPS: ${info.avgNPS}</li>
<li>Vendors: ${info.vendors}</li>
<li>Locations: ${info.locations}</li>
</ul>

<h4><strong>🔥 Top Issues or Praises</strong></h4>
<p>Most frequently mentioned themes:</p>
<ul>
<li>${painPointsData[0]?.pain || 'Service Issues'} (${painPointsData[0]?.frequency || 0} mentions)</li>
<li>${painPointsData[1]?.pain || 'Process Issues'} (${painPointsData[1]?.frequency || 0} mentions)</li>
<li>${painPointsData[2]?.pain || 'Product Issues'} (${painPointsData[2]?.frequency || 0} mentions)</li>
</ul>

<h4><strong>📋 ACPT Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT Category</th><th>Issue Description</th><th>Count</th><th>Sample Comment</th></tr>
<tr><td>Agent</td><td>Service delays, communication gaps</td><td>[Calculate from data]</td><td>"No callback after escalation"</td></tr>
<tr><td>Customer</td><td>Account issues, verification problems</td><td>[Calculate from data]</td><td>"Couldn't verify with OTP"</td></tr>
<tr><td>Process</td><td>Workflow inefficiencies, delays</td><td>[Calculate from data]</td><td>"Refund process too slow"</td></tr>
<tr><td>Technology</td><td>System errors, technical failures</td><td>[Calculate from data]</td><td>"App keeps crashing"</td></tr>
</table>

<h4><strong>🔍 ACPT Sub-driver & Sentiment Table</strong></h4>
<table border="1" cellpadding="6" style="border-collapse: collapse; width: 100%;">
<tr><th>ACPT</th><th>L1 Category</th><th>Sub-driver</th><th>Sentiment</th><th>Count</th><th>Sample Comment</th></tr>
<tr><td>Agent</td><td>Response Time</td><td>Delayed Follow-up</td><td>Negative</td><td>[Calculate]</td><td>"Waiting 3 days for response"</td></tr>
<tr><td>Process</td><td>Refund Process</td><td>Resolution Gaps</td><td>Negative</td><td>[Calculate]</td><td>"Still waiting for refund"</td></tr>
<tr><td>Technology</td><td>App Performance</td><td>System Stability</td><td>Negative</td><td>[Calculate]</td><td>"App crashes frequently"</td></tr>
</table>

<h4><strong>✅ Data Verification</strong></h4>
<ul>
<li>Sum of ACPT Table = ${info.totalComments}? [Verify and confirm]</li>
<li>Sum of Sub-driver Table = ${info.totalComments}? [Verify and confirm]</li>
<li>All feedback categorized? [Confirm 100% coverage]</li>
</ul>

<h4><strong>🎯 Insight Anchors</strong></h4>
<p><strong>Counterintuitive Findings:</strong></p>
<ul>
<li>[Identify unexpected pattern from vendor performance data]</li>
<li>[Highlight surprising correlation between pain points and CSAT]</li>
<li>[Point out domain-specific trends that contradict expectations]</li>
</ul>

REMEMBER: Output BOTH Part 1 AND Part 2 completely. Use provided data to fill in specific numbers where placeholders exist. Maintain HTML formatting exactly as shown.`;
}
</script>
</body>
<footer class="bg-yellow-300 py-2 shadow-md mt-auto">
    <div class="container mx-auto px-9 flex justify-between items-center">
        <div>
            <p class="text-gray-700 text-xs"><b>&#169 <?php echo date("Y"); ?> Bill Gosling Outsourcing. All rights reserved.</b></p>
			<p class="text-gray-700 text-xs">This system is for the use of authorized personnel only and by accessing this system you hereby consent to the system being monitored by the Company. Any unauthorized use will be considered a breach of the Company's Information Security policies and may also be unlawful under law. Bill Gosling Outsourcing reserves the right to take any action including disciplinary action or legal proceedings in a court of law against persons involved in the violation of the access restrictions herein. The Information Is 'Internal, Restricted'.</p>
        </div>
    </div>
</footer>
</html>