<?php
// test/test_ols_api.php

$cookieFile = __DIR__ . '/cookie.txt';  // Path to store session cookies
$username = '<EMAIL>';
$password = 'test';

// Test parameters
$test_params = [
    'user_id' => 2,
    'data_id' => '686cf72157df2',
];

// Detect if running on AWS
$is_aws = getenv('AWS_ENVIRONMENT') || getenv('EC2_INSTANCE_ID') || strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'Amazon') !== false;

if ($is_aws) {
    // Use AWS-specific settings
    $python_commands = ['python3', 'python'];
    $path_command = 'which';
    $use_env_direct = true;
} else {
    // Use local settings
    $python_commands = ['python', 'python3'];
    $path_command = 'where';
    $use_env_direct = false;
}

// Start test
if (login($username, $password, $cookieFile)) {
    call_api('csat', $test_params, $cookieFile);
    call_api('nps', $test_params, $cookieFile);
} else {
    echo "\n❌ Login failed. Aborting tests.\n";
}

// --- FUNCTIONS ---

function login($username, $password, $cookieFile) {
    $login_url = 'http://localhost/feedback-10/index.php';
    $post_fields = http_build_query([
        'username' => $username,
        'password' => $password,
        'action'   => 'login'
    ]);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    curl_setopt($ch, CURLOPT_HEADER, false);

    $response = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "<pre>\n--- LOGIN ---\nURL: $login_url\nHTTP Status: $httpcode\n</pre>";

    return ($httpcode === 200 || $httpcode === 302);
}

function call_api($action, $params, $cookieFile) {
    $base_url = 'http://localhost/feedback-10/python/api/ols_api.php';
    $params['action'] = $action;
    $url = $base_url . '?' . http_build_query($params);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    curl_setopt($ch, CURLOPT_HEADER, false);

    $output = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "<pre>\n--- API CALL [$action] ---\nURL: $url\nHTTP Status: $httpcode\nResponse:\n$output\n</pre>";
}
?>
