openapi: 3.0.0
info:
  title: Product import API
  version: '1.0'
servers:
  - url: 'http://localhost:8000'
paths:
  /products.create:
    post:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                test:
                  $ref: "#/components/schemas/TestObject"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                properties:
                  result:
                    type: string
components:
  schemas:
    TestObject:
      type: object
      required:
        - input
      properties:
        input:
          type: string
          example: 5201f887-e28a-45df-ad93-bde1021bae11