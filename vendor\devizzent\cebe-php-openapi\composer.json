{"name": "devizzent/cebe-php-openapi", "description": "Read and write OpenAPI yaml/json files and make the content accessable in PHP objects.", "keywords": ["openapi"], "homepage": "https://github.com/DEVizzent/cebe-php-openapi#readme", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://cebe.cc/", "role": "Creator"}, {"name": "Vicent <PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/DEVizzent/cebe-php-openapi/issues", "source": "https://github.com/DEVizzent/cebe-php-openapi"}, "require": {"php": ">=7.1.0", "ext-json": "*", "symfony/yaml": "^3.4 || ^4 || ^5 || ^6 || ^7", "justinrainbow/json-schema": "^5.2 || ^6.0"}, "require-dev": {"cebe/indent": "*", "phpunit/phpunit": "^6.5 || ^7.5 || ^8.5 || ^9.4 || ^11.4", "oai/openapi-specification-3.0": "3.0.3", "oai/openapi-specification-3.1": "3.1.0", "mermade/openapi3-examples": "1.0.0", "apis-guru/openapi-directory": "1.0.0", "phpstan/phpstan": "^0.12.0"}, "conflict": {"symfony/yaml": "3.4.0 - 3.4.4 || 4.0.0 - 4.4.17 || 5.0.0 - 5.1.9 || 5.2.0"}, "replace": {"cebe/php-openapi": "1.7.0"}, "autoload": {"psr-4": {"cebe\\openapi\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "bin": ["bin/php-openapi"], "repositories": [{"type": "package", "package": {"name": "oai/openapi-specification-3.0", "version": "3.0.3", "source": {"url": "https://github.com/OAI/OpenAPI-Specification", "type": "git", "reference": "3.0.3"}}}, {"type": "package", "package": {"name": "oai/openapi-specification-3.1", "version": "3.1.0", "source": {"url": "https://github.com/OAI/OpenAPI-Specification", "type": "git", "reference": "v3.1.1-dev"}}}, {"type": "package", "package": {"name": "mermade/openapi3-examples", "version": "1.0.0", "source": {"url": "https://github.com/Mermade/openapi3-examples", "type": "git", "reference": "9c2997e1a25919a8182080cc43a4db06d2dc775d"}}}, {"type": "package", "package": {"name": "apis-guru/openapi-directory", "version": "1.0.0", "dist": {"url": "https://github.com/APIs-guru/openapi-directory/archive/refs/heads/openapi3.0.0.zip", "type": "zip"}}}]}