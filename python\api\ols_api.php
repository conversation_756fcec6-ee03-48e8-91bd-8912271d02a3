<?php
session_start();

// Load environment variables using the same system as the rest of your app
require_once __DIR__ . '/../../config.php';

// Ensure environment variables are available for Python
if (!getenv('DB_HOST_ALT') && isset($_ENV['DB_HOST_ALT'])) {
    putenv('DB_HOST_ALT=' . $_ENV['DB_HOST_ALT']);
}
if (!getenv('DB_NAME_ALT') && isset($_ENV['DB_NAME_ALT'])) {
    putenv('DB_NAME_ALT=' . $_ENV['DB_NAME_ALT']);
}
if (!getenv('DB_USER_ALT') && isset($_ENV['DB_USER_ALT'])) {
    putenv('DB_USER_ALT=' . $_ENV['DB_USER_ALT']);
}
if (!getenv('DB_PASS_ALT') && isset($_ENV['DB_PASS_ALT'])) {
    putenv('DB_PASS_ALT=' . $_ENV['DB_PASS_ALT']);
}
if (!getenv('OPENAI_API_KEY') && isset($_ENV['OPENAI_API_KEY'])) {
    putenv('OPENAI_API_KEY=' . $_ENV['OPENAI_API_KEY']);
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../python_wrapper.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Session expired or invalid']);
    exit();
}

$analyzer = new PythonAnalysis();
$action = $_GET['action'] ?? '';
$user_id = $_SESSION['user_id'];

try {
    switch ($action) {
        case 'csat':
            $params = $_GET;
            $darkMode = isset($params['dark_mode']) && $params['dark_mode'] === 'true';
            $result = $analyzer->runAnalysis($params, 'csat', $user_id, $darkMode);
            echo json_encode($result);
            break;
            
        case 'nps':
            $params = $_GET;
            $darkMode = isset($params['dark_mode']) && $params['dark_mode'] === 'true';
            $result = $analyzer->runAnalysis($params, 'nps', $user_id, $darkMode);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("OLS API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}
?>