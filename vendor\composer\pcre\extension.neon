# composer/pcre PHPStan extensions
#
# These can be reused by third party packages by including 'vendor/composer/pcre/extension.neon'
# in your phpstan config

services:
    -
        class: Composer\Pcre\PHPStan\PregMatchParameterOutTypeExtension
        tags:
            - phpstan.staticMethodParameterOutTypeExtension
    -
        class: Composer\Pcre\PHPStan\PregMatchTypeSpecifyingExtension
        tags:
            - phpstan.typeSpecifier.staticMethodTypeSpecifyingExtension
    -
        class: Composer\Pcre\PHPStan\PregReplaceCallbackClosureTypeExtension
        tags:
            - phpstan.staticMethodParameterClosureTypeExtension

rules:
    - Composer\Pcre\PHPStan\UnsafeStrictGroupsCallRule
    - Composer\Pcre\PHPStan\InvalidRegexPatternRule
