<?php
require_once 'DatabaseInteraction.php';
require_once 'vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$db = new DatabaseInteraction();
$conn = $db->connect();

$batch_size = 100;
$rate_limit_per_minute = 30;

while (true) {
    $comments_to_process = $db->getQueuedComments($batch_size);

    if (empty($comments_to_process)) {
        sleep(60);
        continue;
    }

    $processed_count = 0;
    $start_time = time();

    foreach ($comments_to_process as $comment) {
        if ($processed_count >= $rate_limit_per_minute) {
            sleep(60 - (time() - $start_time));
            $processed_count = 0;
            $start_time = time();
        }

        if ($db->isCommentAnalyzed($comment['comment'], $comment['data_id'], $comment['pid'])) {
            $db->dequeueComment($comment['id']);
            continue;
        }

        // Get domain category directly from the comment_queue table
        $domain_category = !empty($comment['domain_category']) ? $comment['domain_category'] : 'Collections';

        // If domain category is not set, use Collections as default
        if (empty($domain_category)) {
            $domain_category = 'Collections';
        }

        // Get user-selected main drivers for this data_id
        $selected_main_drivers = $db->getDataMainDrivers($comment['data_id']);

        error_log("Using Domain Category: $domain_category");
        error_log("Selected Main Drivers: " . implode(", ", $selected_main_drivers));

        $analysis_result = analyze_comment($comment['comment'], $domain_category, $selected_main_drivers);
        if ($analysis_result === 'Analysis failed') {
            error_log("Analysis failed for comment: " . $comment['comment']);
            continue;
        }

        $parsed_result = parse_analysis_result($analysis_result);

        // Log if important fields are empty
        if (empty($parsed_result['sentiment']) || empty($parsed_result['painpointscustomerfrustrations'])) {
            error_log("Potential parsing issue. Raw response:\n$analysis_result");
        }

        error_log("Parsed Result: " . print_r($parsed_result, true));

        try {
            // Extract all fields from the comment_queue table using null coalescing operator
            $resolution_comment = $comment['resolution_comment'] ?? null;
            $internal_scores = $comment['internal_scores'] ?? null;
            $feedback_submit_date = $comment['feedback_submit_date'] ?? null;
            $feedback_month = $comment['feedback_month'] ?? null;
            $feedback_time = $comment['feedback_time'] ?? null;
            $lob = $comment['lob'] ?? null;
            $vendor = $comment['vendor'] ?? null;
            $location = $comment['location'] ?? null;
            $partner = $comment['partner'] ?? null;
            $dummy_1 = $comment['dummy_1'] ?? null;
            $dummy_2 = $comment['dummy_2'] ?? null;
            $dummy_3 = $comment['dummy_3'] ?? null;
            $dummy_4 = $comment['dummy_4'] ?? null;
            $dummy_5 = $comment['dummy_5'] ?? null;

            // Insert into analyzed_comments with all fields
            $query = "INSERT INTO analyzed_comments (
                comment, main_driver, sub_driver, sentiment, user_id, data_id, csat, nps, pid,
                painpointscustomerfrustrations, detailedexplanationofthecomment, suggestionsforimprovement, verbitm, domain_category,
                resolution_comment, internal_scores, feedback_submit_date, feedback_month, feedback_time,
                lob, vendor, location, partner, dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
            ) VALUES (
                :comment, :main_driver, :sub_driver, :sentiment, :user_id, :data_id, :csat, :nps, :pid,
                :painpointscustomerfrustrations, :detailedexplanationofthecomment, :suggestionsforimprovement, :verbitm, :domain_category,
                :resolution_comment, :internal_scores, :feedback_submit_date, :feedback_month, :feedback_time,
                :lob, :vendor, :location, :partner, :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
            )";

            $stmt = $conn->prepare($query);
            $stmt->bindParam(':comment', $comment['comment']);
            $stmt->bindParam(':main_driver', $parsed_result['main_driver']);
            $stmt->bindParam(':sub_driver', $parsed_result['sub_driver']);
            $stmt->bindParam(':sentiment', $parsed_result['sentiment']);
            $stmt->bindParam(':user_id', $comment['user_id']);
            $stmt->bindParam(':data_id', $comment['data_id']);
            $stmt->bindParam(':csat', $comment['csat']);
            $stmt->bindParam(':nps', $comment['nps']);
            $stmt->bindParam(':pid', $comment['pid']);
            $stmt->bindParam(':painpointscustomerfrustrations', $parsed_result['painpointscustomerfrustrations']);
            $stmt->bindParam(':detailedexplanationofthecomment', $parsed_result['detailedexplanationofthecomment']);
            $stmt->bindParam(':suggestionsforimprovement', $parsed_result['suggestionsforimprovement']);
            $stmt->bindParam(':verbitm', $parsed_result['verbitm']);
            $stmt->bindParam(':domain_category', $domain_category);
            $stmt->bindParam(':resolution_comment', $resolution_comment);
            $stmt->bindParam(':internal_scores', $internal_scores);
            $stmt->bindParam(':feedback_submit_date', $feedback_submit_date);
            $stmt->bindParam(':feedback_month', $feedback_month);
            $stmt->bindParam(':feedback_time', $feedback_time);
            $stmt->bindParam(':lob', $lob);
            $stmt->bindParam(':vendor', $vendor);
            $stmt->bindParam(':location', $location);
            $stmt->bindParam(':partner', $partner);
            $stmt->bindParam(':dummy_1', $dummy_1);
            $stmt->bindParam(':dummy_2', $dummy_2);
            $stmt->bindParam(':dummy_3', $dummy_3);
            $stmt->bindParam(':dummy_4', $dummy_4);
            $stmt->bindParam(':dummy_5', $dummy_5);

            $stmt->execute();

            $db->dequeueComment($comment['id']);
        } catch (PDOException $e) {
            error_log("DB Error: " . $e->getMessage());
        }

        $processed_count++;
    }

    sleep(1);
}



function analyze_comment($comment, $domain_category, $selected_main_drivers = []) {
    // Create a string of user-selected main drivers if available
    $main_drivers_str = '';
    if (!empty($selected_main_drivers)) {
        $main_drivers_str = "IMPORTANT: The user has selected the following main drivers for this dataset:\n";
        foreach ($selected_main_drivers as $index => $driver) {
            $main_drivers_str .= ($index + 1) . ". " . $driver . "\n";
        }
        $main_drivers_str .= "\nYou MUST ONLY use these user-selected main drivers in your analysis. Do not introduce any other main drivers.\n\n";
    }

    $custom_prompt = <<<EOT
Analyze the following customer feedback comment in the context of the $domain_category domain (user-selected domain):

$main_drivers_str
### Identify the following aspects:
- **Main Driver**: (IMPORTANT: You MUST select one of the user-provided main drivers listed above. If no main drivers were provided, then select from: 'Collections Domain': 'Billing & Payment Issues', 'Customer Service & Resolution Issues', 'Customer Support & Service', 'Transfer & Process Issues', 'Policy & Procedures', 'Tools & Technology', 'Other'.; 'Sales & Marketing Domain': 'Customer Support & Service', 'Customer Service & Resolution Issues', 'Perceived Company Integrity', 'Lack of Clear Communication', 'Marketing & Promotions', 'Others'.; 'Finance & Banking Domain': 'Verification & Account Issues', 'Account Access & Security', 'Billing & Payment Issues', 'Website or App Glitches', 'Refund Policies & Eligibility', 'Loan & Credit Issues', 'Policy & Procedures', 'Tools & Technology', 'Others'.; 'Retail & E-Commerce Domain': 'Customer Support & Service', 'Website or App Glitches', 'Refund Policies & Eligibility', 'Perceived Company Integrity', 'Customer Service & Resolution Issues', 'Shipping & Delivery Issues', 'Product Quality & Returns', 'Policy & Procedures', 'Tools & Technology', 'Others'. **Do not include any domain prefixes in your response.** Specify appropriate matching Single Main Driver)
- **Sub Driver**: (A more specific classification within the main driver, such as Payment Issues, Service Failures, Process Failures, System Errors, Technical Failures, Resolution Issue, Card Issue, Login Issue, etc. Specify an appropriate single Sub Driver. If none of the existing sub-drivers are relevant or applicable, classify the Sub Driver as “Other” instead of using “NA”.)
- **Sentiment**: (As an AI with expertise in language and emotion analysis, your task is to analyze the sentiment of the following text. Please consider the overall tone of the discussion as a third party employee or customer statement with consideration of suggestions that may not exist in the organization at present, the emotion conveyed by the language used, and the context in which words and phrases are used. Indicate whether the sentiment is generally positive, negative, or neutral, and Specify Single Sentiment like Positive, Negative, or Neutral. Do not use Mixed, consider them Neutral Sentiment)
- **Pain Points & Customer Frustrations**: (Extract key pain points, detailing what specific issues the customer faced and how it impacted their experience. Specify all with semi-colon ";" separator as applicable.)
- **Detailed Explanation of the Comment**: (Provide a structured breakdown explaining why the feedback falls under the identified category and sentiment in limited words.)
- **Suggestions for Improvement**: (Provide actionable recommendations that could resolve the customer's concern or improve the service experience. Specify all with semi-colon ";" separator as applicable.)
- **Verbitm**: (Provide verbatim on the comment with key phrases 3-4 words verbatim which helped identify the dissatisfaction. Specify all with semi-colon ";" separator as applicable. In case of no results reply as 'NA').

### Response Format:
**Main Driver**: [Driver]
**Sub Driver**: [Sub Driver]
**Sentiment**: [Sentiment]
**Pain Points & Customer Frustrations**: [List key pain points]
**Detailed Explanation of the Comment**: [Provide a structured breakdown of the customer's concern]
**Suggestions for Improvement**: [Provide clear, actionable suggestions to enhance customer experience]
**Verbitm**: [Provide clear, verbatim mentioned on the DSAT]
EOT;

    try {
        $client = OpenAI::client($_ENV['OPENAI_API_KEY']);
        $response = $client->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                ['role' => 'system', 'content' => "You analyze feedback for the user-selected $domain_category domain and provide structured insights. You MUST use the user-selected main drivers in your analysis and not introduce new ones."],
                ['role' => 'user', 'content' => "Analyze this:\n\n$comment\n\n$custom_prompt"]
            ],
            'max_tokens' => 4000,
            'temperature' => 0.2
        ]);

        return trim($response->choices[0]->message->content);
    } catch (\Exception $e) {
        error_log("Exception in analyze_comment: " . $e->getMessage());
        return 'Analysis failed';
    }
}

function parse_analysis_result($result) {
    $lines = explode("\n", $result);
    $fields = [
        'main_driver' => '**Main Driver**:',
        'sub_driver' => '**Sub Driver**:',
        'sentiment' => '**Sentiment**:',
        'pain_points' => '**Pain Points & Customer Frustrations**:',
        'detailed_explanation' => '**Detailed Explanation of the Comment**:',
        'suggestions' => '**Suggestions for Improvement**:',
        'verbatim' => '**Verbitm**:',
    ];

    $parsed = [];

    foreach ($fields as $key => $label) {
        $parsed[$key] = '';
        foreach ($lines as $line) {
            $line = trim($line);
            if (preg_match('/^-?\s*' . preg_quote($label, '/') . '\s*(.*)$/i', $line, $matches)) {
                $parsed[$key] = trim($matches[1]);
                break;
            }
        }
    }

    return [
        'main_driver' => $parsed['main_driver'],
        'sub_driver' => $parsed['sub_driver'],
        'sentiment' => $parsed['sentiment'],
        'painpointscustomerfrustrations' => $parsed['pain_points'],
        'detailedexplanationofthecomment' => $parsed['detailed_explanation'],
        'suggestionsforimprovement' => $parsed['suggestions'],
        'verbitm' => $parsed['verbatim']
    ];
}
