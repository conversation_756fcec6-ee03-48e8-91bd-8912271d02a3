{"name": "marc-mabe/php-enum", "description": "Simple and fast implementation of enumerations with native PHP", "type": "library", "keywords": ["enum", "enumeration", "enumerator", "enumset", "enum-set", "set", "enummap", "enum-map", "map", "type", "<PERSON><PERSON>t", "type-hint"], "homepage": "https://github.com/marc-mabe/php-enum", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^7.1 | ^8.0", "ext-reflection": "*"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "autoload-dev": {"psr-4": {"MabeEnumTest\\": "tests/MabeEnumTest/", "MabeEnumStaticAnalysis\\": "tests/MabeEnumStaticAnalysis/", "MabeEnumBench\\": "bench/"}}, "extra": {"branch-alias": {"dev-master": "4.7-dev", "dev-3.x": "3.2-dev"}}}