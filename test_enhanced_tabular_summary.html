<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Tabular Summary</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .data-display { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Enhanced Tabular Summary Test</h1>
    
    <div class="section">
        <h2>Test Enhanced Tabular Summary Structure</h2>
        <button onclick="testTabularSummary()">Test Tabular Summary API</button>
        <button onclick="testAISummary()">Test AI Summary with Enhanced Data</button>
        <div id="results"></div>
    </div>

    <div class="section">
        <h2>Sample Enhanced Data Structure</h2>
        <div class="data-display">
            <pre id="sampleData"></pre>
        </div>
    </div>

    <script>
        // Sample enhanced data structure
        const sampleEnhancedData = {
            detailed_summary: [
                {
                    main_driver: "Service Quality",
                    sub_driver: "Response Time",
                    sentiment: "Negative",
                    domain_category: "Customer Service",
                    lob: "Telecom",
                    vendor: "Vendor A",
                    location: "Mumbai",
                    partner: "Partner 1",
                    team: "Team Alpha",
                    resolution_status: "Unresolved",
                    pain_points: "Delayed response from agent",
                    suggestions: "Improve response time SLA",
                    total_comments: 45,
                    avg_csat: 2.3,
                    avg_nps: 1.8,
                    positive_count: 5,
                    negative_count: 35,
                    neutral_count: 5,
                    resolved_count: 10
                }
            ],
            overall_statistics: {
                total_records: 1250,
                overall_avg_csat: 3.2,
                overall_avg_nps: 2.8,
                unique_drivers: 8,
                unique_vendors: 5,
                unique_locations: 12,
                total_positive: 320,
                total_negative: 680,
                total_neutral: 250,
                positive_percentage: 25.6,
                negative_percentage: 54.4
            },
            top_issues: [
                {
                    main_driver: "Service Quality",
                    sub_driver: "Response Time",
                    frequency: 245,
                    avg_csat: 2.1,
                    avg_nps: 1.9,
                    negative_count: 200
                },
                {
                    main_driver: "Agent Behavior",
                    sub_driver: "Lack of Empathy",
                    frequency: 180,
                    avg_csat: 2.3,
                    avg_nps: 2.0,
                    negative_count: 150
                }
            ],
            vendor_performance: [
                {
                    vendor: "Vendor A",
                    total_comments: 450,
                    avg_csat: 2.8,
                    avg_nps: 2.5,
                    negative_feedback: 280,
                    negative_percentage: 62.2
                },
                {
                    vendor: "Vendor B",
                    total_comments: 380,
                    avg_csat: 3.5,
                    avg_nps: 3.2,
                    negative_feedback: 150,
                    negative_percentage: 39.5
                }
            ]
        };

        // Display sample data
        document.getElementById('sampleData').textContent = JSON.stringify(sampleEnhancedData, null, 2);

        async function testTabularSummary() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing tabular summary API...</p>';

            try {
                const response = await fetch('data_tabular_summary.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        filters: {
                            // Add some basic filters for testing
                            date_from: '2024-01-01',
                            date_to: '2024-12-31'
                        }
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ API Test Successful!</h3>
                            <p><strong>Data Structure:</strong></p>
                            <ul>
                                <li>Detailed Summary Records: ${result.data.detailed_summary ? result.data.detailed_summary.length : 'N/A'}</li>
                                <li>Overall Statistics: ${result.data.overall_statistics ? 'Available' : 'Not Available'}</li>
                                <li>Top Issues: ${result.data.top_issues ? result.data.top_issues.length : 'N/A'}</li>
                                <li>Vendor Performance: ${result.data.vendor_performance ? result.data.vendor_performance.length : 'N/A'}</li>
                            </ul>
                            <details>
                                <summary>View Raw Data</summary>
                                <pre>${JSON.stringify(result.data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ API Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function testAISummary() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing AI summary with enhanced data...</p>';

            try {
                // Test the enhanced prompt building function
                const prompt = buildEnhancedPromptWithSummaryData(
                    sampleEnhancedData.detailed_summary,
                    sampleEnhancedData.overall_statistics,
                    sampleEnhancedData.top_issues,
                    sampleEnhancedData.vendor_performance,
                    3.2,
                    2.8
                );

                resultsDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Enhanced Prompt Generated!</h3>
                        <p><strong>Prompt Length:</strong> ${prompt.length} characters</p>
                        <details>
                            <summary>View Generated Prompt</summary>
                            <pre style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;">${prompt}</pre>
                        </details>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Prompt Generation Error: ${error.message}</div>`;
            }
        }

        // Include the enhanced prompt builder function for testing
        function buildEnhancedPromptWithSummaryData(tableData, overallStats, topIssues, vendorPerformance, avgCSAT, avgNPS) {
            const overallSection = overallStats ? `
OVERALL STATISTICS:
- Total Records: ${overallStats.total_records}
- Average CSAT: ${overallStats.overall_avg_csat} (Scale: 1-5)
- Average NPS: ${overallStats.overall_avg_nps} (Scale: 1-5)
- Unique Drivers: ${overallStats.unique_drivers}
- Unique Vendors: ${overallStats.unique_vendors}
- Unique Locations: ${overallStats.unique_locations}
- Sentiment Distribution: ${overallStats.total_positive} Positive (${overallStats.positive_percentage}%), ${overallStats.total_negative} Negative (${overallStats.negative_percentage}%), ${overallStats.total_neutral} Neutral
` : '';

            const topIssuesSection = topIssues && topIssues.length > 0 ? `
TOP ISSUES BY FREQUENCY:
${topIssues.map(issue => `- ${issue.main_driver} > ${issue.sub_driver}: ${issue.frequency} mentions, CSAT ${issue.avg_csat}, NPS ${issue.avg_nps}, ${issue.negative_count} negative`).join('\n')}
` : '';

            const vendorSection = vendorPerformance && vendorPerformance.length > 0 ? `
VENDOR PERFORMANCE ANALYSIS:
${vendorPerformance.map(vendor => `- ${vendor.vendor}: ${vendor.total_comments} comments, CSAT ${vendor.avg_csat}, NPS ${vendor.avg_nps}, ${vendor.negative_feedback} negative (${vendor.negative_percentage}%)`).join('\n')}
` : '';

            const detailedSection = tableData && tableData.length > 0 ? `
DETAILED BREAKDOWN BY CATEGORY:
${tableData.slice(0, 20).map(row => `- ${row.main_driver}/${row.sub_driver} (${row.sentiment}): ${row.total_comments} comments, CSAT ${row.csat_score || row.avg_csat}, NPS ${row.nps_score || row.avg_nps}, Vendor: ${row.vendor}, Location: ${row.location}`).join('\n')}
` : '';

            return `You are an expert customer experience analyst. Analyze this comprehensive feedback dataset and provide a detailed executive summary.

${overallSection}
${topIssuesSection}
${vendorSection}
${detailedSection}

CRITICAL INSTRUCTIONS:
1. Output BOTH Part 1 AND Part 2 in full - do not summarize or merge
2. Use EXACT HTML tags as specified
3. Include ALL requested sections with actual calculated data
4. Use the provided data structure
5. Maintain professional tone throughout
6. Calculate actual numbers from the data provided above

PART 1: EXECUTIVE SUMMARY
Generate exactly this structure with actual data:

<h3><strong>📊 Executive Summary</strong></h3>
<p><strong>Dataset Overview:</strong></p>
<p>Analyzed customer feedback across with an average CSAT of <strong>${avgCSAT.toFixed(1)}</strong> and NPS of <strong>${avgNPS.toFixed(1)}</strong>. Overall customer satisfaction appears to be [analyze based on scores], with a significant number of comments indicating [analyze sentiment patterns].</p>

Remember: Use ONLY the data provided above. Calculate actual numbers, don't use placeholders. Ensure all tables contain real data from the analysis.`;
        }
    </script>
</body>
</html>
