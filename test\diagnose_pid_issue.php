<?php
/**
 * <PERSON><PERSON>t to diagnose data issues focusing on pid as the unique identifier
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set high memory and execution time limits
ini_set('memory_limit', '1G');
ini_set('max_execution_time', 3600); // 1 hour

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682b54974bab6';

// Log file
$log_file = 'pid_diagnosis.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    
    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting PID-based diagnosis for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Count total records by pid
    log_message("Counting total records by pid...");
    
    $totalPidQuery = "SELECT COUNT(DISTINCT pid) as count FROM feedback_data WHERE data_id = :data_id";
    $totalPidStmt = $conn->prepare($totalPidQuery);
    $totalPidStmt->bindParam(':data_id', $data_id);
    $totalPidStmt->execute();
    $total_pid_count = $totalPidStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Count processed records by pid
    log_message("Counting processed records by pid...");
    
    $processedPidQuery = "
        SELECT COUNT(DISTINCT ac.pid) as count
        FROM analyzed_comments ac
        WHERE ac.data_id = :data_id
    ";
    $processedPidStmt = $conn->prepare($processedPidQuery);
    $processedPidStmt->bindParam(':data_id', $data_id);
    $processedPidStmt->execute();
    $processed_pid_count = $processedPidStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Count queued records by pid
    log_message("Counting queued records by pid...");
    
    $queuedPidQuery = "
        SELECT COUNT(DISTINCT cq.pid) as count
        FROM comment_queue cq
        WHERE cq.data_id = :data_id
    ";
    $queuedPidStmt = $conn->prepare($queuedPidQuery);
    $queuedPidStmt->bindParam(':data_id', $data_id);
    $queuedPidStmt->execute();
    $queued_pid_count = $queuedPidStmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $missing_pid_count = $total_pid_count - $processed_pid_count - $queued_pid_count;
    
    log_message("PID Counts - Feedback data: $total_pid_count, Analyzed comments: $processed_pid_count, In queue: $queued_pid_count, Missing: $missing_pid_count");
    
    // Find missing PIDs
    log_message("Finding missing PIDs...");
    
    $missingPidQuery = "
        SELECT fd.pid
        FROM feedback_data fd
        WHERE fd.data_id = :data_id
        AND NOT EXISTS (
            SELECT 1 FROM analyzed_comments ac
            WHERE ac.data_id = fd.data_id AND ac.pid = fd.pid
        )
        AND NOT EXISTS (
            SELECT 1 FROM comment_queue cq
            WHERE cq.data_id = fd.data_id AND cq.pid = fd.pid
        )
        GROUP BY fd.pid
        LIMIT 1000
    ";
    
    $missingPidStmt = $conn->prepare($missingPidQuery);
    $missingPidStmt->bindParam(':data_id', $data_id);
    $missingPidStmt->execute();
    $missingPids = $missingPidStmt->fetchAll(PDO::FETCH_COLUMN);
    
    log_message("Found " . count($missingPids) . " missing PIDs");
    
    if (count($missingPids) > 0) {
        log_message("Sample missing PIDs: " . implode(', ', array_slice($missingPids, 0, 10)));
        
        // Get sample records for missing PIDs
        $sampleSize = min(5, count($missingPids));
        $samplePids = array_slice($missingPids, 0, $sampleSize);
        $placeholders = implode(',', array_fill(0, count($samplePids), '?'));
        
        $sampleQuery = "
            SELECT id, pid, feedback_data
            FROM feedback_data
            WHERE data_id = :data_id AND pid IN ($placeholders)
            LIMIT 10
        ";
        
        $sampleStmt = $conn->prepare($sampleQuery);
        $sampleStmt->bindParam(':data_id', $data_id);
        
        foreach ($samplePids as $index => $pid) {
            $sampleStmt->bindValue($index + 1, $pid);
        }
        
        $sampleStmt->execute();
        $sampleRecords = $sampleStmt->fetchAll(PDO::FETCH_ASSOC);
        
        log_message("Sample records for missing PIDs:");
        foreach ($sampleRecords as $record) {
            $truncatedFeedback = substr($record['feedback_data'], 0, 50);
            if (strlen($record['feedback_data']) > 50) {
                $truncatedFeedback .= "...";
            }
            log_message("  - ID: " . $record['id'] . ", PID: " . $record['pid'] . ", Feedback: '$truncatedFeedback'");
        }
        
        // Check for duplicate PIDs in feedback_data
        log_message("Checking for duplicate PIDs in feedback_data...");
        
        $duplicatePidQuery = "
            SELECT pid, COUNT(*) as count
            FROM feedback_data
            WHERE data_id = :data_id
            GROUP BY pid
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
            LIMIT 10
        ";
        
        $duplicatePidStmt = $conn->prepare($duplicatePidQuery);
        $duplicatePidStmt->bindParam(':data_id', $data_id);
        $duplicatePidStmt->execute();
        $duplicatePids = $duplicatePidStmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($duplicatePids) > 0) {
            log_message("Found " . count($duplicatePids) . " PIDs with multiple records in feedback_data");
            log_message("Top duplicate PIDs:");
            foreach ($duplicatePids as $dup) {
                log_message("  - PID: " . $dup['pid'] . " appears " . $dup['count'] . " times");
            }
        } else {
            log_message("No duplicate PIDs found in feedback_data");
        }
    }
    
    // Create a script to enqueue missing records by PID
    if (count($missingPids) > 0) {
        log_message("Creating a script to enqueue missing records by PID...");
        
        $enqueueScript = "<?php
/**
 * Script to enqueue missing records by PID
 * Generated by diagnose_pid_issue.php
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
\$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
\$dbname = 'goslabsprojectwave2_1';
\$username = 'deploy';
\$password = 'fNas2{7T8oBj';

// Data ID to process
\$data_id = '$data_id';

// Log file
\$log_file = 'enqueue_by_pid.log';

// Function to log messages
function log_message(\$message) {
    global \$log_file;
    
    \$timestamp = date('Y-m-d H:i:s');
    \$log_entry = \"[\$timestamp] \$message\" . PHP_EOL;
    
    // Write to log file
    file_put_contents(\$log_file, \$log_entry, FILE_APPEND);
    
    // Also output to console
    echo \$log_entry;
}

// Connect to database
try {
    log_message(\"Starting to enqueue missing records by PID for data_id: \$data_id\");
    
    \$dsn = \"mysql:host={\$host};dbname={\$dbname};charset=utf8\";
    \$conn = new PDO(\$dsn, \$username, \$password);
    \$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message(\"Connected to database\");
    
    // Get missing records by PID
    \$missingQuery = \"
        SELECT fd.*
        FROM feedback_data fd
        WHERE fd.data_id = :data_id
        AND NOT EXISTS (
            SELECT 1 FROM analyzed_comments ac
            WHERE ac.data_id = fd.data_id AND ac.pid = fd.pid
        )
        AND NOT EXISTS (
            SELECT 1 FROM comment_queue cq
            WHERE cq.data_id = fd.data_id AND cq.pid = fd.pid
        )
        GROUP BY fd.pid
        LIMIT 2000
    \";
    
    \$missingStmt = \$conn->prepare(\$missingQuery);
    \$missingStmt->bindParam(':data_id', \$data_id);
    \$missingStmt->execute();
    \$missingRecords = \$missingStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message(\"Found \" . count(\$missingRecords) . \" missing records by PID\");
    
    // Process missing records
    \$enqueued = 0;
    \$failed = 0;
    \$total = count(\$missingRecords);
    
    foreach (\$missingRecords as \$index => \$record) {
        log_message(\"Processing record ID: \" . \$record['id'] . \", PID: \" . \$record['pid'] . \" (\" . (\$index + 1) . \" of \$total)\");
        
        try {
            // Insert into comment_queue
            \$insertQuery = \"INSERT INTO comment_queue (
                comment, data_id, user_id, csat, nps, pid, status,
                domain_category, resolution_comment, internal_scores, feedback_submit_date,
                feedback_month, feedback_time, lob, vendor, location, partner,
                dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending',
                :domain_category, :resolution_comment, :internal_scores, :feedback_submit_date,
                :feedback_month, :feedback_time, :lob, :vendor, :location, :partner,
                :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
            )\";
            
            \$insertStmt = \$conn->prepare(\$insertQuery);
            \$insertStmt->bindParam(':comment', \$record['feedback_data']);
            \$insertStmt->bindParam(':data_id', \$record['data_id']);
            \$insertStmt->bindParam(':user_id', \$record['user_id']);
            \$insertStmt->bindParam(':csat', \$record['csat']);
            \$insertStmt->bindParam(':nps', \$record['nps']);
            \$insertStmt->bindParam(':pid', \$record['pid']);
            \$insertStmt->bindParam(':domain_category', \$record['domain_category']);
            \$insertStmt->bindParam(':resolution_comment', \$record['resolution_comment']);
            \$insertStmt->bindParam(':internal_scores', \$record['internal_scores']);
            \$insertStmt->bindParam(':feedback_submit_date', \$record['feedback_submit_date']);
            \$insertStmt->bindParam(':feedback_month', \$record['feedback_month']);
            \$insertStmt->bindParam(':feedback_time', \$record['feedback_time']);
            \$insertStmt->bindParam(':lob', \$record['lob']);
            \$insertStmt->bindParam(':vendor', \$record['vendor']);
            \$insertStmt->bindParam(':location', \$record['location']);
            \$insertStmt->bindParam(':partner', \$record['partner']);
            \$insertStmt->bindParam(':dummy_1', \$record['dummy_1']);
            \$insertStmt->bindParam(':dummy_2', \$record['dummy_2']);
            \$insertStmt->bindParam(':dummy_3', \$record['dummy_3']);
            \$insertStmt->bindParam(':dummy_4', \$record['dummy_4']);
            \$insertStmt->bindParam(':dummy_5', \$record['dummy_5']);
            
            \$result = \$insertStmt->execute();
            
            if (\$result) {
                \$enqueued++;
                if (\$enqueued % 10 == 0 || \$enqueued == \$total) {
                    log_message(\"Enqueued \$enqueued of \$total records\");
                }
            } else {
                \$failed++;
                log_message(\"Failed to enqueue record ID: \" . \$record['id'] . \" - Error: \" . json_encode(\$insertStmt->errorInfo()));
            }
        } catch (PDOException \$e) {
            \$failed++;
            log_message(\"ERROR: Failed to enqueue record ID: \" . \$record['id'] . \" - \" . \$e->getMessage());
        }
        
        // Sleep briefly every 100 records to avoid overloading the database
        if (\$index % 100 == 99) {
            log_message(\"Sleeping briefly to avoid overloading the database...\");
            usleep(500000); // 0.5 seconds
        }
    }
    
    log_message(\"Processing completed - Enqueued: \$enqueued, Failed: \$failed\");
    
} catch (PDOException \$e) {
    log_message(\"Database error: \" . \$e->getMessage());
}
";
        
        file_put_contents("enqueue_by_pid.php", $enqueueScript);
        log_message("Created enqueue_by_pid.php script to process the missing records");
    }
    
    // Conclusion
    log_message("\nDiagnosis Summary:");
    log_message("1. Total unique PIDs - Feedback data: $total_pid_count, Analyzed comments: $processed_pid_count, In queue: $queued_pid_count");
    log_message("2. Missing PIDs: $missing_pid_count");
    
    if ($missing_pid_count > 0) {
        log_message("\nConclusion: There are $missing_pid_count PIDs in feedback_data that don't have corresponding records in analyzed_comments or comment_queue.");
        log_message("These records should be enqueued for processing. A script has been created (enqueue_by_pid.php) to do this.");
    } else {
        log_message("\nConclusion: All PIDs in feedback_data have corresponding records in analyzed_comments or comment_queue.");
        log_message("The system appears to be working correctly when using PID as the unique identifier.");
    }
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
