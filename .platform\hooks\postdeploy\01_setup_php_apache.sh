#!/bin/bash
set -euxo pipefail

LOG_FILE="/var/log/eb-postdeploy.log"
exec > >(tee -a ${LOG_FILE}) 2>&1

echo "Starting postdeploy setup..."

# 1. Write PHP and Apache config files
cat <<EOF > /etc/httpd/conf.d/01_directory_upload_limit.conf
<Directory "/var/app/current">
    LimitRequestBody 104857600
</Directory>
EOF

cat <<EOF > /etc/httpd/conf.d/02_apache_timeouts.conf
Timeout 900
ProxyTimeout 900
KeepAliveTimeout 40
EOF

cat <<EOF > /etc/php.d/99-custom.ini
max_execution_time = 2100
max_input_time     = 1600
memory_limit       = 1024M
post_max_size      = 100M
upload_max_filesize= 100M
display_errors     = Off
output_buffering   = 5096
realpath_cache_size = 6M
realpath_cache_ttl = 120
session.gc_maxlifetime = 7200

; Excel export specific settings
sys_temp_dir = /tmp
upload_tmp_dir = /tmp
max_input_vars = 3000

; Ensure required extensions are enabled
extension=zip
extension=xml
extension=gd
extension=mbstring
extension=fileinfo
extension=dom
extension=xmlreader
extension=xmlwriter
EOF

cat <<EOF > /etc/php-fpm.d/z-timeouts.conf
[www]
request_terminate_timeout = 900s
EOF

cat <<EOF > /etc/httpd/conf.d/zz_fcgi_timeouts.conf
<Proxy "unix:/run/php-fpm/www.sock|fcgi://php-fpm">
    ProxySet disablereuse=on \
             timeout=900 \
             connectiontimeout=900 \
             acquire=900
    Require all granted
</Proxy>
ProxyPreserveHost on
EOF

echo "Config files created."

# 2. Install PHP extensions (including zip)
echo "Installing PHP extensions..."
dnf update -y
dnf install -y \
  php-zip php-xml php-gd php-mbstring php-intl php-mysqlnd \
  php-curl php-pdo php-json php-fileinfo php-dom php-simplexml \
  php-xmlreader php-xmlwriter php-opcache php-bcmath php-iconv php-ctype

# 3. Check if zip extension is installed, else try PECL install
if php -m | grep -q zip; then
    echo "PHP zip extension already installed."
else
    echo "PHP zip extension missing, trying alternative installation..."
    dnf install -y php-pear php-devel gcc make libzip-devel
    printf "\n" | pecl install zip || echo "PECL install may have failed"
    
    PHP_INI_DIR=$(php -i | grep "Scan this dir for additional .ini files" | awk '{print $NF}')
    [ -z "$PHP_INI_DIR" ] && PHP_INI_DIR="/etc/php.d"
    echo "extension=zip.so" > "${PHP_INI_DIR}/30-zip.ini"
fi

# 4. Setup temp directories for Excel exports
echo "Setting up temp directories for Excel exports..."
mkdir -p /tmp/excel_exports
chmod 755 /tmp/excel_exports
chown webapp:webapp /tmp/excel_exports || true

# Ensure main temp directory has proper permissions
chmod 755 /tmp
chown root:root /tmp

# 5. Restart PHP and Apache
echo "Restarting php-fpm and httpd..."
systemctl restart php-fpm || true
systemctl restart httpd || true

# 6. Setup worker script and cron
echo "Setting up worker script and cron..."

cat <<'EOS' > /usr/local/bin/run-worker.sh
#!/bin/bash
if pgrep -f "php /var/app/current/worker.php" > /dev/null; then
    echo "Worker script is already running. Skipping this run." >> /var/log/cron.log 2>&1
    exit 0
fi
/usr/bin/php /var/app/current/worker.php >> /var/log/cron.log 2>&1
EOS

chmod +x /usr/local/bin/run-worker.sh

cat <<EOF > /etc/logrotate.d/worker
/var/log/cron.log {
    daily
    rotate 7
    missingok
    notifempty
    compress
    delaycompress
    copytruncate
}
EOF

touch /var/log/cron.log
chmod 666 /var/log/cron.log

echo '* * * * * root /usr/local/bin/run-worker.sh' > /etc/cron.d/worker
chmod 644 /etc/cron.d/worker

systemctl restart crond || true

echo "Postdeploy script completed."