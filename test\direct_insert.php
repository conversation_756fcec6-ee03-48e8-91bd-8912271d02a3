<?php
// Direct insertion script for data_id 682b54974bab6

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = '682b54974bab6';

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database\n";
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    echo "Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count\n";
    
    if ($missing_count <= 0) {
        echo "No missing records\n";
        exit(0);
    }
    
    // Get missing records
    $query = "
        SELECT fd.*
        FROM feedback_data fd
        LEFT JOIN analyzed_comments ac ON ac.data_id = fd.data_id AND ac.comment = fd.feedback_data
        WHERE fd.data_id = :data_id AND ac.id IS NULL
        LIMIT 10
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':data_id', $data_id);
    $stmt->execute();
    
    $missingRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($missingRecords) . " missing records\n";
    
    // Insert missing records directly into analyzed_comments
    $processed = 0;
    foreach ($missingRecords as $record) {
        try {
            $insertQuery = "INSERT INTO analyzed_comments (
                comment, data_id, user_id, csat, nps, pid,
                main_driver, sub_driver, sentiment
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid,
                'Auto-Generated', 'Auto-Generated', 'Neutral'
            )";
            
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindParam(':comment', $record['feedback_data']);
            $insertStmt->bindParam(':data_id', $data_id);
            $insertStmt->bindParam(':user_id', $record['user_id']);
            $insertStmt->bindParam(':csat', $record['csat']);
            $insertStmt->bindParam(':nps', $record['nps']);
            $insertStmt->bindParam(':pid', $record['pid']);
            
            $result = $insertStmt->execute();
            
            if ($result) {
                $processed++;
                echo "Inserted record ID: " . $record['id'] . "\n";
            } else {
                echo "Failed to insert record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()) . "\n";
            }
        } catch (PDOException $e) {
            echo "ERROR: Failed to insert record ID: " . $record['id'] . " - " . $e->getMessage() . "\n";
        }
    }
    
    echo "Processed $processed records\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
}
