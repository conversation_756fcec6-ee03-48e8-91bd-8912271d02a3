<?php
session_start();
if (!isset($_SESSION['feedback_data_id'])) {
    header('Location: upload.php');
    exit();
}

$data_id = $_SESSION['feedback_data_id'];

// Simulate background processing
// In a real-world scenario, you might use a queue system like RabbitMQ or a job scheduler like Laravel Queue
echo "Data processing started. You will be notified once it is complete.";
header('Location: dashboard.php');
exit();