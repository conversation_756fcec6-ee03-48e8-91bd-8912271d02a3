# extracted from "https://github.com/Mermade/openapi3-examples"
# this is an example of the Uber API
# as a demonstration of an API spec in YAML
openapi: "3.0.0"
info:
  title: Uber API
  description: Move your app forward with the Uber API
  version: "1.0.0"
servers:
  - url: https://api.uber.com/v1
paths:
  /products:
    get:
      summary: Product Types
      description: The Products endpoint returns information about the Uber products offered at a given location. The response includes the display name and other details about each product, and lists the products in the proper display order.
      parameters:
        - name: latitude
          in: query
          description: Latitude component of location.
          required: true
          schema:
            type: number
            format: double
        - name: longitude
          in: query
          description: Longitude component of location.
          required: true
          schema:
            type: number
            format: double
      security:
        - apikey: []
      tags:
        - Products
      responses:
        200:
          description: An array of products
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductList"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /estimates/price:
    get:
      summary: Price Estimates
      description: The Price Estimates endpoint returns an estimated price range for each product offered at a given location. The price estimate is provided as a formatted string with the full price range and the localized currency symbol.<br><br>The response also includes low and high estimates, and the [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217) currency code for situations requiring currency conversion. When surge is active for a particular product, its surge_multiplier will be greater than 1, but the price estimate already factors in this multiplier.
      parameters:
        - name: start_latitude
          in: query
          description: Latitude component of start location.
          required: true
          schema:
            type: number
            format: double
        - name: start_longitude
          in: query
          description: Longitude component of start location.
          required: true
          schema:
            type: number
            format: double
        - name: end_latitude
          in: query
          description: Latitude component of end location.
          required: true
          schema:
            type: number
            format: double
        - name: end_longitude
          in: query
          description: Longitude component of end location.
          required: true
          schema:
            type: number
            format: double
      tags:
        - Estimates
      responses:
        200:
          description: An array of price estimates by product
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PriceEstimate"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /estimates/time:
    get:
      summary: Time Estimates
      description: The Time Estimates endpoint returns ETAs for all products offered at a given location, with the responses expressed as integers in seconds. We recommend that this endpoint be called every minute to provide the most accurate, up-to-date ETAs.
      parameters:
        - name: start_latitude
          in: query
          description: Latitude component of start location.
          required: true
          schema:
            type: number
            format: double
        - name: start_longitude
          in: query
          description: Longitude component of start location.
          required: true
          schema:
            type: number
            format: double
        - name: customer_uuid
          in: query
          schema:
            type: string
            format: uuid
          description: Unique customer identifier to be used for experience customization.
        - name: product_id
          in: query
          schema:
            type: string
          description: Unique identifier representing a specific product for a given latitude & longitude.
      tags:
        - Estimates
      responses:
        200:
          description: An array of products
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Product"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /me:
    get:
      summary: User Profile
      description: The User Profile endpoint returns information about the Uber user that has authorized with the application.
      tags:
        - User
      responses:
        200:
          description: Profile information for a user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Profile"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /history:
    get:
      summary: User Activity
      description: The User Activity endpoint returns data about a user's lifetime activity with Uber. The response will include pickup locations and times, dropoff locations and times, the distance of past requests, and information about which products were requested.<br><br>The history array in the response will have a maximum length based on the limit parameter. The response value count may exceed limit, therefore subsequent API requests may be necessary.
      parameters:
        - name: offset
          in: query
          schema:
            type: integer
            format: int32
          description: Offset the list of returned results by this amount. Default is zero.
        - name: limit
          in: query
          schema:
            type: integer
            format: int32
          description: Number of items to retrieve. Default is 5, maximum is 100.
      tags:
        - User
      responses:
        200:
          description: History information for the given user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Activities"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
components:
  securitySchemes:
    apikey:
      type: apiKey
      name: server_token
      in: query
  schemas:
    Product:
      properties:
        product_id:
          type: string
          description: Unique identifier representing a specific product for a given latitude & longitude. For example, uberX in San Francisco will have a different product_id than uberX in Los Angeles.
        description:
          type: string
          description: Description of product.
        display_name:
          type: string
          description: Display name of product.
        capacity:
          type: integer
          description: Capacity of product. For example, 4 people.
        image:
          type: string
          description: Image URL representing the product.
    ProductList:
      properties:
        products:
          description: Contains the list of products
          type: array
          items:
            $ref: "#/components/schemas/Product"
    PriceEstimate:
      properties:
        product_id:
          type: string
          description: Unique identifier representing a specific product for a given latitude & longitude. For example, uberX in San Francisco will have a different product_id than uberX in Los Angeles
        currency_code:
          type: string
          description: "[ISO 4217](http://en.wikipedia.org/wiki/ISO_4217) currency code."
        display_name:
          type: string
          description: Display name of product.
        estimate:
          type: string
          description: Formatted string of estimate in local currency of the start location. Estimate could be a range, a single number (flat rate) or "Metered" for TAXI.
        low_estimate:
          type: number
          description: Lower bound of the estimated price.
        high_estimate:
          type: number
          description: Upper bound of the estimated price.
        surge_multiplier:
          type: number
          description: Expected surge multiplier. Surge is active if surge_multiplier is greater than 1. Price estimate already factors in the surge multiplier.
    Profile:
      properties:
        first_name:
          type: string
          description: First name of the Uber user.
        last_name:
          type: string
          description: Last name of the Uber user.
        email:
          type: string
          description: Email address of the Uber user
        picture:
          type: string
          description: Image URL of the Uber user.
        promo_code:
          type: string
          description: Promo code of the Uber user.
    Activity:
      properties:
        uuid:
          type: string
          description: Unique identifier for the activity
    Activities:
      properties:
        offset:
          type: integer
          format: int32
          description: Position in pagination.
        limit:
          type: integer
          format: int32
          description: Number of items to retrieve (100 max).
        count:
          type: integer
          format: int32
          description: Total number of items available.
        history:
          type: array
          items:
            $ref: "#/components/schemas/Activity"
    Error:
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
        fields:
          type: string
