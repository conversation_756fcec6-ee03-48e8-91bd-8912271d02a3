-- Database Performance Indexes for Dashboard Optimization
-- MySQL Compatible Version (No IF NOT EXISTS syntax)
-- Run this script to create all required indexes for optimal dashboard performance
-- Compatible with MySQL 5.7+ and AWS RDS
-- 
-- IMPORTANT: If an index already exists, you'll get an error but can continue with the next one
-- This is normal behavior in MySQL

-- Main performance index for dashboard queries (most critical)
CREATE INDEX idx_analyzed_comments_performance 
ON analyzed_comments (data_id, sentiment, domain_category, user_id);

-- Time-series queries optimization
CREATE INDEX idx_analyzed_comments_created_at 
ON analyzed_comments (created_at, data_id, user_id);

-- Driver-specific indexes
CREATE INDEX idx_analyzed_comments_main_driver 
ON analyzed_comments (main_driver, data_id, user_id);

CREATE INDEX idx_analyzed_comments_sub_driver 
ON analyzed_comments (sub_driver, data_id, user_id);

-- Sentiment analysis optimization
CREATE INDEX idx_analyzed_comments_sentiment_analysis 
ON analyzed_comments (sentiment, data_id, user_id, created_at);

-- <PERSON><PERSON><PERSON>, Vendor, Location, Partner sentiment indexes
CREATE INDEX idx_analyzed_comments_lob 
ON analyzed_comments (lob, sentiment, data_id, user_id);

CREATE INDEX idx_analyzed_comments_vendor 
ON analyzed_comments (vendor, sentiment, data_id, user_id);

CREATE INDEX idx_analyzed_comments_location 
ON analyzed_comments (location, sentiment, data_id, user_id);

CREATE INDEX idx_analyzed_comments_partner 
ON analyzed_comments (partner, sentiment, data_id, user_id);

-- Index for CSAT and NPS impact queries (corrected column names)
CREATE INDEX idx_analyzed_comments_scores 
ON analyzed_comments (csat, nps, data_id, user_id);

-- Index for internal scores
CREATE INDEX idx_analyzed_comments_internal_scores 
ON analyzed_comments (internal_scores, data_id, user_id);

-- Composite index for word cloud queries (corrected column name)
CREATE INDEX idx_analyzed_comments_verbatim 
ON analyzed_comments (data_id, user_id, sentiment, verbitm(100));

-- Index for PID queries (for duplicate checking)
CREATE INDEX idx_analyzed_comments_pid 
ON analyzed_comments (pid, data_id, user_id);

-- Index for feedback date queries
CREATE INDEX idx_analyzed_comments_feedback_date 
ON analyzed_comments (feedback_submit_date, data_id, user_id);

-- Additional domain category filter index
CREATE INDEX idx_analyzed_comments_domain_filter 
ON analyzed_comments (domain_category, data_id, user_id, sentiment);

-- Show all indexes after creation (optional verification)
-- SHOW INDEX FROM analyzed_comments;
