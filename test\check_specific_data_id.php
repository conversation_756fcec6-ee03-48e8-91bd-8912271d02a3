<?php
// <PERSON><PERSON>t to check records for specific data_id

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Checking records for data_id = '686793923253e'...\n";

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    // Connect to the database
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Successfully connected to database!\n";
    
    // Query to count records for the specific data_id
    $data_id = '686793923253e';
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id");
    $stmt->bindParam(':data_id', $data_id);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Number of records for data_id '{$data_id}': {$result['count']}\n";
    
    // If records exist, show some details
    if ($result['count'] > 0) {
        echo "\nSample records for data_id '{$data_id}':\n";
        echo "----------------------------------------\n";
        
        $stmt = $conn->prepare("SELECT id, comment, sentiment, main_driver, csat, nps, created_at FROM analyzed_comments WHERE data_id = :data_id LIMIT 5");
        $stmt->bindParam(':data_id', $data_id);
        $stmt->execute();
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "ID: {$row['id']}\n";
            echo "Sentiment: {$row['sentiment']}\n";
            echo "Main Driver: {$row['main_driver']}\n";
            echo "CSAT: {$row['csat']}, NPS: {$row['nps']}\n";
            echo "Created: {$row['created_at']}\n";
            echo "Comment: " . substr($row['comment'], 0, 100) . "...\n";
            echo "----------------------------------------\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nQuery completed.\n";
?> 