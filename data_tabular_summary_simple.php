<?php
// Simple version of tabular summary for testing
session_start();
require_once 'config.php';

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    // Check if user is authenticated
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'error' => 'User not authenticated.']);
        exit;
    }

    $user_id = $_SESSION['user_id'];

    // Get database connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Simple tabular summary query
    $sql = "SELECT 
      main_driver,
      sub_driver,
      sentiment,
      domain_category,
      lob,
      vendor,
      location,
      partner,
      dummy_1 as team,
      dummy_5 as resolution_status,
      painpointscustomerfrustrations as pain_points,
      suggestionsforimprovement as suggestions,
      COUNT(*) as total_comments,
      ROUND(AVG(CAST(csat AS DECIMAL(5,2))), 2) as avg_csat,
      ROUND(AVG(CAST(nps AS DECIMAL(5,2))), 2) as avg_nps
    FROM analyzed_comments
    WHERE user_id = :user_id
    GROUP BY main_driver, sub_driver, sentiment, domain_category, lob, vendor, location, partner, dummy_1, dummy_5, painpointscustomerfrustrations, suggestionsforimprovement
    ORDER BY total_comments DESC
    LIMIT 50";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([':user_id' => $user_id]);
    $tabularSummary = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Return simple structure for now
    echo json_encode(['success' => true, 'data' => $tabularSummary]);

} catch (Throwable $e) {
    // Log the error for debugging
    file_put_contents('php_errors.log', "[SimpleTabularSummary][ERROR] " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    // Always return valid JSON
    echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
    exit;
} 
?>
