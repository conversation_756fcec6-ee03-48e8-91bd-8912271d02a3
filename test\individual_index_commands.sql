-- Individual Index Creation Commands for AWS RDS MySQL
-- Run these commands one by one if the batch script fails
-- Copy and paste each command individually into your MySQL console

-- 1. Main performance index (MOST CRITICAL - Run this first)
CREATE INDEX idx_analyzed_comments_performance ON analyzed_comments (data_id, sentiment, domain_category, user_id);

-- 2. Time-series queries optimization
CREATE INDEX idx_analyzed_comments_created_at ON analyzed_comments (created_at, data_id, user_id);

-- 3. Main driver analysis
CREATE INDEX idx_analyzed_comments_main_driver ON analyzed_comments (main_driver, data_id, user_id);

-- 4. Sub driver analysis
CREATE INDEX idx_analyzed_comments_sub_driver ON analyzed_comments (sub_driver, data_id, user_id);

-- 5. Sentiment analysis optimization
CREATE INDEX idx_analyzed_comments_sentiment_analysis ON analyzed_comments (sentiment, data_id, user_id, created_at);

-- 6. LOB sentiment queries
CREATE INDEX idx_analyzed_comments_lob ON analyzed_comments (lob, sentiment, data_id, user_id);

-- 7. Vendor sentiment queries
CREATE INDEX idx_analyzed_comments_vendor ON analyzed_comments (vendor, sentiment, data_id, user_id);

-- 8. Location sentiment queries
CREATE INDEX idx_analyzed_comments_location ON analyzed_comments (location, sentiment, data_id, user_id);

-- 9. Partner sentiment queries
CREATE INDEX idx_analyzed_comments_partner ON analyzed_comments (partner, sentiment, data_id, user_id);

-- 10. CSAT and NPS impact queries
CREATE INDEX idx_analyzed_comments_scores ON analyzed_comments (csat, nps, data_id, user_id);

-- 11. Internal scores
CREATE INDEX idx_analyzed_comments_internal_scores ON analyzed_comments (internal_scores, data_id, user_id);

-- 12. Word cloud queries (verbatim text)
CREATE INDEX idx_analyzed_comments_verbatim ON analyzed_comments (data_id, user_id, sentiment, verbitm(100));

-- 13. PID queries (for duplicate checking)
CREATE INDEX idx_analyzed_comments_pid ON analyzed_comments (pid, data_id, user_id);

-- 14. Feedback date queries
CREATE INDEX idx_analyzed_comments_feedback_date ON analyzed_comments (feedback_submit_date, data_id, user_id);

-- 15. Domain category filter
CREATE INDEX idx_analyzed_comments_domain_filter ON analyzed_comments (domain_category, data_id, user_id, sentiment);

-- Verification command - Run this to see all indexes
SHOW INDEX FROM analyzed_comments;

-- Check if indexes are being used (run after creating indexes)
-- EXPLAIN SELECT sentiment, COUNT(*) FROM analyzed_comments WHERE data_id = 'your_data_id' AND user_id = 1 GROUP BY sentiment;
