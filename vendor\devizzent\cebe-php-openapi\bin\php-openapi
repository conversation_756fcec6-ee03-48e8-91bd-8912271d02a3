#!/usr/bin/env php
<?php

/**
 * PHP OpenAPI validation tool
 *
 * @copyright Copyright (c) 2018 <PERSON><PERSON> <<EMAIL>> and contributors
 * @license https://github.com/cebe/php-openapi/blob/master/LICENSE
 */

use cebe\openapi\ReferenceContext;

$composerAutoload = [
    __DIR__ . '/../vendor/autoload.php', // standalone with "composer install" run
    __DIR__ . '/../../../autoload.php',  // script is installed as a composer binary
];
foreach ($composerAutoload as $autoload) {
    if (file_exists($autoload)) {
        require($autoload);
        break;
    }
}

// Send all errors to stderr
ini_set('display_errors', 'stderr');
// open streams if not in CLI sapi
defined('STDOUT') or define('STDOUT', fopen('php://stdout', 'w'));
defined('STDERR') or define('STDERR', fopen('php://stderr', 'w'));

$command = null;
$inputFile = null;
$inputFormat = null;
$outputFile = null;
$outputFormat = null;
$silentMode = false;
$referenceMode = ReferenceContext::RESOLVE_MODE_ALL;
foreach($argv as $k => $arg) {
    if ($k == 0) {
        continue;
    }
    if ($arg[0] == '-' || $arg === 'help') {
        $arg = explode('=', $arg);
        switch($arg[0]) {
            case '--read-yaml':
                if ($inputFormat === null) {
                    $inputFormat = 'yaml';
                } else {
                    error("Conflicting arguments: only one of --read-json or --read-yaml is allowed!", "usage");
                }
            break;
            case '--read-json':
                if ($inputFormat === null) {
                    $inputFormat = 'json';
                } else {
                    error("Conflicting arguments: only one of --read-json or --read-yaml is allowed!", "usage");
                }
            break;
            case '--resolve-none':
                $referenceMode = false;
            break;
            case '--resolve-external':
                $referenceMode = ReferenceContext::RESOLVE_MODE_INLINE;
            break;
            case '--resolve-all':
                $referenceMode = ReferenceContext::RESOLVE_MODE_ALL;
            break;
            case '--write-yaml':
                if ($outputFormat === null) {
                    $outputFormat = 'yaml';
                } else {
                    error("Conflicting arguments: only one of --write-json or --write-yaml is allowed!", "usage");
                }
            break;
            case '--write-json':
                if ($outputFormat === null) {
                    $outputFormat = 'json';
                } else {
                    error("Conflicting arguments: only one of --write-json or --write-yaml is allowed!", "usage");
                }
            break;
            case '-s':
            case '--silent':
                $silentMode = true;
            break;
            case '-h':
            case '--help':
            case 'help':
                print_formatted(
                    "\BPHP OpenAPI 3 tool\C\n"
                    . "\B------------------\C\n"
                    . "by Carsten Brandt <<EMAIL>>\n\n",
                    STDERR
                );
                usage();
            break;
            default:
                error("Unknown argument " . $arg[0], "usage");
        }
    } else {
        if ($command === null) {
            $command = $arg;
            // inline is an alias for "convert --resolve-external"
            if ($command === 'inline') {
                $command = 'convert';
                $referenceMode = ReferenceContext::RESOLVE_MODE_INLINE;
            }
        } elseif ($inputFile === null) {
            $inputFile = $arg;
        } elseif ($outputFile === null) {
            if ($command !== 'convert') {
                error("Too many arguments: " . $arg, "usage");
            }
            $outputFile = $arg;
        } else {
            error("Too many arguments: " . $arg, "usage");
        }
    }
}
switch ($command) {
    case 'validate':

        $errors = [];

        $openApi = read_input($inputFile, $inputFormat);
        $referenceContext = new ReferenceContext($openApi, $inputFile ? realpath($inputFile) : '');
        $referenceContext->throwException = false;
        $referenceContext->mode = ReferenceContext::RESOLVE_MODE_INLINE;
        $openApi->resolveReferences($referenceContext);

        $openApi->setDocumentContext($openApi, new \cebe\openapi\json\JsonPointer(''));

        // Validate

        // OpenAPI version check
        $openApiVersion = $openApi->getMajorVersion();
        if ($openApiVersion === \cebe\openapi\spec\OpenApi::VERSION_UNSUPPORTED) {
            error("Unsupported OpenAPI version: " . $openApi->openapi);
        }

        $openApi->validate();
        $errors = array_merge($errors, $openApi->getErrors());

        $validator = new JsonSchema\Validator;
        $openApiData = $openApi->getSerializableData();
        $validator->validate($openApiData, (object)['$ref' => 'file://' . dirname(__DIR__) . "/schemas/openapi-v{$openApiVersion}.json"]);

        if ($validator->isValid() && empty($errors)) {
            if(!$silentMode) {
                print_formatted("The supplied API Description \B\Gvalidates\C against the OpenAPI v{$openApiVersion} schema.\n", STDERR);
            }
            exit(0);
        }

        if (!empty($errors)) {
            if ($inputFile === null) {
                print_formatted("\BErrors found while reading the API description from STDIN:\C\n", STDERR);
            } else {
                print_formatted("\BErrors found while reading the API description from {$inputFile}:\C\n", STDERR);
            }
            foreach ($errors as $error) {
                if (($openPos = strpos($error, '[')) !== false && ($closePos = strpos($error, ']')) !== false && $openPos < $closePos) {
                    $error = escape_formatted(substr($error, 0, $openPos + 1)) . '\Y'
                           . escape_formatted(substr($error, $openPos + 1, $closePos - $openPos - 1)) . '\C'
                           . escape_formatted(substr($error, $closePos));
                } else {
                    $error = escape_formatted($error);
                }
                print_formatted("- " . $error . "\n", STDERR);
            }
        }
        if (!$validator->isValid()) {
            print_formatted("\BOpenAPI v{$openApiVersion} schema violations:\C\n", STDERR);
            $errors = $validator->getErrors();
            foreach ($errors as $error) {
                // hide some errors triggered by other errors further down the path
                if (strpos($error['message'], 'The property $ref is required') !== false && substr($error['property'], -4, 4) === '$ref') {
                    $hasErrorInPath = false;
                    foreach ($errors as $suberror) {
                        if ($suberror['property'] !== $error['property'] && strpos($suberror['property'], substr($error['property'], 0, -4)) === 0) {
                            $hasErrorInPath = true;
                            break;
                        }
                    }
                    if ($hasErrorInPath) {
                        continue;
                    }
                }
                if (strpos($error['message'], 'Failed to match exactly one schema') !== false) {
                    $hasErrorInPath = false;
                    foreach ($errors as $suberror) {
                        if (strpos($suberror['property'], $error['property'] . '.') === 0) {
                            $hasErrorInPath = true;
                            break;
                        }
                    }
                    if ($hasErrorInPath) {
                        continue;
                    }
                }

                print_formatted(sprintf("- [\Y%s\C] %s\n", escape_formatted($error['property']), escape_formatted($error['message'])), STDERR);
            }
        }
        exit(2);

        break;
    case 'convert':

        $openApi = read_input($inputFile, $inputFormat);
        try {
            // set document context for correctly converting recursive references
            $openApi->setDocumentContext($openApi, new \cebe\openapi\json\JsonPointer(''));
            if ($referenceMode) {
                $referenceContext = new ReferenceContext($openApi, $inputFile ? realpath($inputFile) : '');
                $referenceContext->mode = $referenceMode;
                $openApi->resolveReferences($referenceContext);
            }
        } catch (\cebe\openapi\exceptions\UnresolvableReferenceException $e) {
            error("[\e[33m{$e->context}\e[0m] " . $e->getMessage());
        }

        if ($outputFile === null) {
            if ($outputFormat === null) {
                error("No output fromat specified, please specify --write-json or --write-yaml.", "usage");
            } elseif ($outputFormat === 'json') {
                fwrite(STDOUT, \cebe\openapi\Writer::writeToJson($openApi));
            } else {
                fwrite(STDOUT, \cebe\openapi\Writer::writeToYaml($openApi));
            }
            fclose(STDOUT);
            exit(0);
        }

        if ($outputFormat === null) {
            if (strtolower(substr($outputFile, -5, 5)) === '.json') {
                $outputFormat = 'json';
            } elseif (strtolower(substr($outputFile, -5, 5)) === '.yaml') {
                $outputFormat = 'yaml';
            } elseif (strtolower(substr($outputFile, -4, 4)) === '.yml') {
                $outputFormat = 'yaml';
            } else {
                error("Failed to detect output format from file name, please specify --write-json or --write-yaml.", "usage");
            }
        }
        if ($outputFormat === 'json') {
            \cebe\openapi\Writer::writeToJsonFile($openApi, $outputFile);
        } else {
            \cebe\openapi\Writer::writeToYamlFile($openApi, $outputFile);
        }
        exit(0);

        break;
    case null:
        error("No command specified.", "usage");
        break;
    default:
        error("Unknown command " . $command, "usage");
}



// functions

function read_input($inputFile, $inputFormat)
{
    try {
        if ($inputFile === null) {
            $fileContent = file_get_contents("php://stdin");
            if ($inputFormat === null) {
                $inputFormat = (ltrim($fileContent) === '{' && rtrim($fileContent) === '}') ? 'json' : 'yaml';
            }
            if ($inputFormat === 'json') {
                $openApi = \cebe\openapi\Reader::readFromJson($fileContent);
            } else {
                $openApi = \cebe\openapi\Reader::readFromYaml($fileContent);
            }
        } else {
            if (!file_exists($inputFile)) {
                error("File does not exist: " . $inputFile);
            }
            if ($inputFormat === null) {
                if (strtolower(substr($inputFile, -5, 5)) === '.json') {
                    $inputFormat = 'json';
                } elseif (strtolower(substr($inputFile, -5, 5)) === '.yaml') {
                    $inputFormat = 'yaml';
                } elseif (strtolower(substr($inputFile, -4, 4)) === '.yml') {
                    $inputFormat = 'yaml';
                } else {
                    error("Failed to detect input format from file name, please specify --read-json or --read-yaml.", "usage");
                }
            }
            if ($inputFormat === 'json') {
                $openApi = \cebe\openapi\Reader::readFromJsonFile(realpath($inputFile), \cebe\openapi\spec\OpenApi::class, false);
            } else {
                $openApi = \cebe\openapi\Reader::readFromYamlFile(realpath($inputFile), \cebe\openapi\spec\OpenApi::class, false);
            }
        }
        $openApi->setDocumentContext($openApi, new \cebe\openapi\json\JsonPointer(''));
    } catch (Symfony\Component\Yaml\Exception\ParseException $e) {
        error($e->getMessage());
        exit(1);
    } catch (cebe\openapi\exceptions\IOException $e) {
        error($e->getMessage());
        exit(1);
    }
    return $openApi;
}

/**
 * Display usage information
 */
function usage() {
    global $argv;
    $cmd = basename($argv[0]);
    print_formatted(<<<EOF
Usage:
  $cmd \B<command>\C [\Y<options>\C] [\Ginput.yml\C|\Ginput.json\C] [\Goutput.yml\C|\Goutput.json\C]

  The following commands are available:

    \Bvalidate\C   Validate the API Description in the specified \Ginput file\C against the OpenAPI v3.0 schema.
               Note: the validation is performed in two steps. The results are composed of
                (1) structural errors found while reading the API Description file, and
                (2) violations of the OpenAPI v3.0 schema.

               If no input file is specified input will be read from STDIN.
               The tool will try to auto-detect the content type of the input, but may fail
               to do so. You may specify \Y--read-yaml\C or \Y--read-json\C to force the file type.

               Exits with code 2 on validation errors, 1 on other errors and 0 on success.

    \Bconvert\C    Convert a JSON or YAML input file to JSON or YAML output file.

               If no input file is specified input will be read from STDIN.
               If no output file is specified output will be written to STDOUT.
               The tool will try to auto-detect the content type of the input and output file, but may fail
               to do so. You may specify \Y--read-yaml\C or \Y--read-json\C to force the input file type.
               and \Y--write-yaml\C or \Y--write-json\C to force the output file type.

               By default all references are resolved (replaced with the object referred to). You can control
               handling of references with the following arguments:

               \Y--resolve-none\C      Do not resolve references.
               \Y--resolve-external\C  Only resolve references that point to external files.
                                   This process is often referred to as "inlining".
               \Y--resolve-all\C       Resolve all references (default).
                                   Recursive pointers will stay references.

    \Binline\C     Convert a JSON or YAML input file to JSON or YAML output file and
               resolve all external references. The output will be a single API Description file.
               This is a shortcut for calling \Bconvert\C \Y--resolve-external\C.

    \Bhelp\C       Shows this usage information.

  Options:

    \Y--read-json\C   force reading input as JSON. Auto-detect if not specified.
    \Y--read-yaml\C   force reading input as YAML. Auto-detect if not specified.
    \Y--write-json\C  force writing output as JSON. Auto-detect if not specified.
    \Y--write-yaml\C  force writing output as YAML. Auto-detect if not specified.
    \Y-s, --silent\C  silent mode. Will hide all success/information messages and only print errors.


EOF
    , STDERR
);
    exit(1);
}

/**
 * Send custom error message to stderr
 * @param $message string
 * @param $callback mixed called before script exit
 * @return void
 */
function error($message, $callback = null) {
    print_formatted("\B\RError\C: " . escape_formatted($message) . "\n", STDERR);
    if (is_callable($callback)) {
        call_user_func($callback);
    }
    exit(1);
}

function print_formatted($string, $stream) {
    fwrite($stream, strtr($string, [
            '\\Y' => "\033[33m", // yellow
            '\\G' => "\033[32m", // green
            '\\R' => "\033[31m", // red
            '\\B' => "\033[1m", // bold
            '\\C' => "\033[0m", // clear
            '\\\\' => '\\',
    ]));
}

function escape_formatted($string) {
    return strtr($string, ['\\' => '\\\\']);
}
