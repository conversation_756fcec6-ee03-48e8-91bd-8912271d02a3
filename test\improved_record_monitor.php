<?php
/**
 * Improved Record Monitor
 * This version uses a more reliable method for identifying missing records
 * and handles duplicate comments correctly
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682b54974bab6';

// Log file
$log_file = 'improved_monitor.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    
    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting improved record monitor for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database: $dbname on host: $host");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    if ($missing_count <= 0) {
        log_message("No missing records for data_id: $data_id");
        exit(0);
    }
    
    // Use a more reliable method to identify missing records
    // This query finds feedback records that don't have a corresponding analyzed comment
    $missingQuery = "
        SELECT fd.* 
        FROM feedback_data fd
        LEFT JOIN (
            SELECT DISTINCT comment, data_id
            FROM analyzed_comments
            WHERE data_id = :data_id
        ) ac ON fd.feedback_data = ac.comment AND fd.data_id = ac.data_id
        WHERE fd.data_id = :data_id
        AND ac.comment IS NULL
        LIMIT 1000
    ";
    
    $missingStmt = $conn->prepare($missingQuery);
    $missingStmt->bindParam(':data_id', $data_id);
    $missingStmt->execute();
    $missingRecords = $missingStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Found " . count($missingRecords) . " missing records using JOIN method");
    
    // If we didn't find any records with the JOIN method, try a different approach
    if (count($missingRecords) == 0) {
        log_message("Trying alternative approach to find missing records...");
        
        // Get all feedback data IDs
        $feedbackQuery = "SELECT id FROM feedback_data WHERE data_id = :data_id";
        $feedbackStmt = $conn->prepare($feedbackQuery);
        $feedbackStmt->bindParam(':data_id', $data_id);
        $feedbackStmt->execute();
        $feedbackIds = $feedbackStmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Get all analyzed comments with their feedback_data
        $analyzedQuery = "
            SELECT fd.id
            FROM feedback_data fd
            INNER JOIN analyzed_comments ac ON fd.feedback_data = ac.comment AND fd.data_id = ac.data_id
            WHERE fd.data_id = :data_id
        ";
        $analyzedStmt = $conn->prepare($analyzedQuery);
        $analyzedStmt->bindParam(':data_id', $data_id);
        $analyzedStmt->execute();
        $analyzedIds = $analyzedStmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Find the difference
        $missingIds = array_diff($feedbackIds, $analyzedIds);
        
        log_message("Found " . count($missingIds) . " missing records using ID comparison");
        
        if (count($missingIds) > 0) {
            // Get the details for these missing records
            $idsStr = implode(',', array_slice($missingIds, 0, 1000)); // Limit to 1000 records
            
            $detailsQuery = "SELECT * FROM feedback_data WHERE id IN ($idsStr)";
            $detailsStmt = $conn->prepare($detailsQuery);
            $detailsStmt->execute();
            $missingRecords = $detailsStmt->fetchAll(PDO::FETCH_ASSOC);
            
            log_message("Retrieved details for " . count($missingRecords) . " missing records");
        }
    }
    
    // Process missing records
    if (count($missingRecords) > 0) {
        log_message("Processing " . count($missingRecords) . " missing records...");
        
        $processed = 0;
        $failed = 0;
        
        foreach ($missingRecords as $record) {
            log_message("Processing record ID: " . $record['id']);
            
            // Check if this record is already in the queue
            $checkQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = :data_id AND comment = :comment";
            $checkStmt = $conn->prepare($checkQuery);
            $checkStmt->bindParam(':data_id', $data_id);
            $checkStmt->bindParam(':comment', $record['feedback_data']);
            $checkStmt->execute();
            $checkResult = $checkStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($checkResult['count'] > 0) {
                log_message("Record ID: " . $record['id'] . " already in queue");
                continue;
            }
            
            // Try to insert into comment_queue
            try {
                $insertQuery = "INSERT INTO comment_queue (
                    comment, data_id, user_id, csat, nps, pid, status
                ) VALUES (
                    :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending'
                )";
                
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bindParam(':comment', $record['feedback_data']);
                $insertStmt->bindParam(':data_id', $record['data_id']);
                $insertStmt->bindParam(':user_id', $record['user_id']);
                $insertStmt->bindParam(':csat', $record['csat']);
                $insertStmt->bindParam(':nps', $record['nps']);
                $insertStmt->bindParam(':pid', $record['pid']);
                
                $result = $insertStmt->execute();
                
                if ($result) {
                    $processed++;
                    log_message("Successfully enqueued record ID: " . $record['id']);
                } else {
                    $failed++;
                    log_message("Failed to enqueue record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
                    
                    // Try direct insertion as fallback
                    try {
                        log_message("Attempting direct insertion for record ID: " . $record['id']);
                        
                        $directQuery = "INSERT INTO analyzed_comments (
                            comment, data_id, user_id, csat, nps, pid,
                            main_driver, sub_driver, sentiment
                        ) VALUES (
                            :comment, :data_id, :user_id, :csat, :nps, :pid,
                            'Auto-Generated', 'Auto-Generated', 'Neutral'
                        )";
                        
                        $directStmt = $conn->prepare($directQuery);
                        $directStmt->bindParam(':comment', $record['feedback_data']);
                        $directStmt->bindParam(':data_id', $record['data_id']);
                        $directStmt->bindParam(':user_id', $record['user_id']);
                        $directStmt->bindParam(':csat', $record['csat']);
                        $directStmt->bindParam(':nps', $record['nps']);
                        $directStmt->bindParam(':pid', $record['pid']);
                        
                        $directResult = $directStmt->execute();
                        
                        if ($directResult) {
                            $processed++;
                            log_message("Successfully inserted record ID: " . $record['id'] . " directly");
                        } else {
                            log_message("Failed to insert record ID: " . $record['id'] . " directly - Error: " . json_encode($directStmt->errorInfo()));
                        }
                    } catch (PDOException $e) {
                        log_message("ERROR: Failed to insert record ID: " . $record['id'] . " directly - " . $e->getMessage());
                    }
                }
            } catch (PDOException $e) {
                $failed++;
                log_message("ERROR: Failed to enqueue record ID: " . $record['id'] . " - " . $e->getMessage());
                
                // Try direct insertion as fallback
                try {
                    log_message("Attempting direct insertion for record ID: " . $record['id'] . " after exception");
                    
                    $directQuery = "INSERT INTO analyzed_comments (
                        comment, data_id, user_id, csat, nps, pid,
                        main_driver, sub_driver, sentiment
                    ) VALUES (
                        :comment, :data_id, :user_id, :csat, :nps, :pid,
                        'Auto-Generated', 'Auto-Generated', 'Neutral'
                    )";
                    
                    $directStmt = $conn->prepare($directQuery);
                    $directStmt->bindParam(':comment', $record['feedback_data']);
                    $directStmt->bindParam(':data_id', $record['data_id']);
                    $directStmt->bindParam(':user_id', $record['user_id']);
                    $directStmt->bindParam(':csat', $record['csat']);
                    $directStmt->bindParam(':nps', $record['nps']);
                    $directStmt->bindParam(':pid', $record['pid']);
                    
                    $directResult = $directStmt->execute();
                    
                    if ($directResult) {
                        $processed++;
                        log_message("Successfully inserted record ID: " . $record['id'] . " directly after exception");
                    } else {
                        log_message("Failed to insert record ID: " . $record['id'] . " directly after exception - Error: " . json_encode($directStmt->errorInfo()));
                    }
                } catch (PDOException $e2) {
                    log_message("ERROR: Failed to insert record ID: " . $record['id'] . " directly after exception - " . $e2->getMessage());
                }
            }
        }
        
        log_message("Processing completed - Processed: $processed, Failed: $failed");
    } else {
        log_message("No missing records found to process");
    }
    
    log_message("Improved record monitor completed");
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
