-- Add new columns to feedback_data table
ALTER TABLE feedback_data 
ADD COLUMN resolution_comment TEXT,
ADD COLUMN internal_scores VARCHAR(50),
ADD COLUMN feedback_submit_date DATETIME,
ADD COLUMN feedback_month VARCHAR(10),
ADD COLUMN feedback_time VARCHAR(10),
ADD COLUMN lob VARCHAR(100),
ADD COLUMN vendor VARCHAR(100),
ADD COLUMN location VARCHAR(100),
ADD COLUMN partner VARCHAR(100),
ADD COLUMN dummy_1 VARCHAR(100),
ADD COLUMN dummy_2 VARCHAR(100),
ADD COLUMN dummy_3 VARCHAR(100),
ADD COLUMN dummy_4 VARCHAR(100),
ADD COLUMN dummy_5 VARCHAR(100),
ADD COLUMN domain_category VARCHAR(100);

-- Create domain_categories table
CREATE TABLE IF NOT EXISTS domain_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    domain_category VARCHAR(100) NOT NULL,
    main_driver VARCHAR(100) NOT NULL,
    UNIQUE KEY unique_domain_driver (domain_category, main_driver)
);

-- Insert domain categories and main drivers
INSERT INTO domain_categories (domain_category, main_driver) VALUES
('Collections', 'Billing & Payment Issues'),
('Collections', 'Customer Service & Resolution Issues'),
('Collections', 'Customer Support & Service'),
('Collections', 'Policy & Procedures'),
('Collections', 'Tools & Technology'),
('Collections', 'Transfer & Process Issues'),
('Customer Service', 'Agent Behavior'),
('Customer Service', 'Call Quality Issues'),
('Customer Service', 'Communication Clarity'),
('Customer Service', 'First Call Resolution'),
('Customer Service', 'Knowledge Management'),
('Customer Service', 'Resolution Efficiency'),
('Finance & Banking', 'Account Access Issues'),
('Finance & Banking', 'Account Application'),
('Finance & Banking', 'Billing & Payment Issues'),
('Finance & Banking', 'Customer Service & Resolution Issues'),
('Finance & Banking', 'Dispute Handling'),
('Finance & Banking', 'Loan & Credit Issues'),
('Finance & Banking', 'Policy & Procedures'),
('Finance & Banking', 'Refund Delays'),
('Finance & Banking', 'Refund Policies & Eligibility'),
('Finance & Banking', 'Tools & Technology'),
('Finance & Banking', 'Transaction Errors'),
('Finance & Banking', 'Verification Issues'),
('Retail & E-commerce', 'Customer Service'),
('Retail & E-commerce', 'Delivery Experience'),
('Retail & E-commerce', 'Order Fulfillment'),
('Retail & E-commerce', 'Product Quality'),
('Retail & E-commerce', 'Returns & Refunds'),
('Retail & E-commerce', 'Website Experience'),
('Sales & Marketing', 'Brand Representation'),
('Sales & Marketing', 'Customer Service & Resolution Issues'),
('Sales & Marketing', 'Customer Support & Service'),
('Sales & Marketing', 'Follow-up Communication'),
('Sales & Marketing', 'Lack of Clear Communication'),
('Sales & Marketing', 'Lead Handling'),
('Sales & Marketing', 'Marketing'),
('Sales & Marketing', 'Perceived Company Integrity'),
('Sales & Marketing', 'Product Knowledge Gaps'),
('Sales & Marketing', 'Promotions & Discounts'),
('Technical Support', 'Hardware Issues'),
('Technical Support', 'Self-Service Portal Issues'),
('Technical Support', 'Software Problems'),
('Technical Support', 'System Downtime'),
('Technical Support', 'Tool Integration'),
('Technical Support', 'User Accessibility');
