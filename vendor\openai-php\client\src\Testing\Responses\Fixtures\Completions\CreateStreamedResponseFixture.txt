data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": " everyone", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": "!", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": "\n", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": "\n", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": "This", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": " is", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": " a", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": " fake", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": " completion", "index": 0, "logprobs": null, "finish_reason": null}], "model": "gpt-3.5-turbo-instruct"}
data: {"id": "cmpl-6ynJi2uZZnKntnEZreDcjGyoPbVAn", "object": "text_completion", "created": 1679430847, "choices": [{"text": " response.", "index": 0, "logprobs": null, "finish_reason": "length"}], "model": "gpt-3.5-turbo-instruct"}
data: [DONE]
