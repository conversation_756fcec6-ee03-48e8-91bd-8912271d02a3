<?php
/**
 * Database Connection Test Script
 * 
 * This script tests the connection to both the primary and alternate databases
 * and provides detailed error information if the connection fails.
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Import required files
require_once 'DatabaseInteraction.php';

echo "<h1>Database Connection Test</h1>";

// Test the database connection
try {
    $db = new DatabaseInteraction();
    $conn = $db->connect();
    
    if ($conn) {
        echo "<p style='color: green;'>✓ Successfully connected to the database!</p>";
        
        // Test query to check if we can read data
        $query = "SELECT COUNT(*) as count FROM feedback_data WHERE data_id = '682b54974bab6'";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>Found " . $result['count'] . " records for data_id 682b54974bab6 in feedback_data table.</p>";
        
        // Test if we can read from analyzed_comments
        $query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = '682b54974bab6'";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>Found " . $result['count'] . " records for data_id 682b54974bab6 in analyzed_comments table.</p>";
        
        // Test if we can read from comment_queue
        $query = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = '682b54974bab6'";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>Found " . $result['count'] . " records for data_id 682b54974bab6 in comment_queue table.</p>";
        
        // Test if we can write to comment_queue
        echo "<p>Testing write permission to comment_queue table...</p>";
        
        try {
            // Begin transaction so we can rollback
            $conn->beginTransaction();
            
            $query = "INSERT INTO comment_queue (comment, data_id, user_id, csat, nps, pid, status) 
                      VALUES ('TEST_COMMENT', '682b54974bab6', 1, 0, 0, 0, 'pending')";
            $stmt = $conn->prepare($query);
            $result = $stmt->execute();
            
            if ($result) {
                echo "<p style='color: green;'>✓ Successfully wrote test record to comment_queue table!</p>";
                
                // Get the ID of the inserted record
                $id = $conn->lastInsertId();
                
                // Delete the test record
                $query = "DELETE FROM comment_queue WHERE id = :id";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':id', $id);
                $stmt->execute();
                
                echo "<p>Test record deleted.</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to write test record to comment_queue table!</p>";
            }
            
            // Rollback transaction to ensure no test data remains
            $conn->rollBack();
            
        } catch (PDOException $e) {
            $conn->rollBack();
            echo "<p style='color: red;'>✗ Error testing write permission: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Failed to connect to the database!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

// Display environment variables (with password masked)
echo "<h2>Environment Variables</h2>";
echo "<pre>";
echo "DB_HOST_ALT: " . (isset($_ENV['DB_HOST_ALT']) ? $_ENV['DB_HOST_ALT'] : 'Not set') . "\n";
echo "DB_NAME_ALT: " . (isset($_ENV['DB_NAME_ALT']) ? $_ENV['DB_NAME_ALT'] : 'Not set') . "\n";
echo "DB_USER_ALT: " . (isset($_ENV['DB_USER_ALT']) ? $_ENV['DB_USER_ALT'] : 'Not set') . "\n";
echo "DB_PASS_ALT: " . (isset($_ENV['DB_PASS_ALT']) ? '********' : 'Not set') . "\n";
echo "</pre>";

// Display PHP info
echo "<h2>PHP Configuration</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>PDO Drivers: " . implode(', ', PDO::getAvailableDrivers()) . "</p>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . " seconds</p>";
?>
