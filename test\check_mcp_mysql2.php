<?php
// <PERSON><PERSON><PERSON> to check MCP-MySQL-2 server connection and data

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Testing connection to MCP-MySQL-2 server...\n";

// Try to connect using the alternate database credentials from .ebextensions
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    // Connect to the database
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Successfully connected to MCP-MySQL-2 server!\n";
    
    // Check if the analyzed_comments table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'analyzed_comments'");
    if ($stmt->rowCount() > 0) {
        echo "The analyzed_comments table exists.\n";
        
        // Check the structure of the analyzed_comments table
        $stmt = $conn->query("DESCRIBE analyzed_comments");
        echo "\nStructure of analyzed_comments table:\n";
        echo "------------------------------------\n";
        echo "Field | Type | Null | Key | Default | Extra\n";
        echo "------------------------------------\n";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "{$row['Field']} | {$row['Type']} | {$row['Null']} | {$row['Key']} | {$row['Default']} | {$row['Extra']}\n";
        }
        
        // Check if there are any records in the analyzed_comments table
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "\nTotal records in analyzed_comments table: {$count}\n";
        
        // Check if there are any records with non-empty LOB, Vendor, Location, and Partner fields
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE lob IS NOT NULL AND lob != ''");
        $lob_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with non-empty LOB: {$lob_count}\n";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE vendor IS NOT NULL AND vendor != ''");
        $vendor_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with non-empty Vendor: {$vendor_count}\n";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE location IS NOT NULL AND location != ''");
        $location_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with non-empty Location: {$location_count}\n";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE partner IS NOT NULL AND partner != ''");
        $partner_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with non-empty Partner: {$partner_count}\n";
        
        // Check if there are any records with user_id = 2
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = 2");
        $user_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with user_id = 2: {$user_count}\n";
        
        // Check if there are any records with user_id = 2 and non-empty LOB, Vendor, Location, and Partner fields
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = 2 AND lob IS NOT NULL AND lob != ''");
        $user_lob_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with user_id = 2 and non-empty LOB: {$user_lob_count}\n";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = 2 AND vendor IS NOT NULL AND vendor != ''");
        $user_vendor_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with user_id = 2 and non-empty Vendor: {$user_vendor_count}\n";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = 2 AND location IS NOT NULL AND location != ''");
        $user_location_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with user_id = 2 and non-empty Location: {$user_location_count}\n";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = 2 AND partner IS NOT NULL AND partner != ''");
        $user_partner_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "Records with user_id = 2 and non-empty Partner: {$user_partner_count}\n";
        
        // Sample some LOB, Vendor, Location, and Partner values
        echo "\nSample LOB values:\n";
        $stmt = $conn->query("SELECT DISTINCT lob FROM analyzed_comments WHERE lob IS NOT NULL AND lob != '' LIMIT 10");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['lob']}\n";
        }
        
        echo "\nSample Vendor values:\n";
        $stmt = $conn->query("SELECT DISTINCT vendor FROM analyzed_comments WHERE vendor IS NOT NULL AND vendor != '' LIMIT 10");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['vendor']}\n";
        }
        
        echo "\nSample Location values:\n";
        $stmt = $conn->query("SELECT DISTINCT location FROM analyzed_comments WHERE location IS NOT NULL AND location != '' LIMIT 10");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['location']}\n";
        }
        
        echo "\nSample Partner values:\n";
        $stmt = $conn->query("SELECT DISTINCT partner FROM analyzed_comments WHERE partner IS NOT NULL AND partner != '' LIMIT 10");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['partner']}\n";
        }
    } else {
        echo "The analyzed_comments table does not exist.\n";
    }
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
