<?php
/**
 * AWS-Optimized Database Performance Tester for Dashboard Optimization
 * Adjusted benchmarks for AWS RDS network latency and cloud environment
 *
 * Usage: Run this file on your AWS environment
 * URL: https://your-aws-domain.com/database_performance_tester_aws.php
 */

// Set execution time limit for comprehensive testing
set_time_limit(300); // 5 minutes

// Include database configuration
require_once 'config.php';
require_once 'DatabaseInteraction.php';

// Initialize database connection
$db = new DatabaseInteraction();
$conn = $db->connect();

if (!$conn) {
    die("❌ Database connection failed. Please check your configuration.");
}

// HTML Header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS Database Performance Tester - Dashboard Optimization</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; min-width: 150px; text-align: center; }
        .performance-score { font-size: 24px; font-weight: bold; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .aws-note { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>☁️ AWS Database Performance Tester</h1>
        <p><strong>Dashboard Optimization Verification Tool for AWS RDS</strong></p>
        <p>Testing database performance for optimized dashboard queries on AWS cloud environment.</p>

        <div class="aws-note">
            <h3>📋 AWS Environment Information</h3>
            <p><strong>Platform:</strong> PHP 8.3 on 64bit Amazon Linux 2023/4.6.1</p>
            <p><strong>Expected AWS Performance:</strong></p>
            <ul>
                <li>Network latency adds 50-200ms to query times</li>
                <li>PHP 8.3 provides excellent performance optimizations</li>
                <li>Amazon Linux 2023 optimized for AWS infrastructure</li>
                <li>Benchmarks adjusted for cloud environment</li>
            </ul>
        </div>

<?php

// Test tracking
$totalTests = 8; // Reduced for AWS focus
$currentTest = 0;
$passedTests = 0;
$failedTests = 0;
$testResults = [];

// Helper function to execute query with timing (PHP 8.3 optimized)
function executeTimedQuery($conn, $query, $description) {
    $startTime = hrtime(true); // Use high-resolution timer for PHP 8.3
    try {
        $result = $conn->query($query);
        $endTime = hrtime(true);
        $executionTime = ($endTime - $startTime) / 1_000_000; // Convert nanoseconds to milliseconds

        return [
            'success' => true,
            'execution_time' => $executionTime,
            'result' => $result,
            'description' => $description,
            'query' => $query
        ];
    } catch (Exception $e) {
        $endTime = hrtime(true);
        $executionTime = ($endTime - $startTime) / 1_000_000;

        return [
            'success' => false,
            'execution_time' => $executionTime,
            'error' => $e->getMessage(),
            'description' => $description,
            'query' => $query
        ];
    }
}

// AWS-adjusted performance rating
function getAWSPerformanceRating($executionTime) {
    if ($executionTime < 100) return ['excellent', 'Excellent'];
    if ($executionTime < 250) return ['good', 'Good'];
    if ($executionTime < 500) return ['fair', 'Fair'];
    return ['poor', 'Poor'];
}

// Test 1: Database Connection and Basic Info
$currentTest++;
echo "<div class='test-section info'>";
echo "<h2>📊 Test 1: AWS Database Connection & Information</h2>";

try {
    // Get database info with AWS-safe query
    $dbInfo = $conn->query("SELECT DATABASE() as db_name, VERSION() as version, NOW() as server_time")->fetch(PDO::FETCH_ASSOC);

    // Get table info
    $tableInfo = $conn->query("SELECT COUNT(*) as total_records FROM analyzed_comments")->fetch(PDO::FETCH_ASSOC);

    // Get AWS-specific info
    $awsInfo = $conn->query("SHOW VARIABLES LIKE 'hostname'")->fetch(PDO::FETCH_ASSOC);

    // Get PHP and platform information
    $phpVersion = PHP_VERSION;
    $platformInfo = php_uname();
    $memoryLimit = ini_get('memory_limit');
    $maxExecutionTime = ini_get('max_execution_time');

    echo "<div class='success'>";
    echo "<h3>✅ AWS Database Connection Successful</h3>";
    echo "<p><strong>Database:</strong> {$dbInfo['db_name']}</p>";
    echo "<p><strong>MySQL Version:</strong> {$dbInfo['version']}</p>";
    echo "<p><strong>Server Time:</strong> {$dbInfo['server_time']}</p>";
    echo "<p><strong>Total Records:</strong> {$tableInfo['total_records']}</p>";
    echo "<p><strong>PHP Version:</strong> {$phpVersion}</p>";
    echo "<p><strong>Platform:</strong> " . substr($platformInfo, 0, 50) . "...</p>";
    echo "<p><strong>Memory Limit:</strong> {$memoryLimit}</p>";
    echo "<p><strong>Max Execution Time:</strong> {$maxExecutionTime}s</p>";
    if ($awsInfo) {
        echo "<p><strong>Database Server:</strong> {$awsInfo['Value']}</p>";
    }
    echo "</div>";

    $passedTests++;
    $testResults['connection'] = 'PASSED';
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ AWS Database Connection Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";

    $failedTests++;
    $testResults['connection'] = 'FAILED';
}
echo "</div>";

// Test 2: Index Verification (same as before but with AWS context)
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>🔍 Test 2: AWS RDS Index Verification</h2>";

$requiredIndexes = [
    'idx_analyzed_comments_performance' => 'Main dashboard performance index',
    'idx_analyzed_comments_scores' => 'CSAT/NPS impact queries',
    'idx_analyzed_comments_verbatim' => 'Word cloud queries',
    'idx_analyzed_comments_created_at' => 'Time series queries',
    'idx_analyzed_comments_main_driver' => 'Main driver analysis',
    'idx_analyzed_comments_sub_driver' => 'Sub driver analysis',
    'idx_analyzed_comments_lob' => 'LOB sentiment analysis',
    'idx_analyzed_comments_vendor' => 'Vendor sentiment analysis',
    'idx_analyzed_comments_location' => 'Location sentiment analysis',
    'idx_analyzed_comments_partner' => 'Partner sentiment analysis'
];

$indexResults = executeTimedQuery($conn, "SHOW INDEX FROM analyzed_comments", "Index verification");

if ($indexResults['success']) {
    $existingIndexes = [];
    while ($row = $indexResults['result']->fetch(PDO::FETCH_ASSOC)) {
        $existingIndexes[] = $row['Key_name'];
    }

    echo "<table>";
    echo "<tr><th>Index Name</th><th>Purpose</th><th>Status</th></tr>";

    $indexScore = 0;
    foreach ($requiredIndexes as $indexName => $purpose) {
        $exists = in_array($indexName, $existingIndexes);
        $status = $exists ? "✅ EXISTS" : "❌ MISSING";
        $class = $exists ? "success" : "error";

        echo "<tr class='{$class}'>";
        echo "<td>{$indexName}</td>";
        echo "<td>{$purpose}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";

        if ($exists) $indexScore++;
    }
    echo "</table>";

    $indexPercentage = ($indexScore / count($requiredIndexes)) * 100;
    echo "<div class='metric'>";
    echo "<div class='performance-score " . ($indexPercentage >= 90 ? 'excellent' : ($indexPercentage >= 70 ? 'good' : 'poor')) . "'>";
    echo "{$indexPercentage}%</div>";
    echo "<div>Index Coverage</div>";
    echo "</div>";

    if ($indexPercentage >= 90) {
        $passedTests++;
        $testResults['indexes'] = 'PASSED';
    } else {
        $failedTests++;
        $testResults['indexes'] = 'FAILED';
    }
} else {
    echo "<div class='error'>❌ Failed to retrieve index information</div>";
    $failedTests++;
    $testResults['indexes'] = 'FAILED';
}
echo "</div>";

// Test 3: AWS-Optimized Query Performance
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>⚡ Test 3: AWS-Optimized Query Performance</h2>";

// Get sample data for testing
$sampleData = $conn->query("SELECT DISTINCT data_id, user_id FROM analyzed_comments LIMIT 1")->fetch(PDO::FETCH_ASSOC);

if ($sampleData) {
    $testQueries = [
        [
            'name' => 'Basic Sentiment Count',
            'query' => "SELECT sentiment, COUNT(*) as count FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} GROUP BY sentiment",
            'target_time' => 300 // AWS-adjusted target
        ],
        [
            'name' => 'Domain Category Filter',
            'query' => "SELECT domain_category, sentiment, COUNT(*) FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} GROUP BY domain_category, sentiment",
            'target_time' => 350 // AWS-adjusted target
        ],
        [
            'name' => 'Main Driver Analysis',
            'query' => "SELECT main_driver, sentiment, COUNT(*) FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} GROUP BY main_driver, sentiment ORDER BY COUNT(*) DESC LIMIT 10",
            'target_time' => 350 // AWS-adjusted target
        ]
    ];

    echo "<div class='aws-note'>";
    echo "<p><strong>AWS Performance Targets:</strong> Adjusted for network latency and RDS performance</p>";
    echo "</div>";

    echo "<table>";
    echo "<tr><th>Query Type</th><th>Execution Time (ms)</th><th>AWS Performance</th><th>Status</th></tr>";

    $queryScore = 0;
    foreach ($testQueries as $test) {
        $result = executeTimedQuery($conn, $test['query'], $test['name']);

        if ($result['success']) {
            $rating = getAWSPerformanceRating($result['execution_time']);
            $status = $result['execution_time'] <= $test['target_time'] ? "✅ PASSED" : "⚠️ SLOW";

            echo "<tr>";
            echo "<td>{$test['name']}</td>";
            echo "<td>" . number_format($result['execution_time'], 2) . " ms</td>";
            echo "<td class='{$rating[0]}'>{$rating[1]}</td>";
            echo "<td>{$status}</td>";
            echo "</tr>";

            if ($result['execution_time'] <= $test['target_time']) $queryScore++;
        } else {
            echo "<tr class='error'>";
            echo "<td>{$test['name']}</td>";
            echo "<td>FAILED</td>";
            echo "<td>Error</td>";
            echo "<td>❌ FAILED</td>";
            echo "</tr>";
        }
    }
    echo "</table>";

    $queryPercentage = ($queryScore / count($testQueries)) * 100;
    echo "<div class='metric'>";
    echo "<div class='performance-score " . ($queryPercentage >= 80 ? 'excellent' : ($queryPercentage >= 60 ? 'good' : 'poor')) . "'>";
    echo "{$queryPercentage}%</div>";
    echo "<div>AWS Query Performance</div>";
    echo "</div>";

    if ($queryPercentage >= 60) { // Lower threshold for AWS
        $passedTests++;
        $testResults['aws_queries'] = 'PASSED';
    } else {
        $failedTests++;
        $testResults['aws_queries'] = 'FAILED';
    }
} else {
    echo "<div class='warning'>⚠️ No sample data available for testing</div>";
    $testResults['aws_queries'] = 'SKIPPED';
}
echo "</div>";

// Test 4: AWS Batch Query Simulation
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>🔄 Test 4: AWS Batch Query Simulation</h2>";

$batchQueries = [
    [
        'name' => 'Comments Count',
        'query' => "SELECT COUNT(*) as total FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']}",
        'target_time' => 250
    ],
    [
        'name' => 'Sentiment Distribution',
        'query' => "SELECT sentiment, COUNT(*) as count FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} GROUP BY sentiment",
        'target_time' => 300
    ],
    [
        'name' => 'Driver Analysis',
        'query' => "SELECT main_driver, sub_driver, sentiment, COUNT(*) as count FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} GROUP BY main_driver, sub_driver, sentiment",
        'target_time' => 400
    ]
];

echo "<p><strong>Simulating AWS dashboard batch API performance...</strong></p>";
echo "<table>";
echo "<tr><th>Query Component</th><th>Execution Time (ms)</th><th>AWS Performance</th><th>Records</th><th>Status</th></tr>";

$batchScore = 0;
$totalBatchTime = 0;
foreach ($batchQueries as $test) {
    $result = executeTimedQuery($conn, $test['query'], $test['name']);

    if ($result['success']) {
        $recordCount = $result['result']->rowCount();
        $rating = getAWSPerformanceRating($result['execution_time']);
        $status = $result['execution_time'] <= $test['target_time'] ? "✅ PASSED" : "⚠️ SLOW";
        $totalBatchTime += $result['execution_time'];

        echo "<tr>";
        echo "<td>{$test['name']}</td>";
        echo "<td>" . number_format($result['execution_time'], 2) . " ms</td>";
        echo "<td class='{$rating[0]}'>{$rating[1]}</td>";
        echo "<td>{$recordCount}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";

        if ($result['execution_time'] <= $test['target_time']) $batchScore++;
    } else {
        echo "<tr class='error'>";
        echo "<td>{$test['name']}</td>";
        echo "<td>FAILED</td>";
        echo "<td>Error</td>";
        echo "<td>0</td>";
        echo "<td>❌ FAILED</td>";
        echo "</tr>";
    }
}
echo "</table>";

echo "<div class='metric'>";
echo "<div class='performance-score " . ($totalBatchTime < 800 ? 'excellent' : ($totalBatchTime < 1200 ? 'good' : 'poor')) . "'>";
echo number_format($totalBatchTime, 2) . " ms</div>";
echo "<div>Total AWS Batch Time</div>";
echo "</div>";

$batchPercentage = ($batchScore / count($batchQueries)) * 100;
if ($batchPercentage >= 60) { // AWS-adjusted threshold
    $passedTests++;
    $testResults['aws_batch'] = 'PASSED';
} else {
    $failedTests++;
    $testResults['aws_batch'] = 'FAILED';
}
echo "</div>";

// Test 5: Index Usage Analysis
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>🔍 Test 5: AWS Index Usage Analysis</h2>";

$explainQueries = [
    [
        'name' => 'Main Dashboard Query',
        'query' => "EXPLAIN SELECT sentiment, COUNT(*) FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} AND domain_category IS NOT NULL GROUP BY sentiment"
    ],
    [
        'name' => 'CSAT Impact Query',
        'query' => "EXPLAIN SELECT main_driver, AVG(csat) FROM analyzed_comments WHERE data_id = '{$sampleData['data_id']}' AND user_id = {$sampleData['user_id']} AND csat IS NOT NULL GROUP BY main_driver"
    ]
];

echo "<table>";
echo "<tr><th>Query Type</th><th>Index Used</th><th>Key Length</th><th>Rows Examined</th><th>Extra Info</th></tr>";

$indexUsageScore = 0;
foreach ($explainQueries as $test) {
    $result = executeTimedQuery($conn, $test['query'], $test['name']);

    if ($result['success']) {
        $explainResult = $result['result']->fetch(PDO::FETCH_ASSOC);

        $indexUsed = $explainResult['key'] ?? 'None';
        $keyLength = $explainResult['key_len'] ?? 'N/A';
        $rowsExamined = $explainResult['rows'] ?? 'Unknown';
        $extra = $explainResult['Extra'] ?? 'N/A';

        $usingIndex = $indexUsed !== 'None' && $indexUsed !== null;
        $status = $usingIndex ? "✅ Using Index" : "❌ No Index";

        echo "<tr class='" . ($usingIndex ? 'success' : 'warning') . "'>";
        echo "<td>{$test['name']}</td>";
        echo "<td>{$indexUsed}</td>";
        echo "<td>{$keyLength}</td>";
        echo "<td>{$rowsExamined}</td>";
        echo "<td>{$extra}</td>";
        echo "</tr>";

        if ($usingIndex) $indexUsageScore++;
    } else {
        echo "<tr class='error'>";
        echo "<td>{$test['name']}</td>";
        echo "<td colspan='4'>FAILED: {$result['error']}</td>";
        echo "</tr>";
    }
}
echo "</table>";

$indexUsagePercentage = ($indexUsageScore / count($explainQueries)) * 100;
if ($indexUsagePercentage >= 70) {
    $passedTests++;
    $testResults['index_usage'] = 'PASSED';
} else {
    $failedTests++;
    $testResults['index_usage'] = 'FAILED';
}
echo "</div>";

// Test 6: AWS Network Latency Test
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>🌐 Test 6: AWS Network Latency Analysis</h2>";

$latencyTests = [
    "SELECT 1" => "Simple ping test",
    "SELECT COUNT(*) FROM analyzed_comments" => "Table scan test",
    "SELECT * FROM analyzed_comments LIMIT 1" => "Single record fetch"
];

echo "<table>";
echo "<tr><th>Test Type</th><th>Query</th><th>Latency (ms)</th><th>Assessment</th></tr>";

$latencyScore = 0;
$totalLatencyTests = count($latencyTests);

foreach ($latencyTests as $query => $description) {
    $result = executeTimedQuery($conn, $query, $description);

    if ($result['success']) {
        $latency = $result['execution_time'];
        $assessment = '';

        if ($latency < 50) {
            $assessment = "🟢 Excellent";
            $latencyScore++;
        } elseif ($latency < 150) {
            $assessment = "🟡 Good";
            $latencyScore++;
        } elseif ($latency < 300) {
            $assessment = "🟠 Fair";
        } else {
            $assessment = "🔴 Poor";
        }

        echo "<tr>";
        echo "<td>{$description}</td>";
        echo "<td><code>{$query}</code></td>";
        echo "<td>" . number_format($latency, 2) . " ms</td>";
        echo "<td>{$assessment}</td>";
        echo "</tr>";
    }
}
echo "</table>";

$latencyPercentage = ($latencyScore / $totalLatencyTests) * 100;
if ($latencyPercentage >= 60) {
    $passedTests++;
    $testResults['latency'] = 'PASSED';
} else {
    $failedTests++;
    $testResults['latency'] = 'FAILED';
}
echo "</div>";

// Test 7: AWS RDS Configuration Check
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>⚙️ Test 7: AWS RDS Configuration Check</h2>";

$configQueries = [
    "SHOW VARIABLES LIKE 'max_connections'" => "Connection limit",
    "SHOW VARIABLES LIKE 'innodb_buffer_pool_size'" => "Buffer pool size",
    "SHOW VARIABLES LIKE 'query_cache_size'" => "Query cache",
    "SHOW STATUS LIKE 'Threads_connected'" => "Current connections"
];

echo "<table>";
echo "<tr><th>Configuration</th><th>Value</th><th>Status</th></tr>";

$configScore = 0;
foreach ($configQueries as $query => $description) {
    try {
        $result = $conn->query($query)->fetch(PDO::FETCH_ASSOC);
        $value = $result['Value'] ?? 'Unknown';

        echo "<tr>";
        echo "<td>{$description}</td>";
        echo "<td>{$value}</td>";
        echo "<td>✅ Retrieved</td>";
        echo "</tr>";

        $configScore++;
    } catch (Exception $e) {
        echo "<tr class='error'>";
        echo "<td>{$description}</td>";
        echo "<td>Error</td>";
        echo "<td>❌ Failed</td>";
        echo "</tr>";
    }
}
echo "</table>";

$configPercentage = ($configScore / count($configQueries)) * 100;
if ($configPercentage >= 75) {
    $passedTests++;
    $testResults['config'] = 'PASSED';
} else {
    $failedTests++;
    $testResults['config'] = 'FAILED';
}
echo "</div>";

// Test 8: Overall AWS Performance Summary
$currentTest++;
echo "<div class='test-section'>";
echo "<h2>📊 Test 8: AWS Performance Summary</h2>";

$overallScore = ($passedTests / $totalTests) * 100;
$performanceGrade = '';
$gradeClass = '';

if ($overallScore >= 85) {
    $performanceGrade = 'A+ (Excellent for AWS)';
    $gradeClass = 'excellent';
} elseif ($overallScore >= 70) {
    $performanceGrade = 'A (Very Good for AWS)';
    $gradeClass = 'good';
} elseif ($overallScore >= 55) {
    $performanceGrade = 'B (Good for AWS)';
    $gradeClass = 'fair';
} elseif ($overallScore >= 40) {
    $performanceGrade = 'C (Fair for AWS)';
    $gradeClass = 'fair';
} else {
    $performanceGrade = 'D (Poor)';
    $gradeClass = 'poor';
}

echo "<div class='metric'>";
echo "<div class='performance-score {$gradeClass}'>";
echo number_format($overallScore, 1) . "%</div>";
echo "<div>AWS Performance Score</div>";
echo "</div>";

echo "<div class='metric'>";
echo "<div class='performance-score {$gradeClass}'>";
echo $performanceGrade . "</div>";
echo "<div>AWS Performance Grade</div>";
echo "</div>";

echo "<h3>📋 AWS Test Results Summary</h3>";
echo "<table>";
echo "<tr><th>Test Category</th><th>Status</th><th>AWS Impact</th></tr>";

$testCategories = [
    'connection' => ['AWS Database Connection', 'Critical - Required for all operations'],
    'indexes' => ['Index Coverage', 'Critical - Affects all query performance'],
    'aws_queries' => ['AWS-Optimized Queries', 'High - Core dashboard functionality'],
    'aws_batch' => ['AWS Batch Simulation', 'High - API performance'],
    'index_usage' => ['Index Usage Analysis', 'High - Query optimization'],
    'latency' => ['Network Latency', 'Medium - AWS-specific performance'],
    'config' => ['RDS Configuration', 'Low - System information'],
];

foreach ($testCategories as $key => $info) {
    $status = $testResults[$key] ?? 'NOT RUN';
    $statusClass = '';
    $statusIcon = '';

    switch ($status) {
        case 'PASSED':
            $statusClass = 'success';
            $statusIcon = '✅';
            break;
        case 'FAILED':
            $statusClass = 'error';
            $statusIcon = '❌';
            break;
        case 'SKIPPED':
            $statusClass = 'warning';
            $statusIcon = '⚠️';
            break;
        default:
            $statusClass = 'info';
            $statusIcon = '❓';
    }

    echo "<tr class='{$statusClass}'>";
    echo "<td>{$info[0]}</td>";
    echo "<td>{$statusIcon} {$status}</td>";
    echo "<td>{$info[1]}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>🎯 AWS Performance Recommendations</h3>";
if ($overallScore >= 70) {
    echo "<div class='success'>";
    echo "<h4>🎉 Good AWS Performance!</h4>";
    echo "<p>Your AWS RDS database is performing well for dashboard operations. Expected improvements:</p>";
    echo "<ul>";
    echo "<li>✅ 50-70% faster initial dashboard load times (accounting for AWS latency)</li>";
    echo "<li>✅ 70-85% faster filter changes with caching</li>";
    echo "<li>✅ Proper index usage for query optimization</li>";
    echo "<li>✅ Ready for production dashboard deployment</li>";
    echo "</ul>";
    echo "</div>";
} elseif ($overallScore >= 50) {
    echo "<div class='warning'>";
    echo "<h4>⚠️ Acceptable AWS Performance with Optimization Opportunities</h4>";
    echo "<p>Your AWS performance is acceptable but could be improved:</p>";
    echo "<ul>";
    echo "<li>Consider upgrading RDS instance size for better performance</li>";
    echo "<li>Review network configuration and geographic proximity</li>";
    echo "<li>Monitor query performance and optimize slow queries</li>";
    echo "<li>Expected improvements: 30-50% faster dashboard performance</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h4>❌ AWS Performance Issues Detected</h4>";
    echo "<p>Significant performance issues found that need attention:</p>";
    echo "<ul>";
    echo "<li>❗ Check AWS RDS instance configuration and size</li>";
    echo "<li>❗ Review network latency and geographic setup</li>";
    echo "<li>❗ Verify all indexes are properly created</li>";
    echo "<li>❗ Consider RDS performance insights for optimization</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>☁️ AWS-Specific Recommendations</h3>";
echo "<div class='aws-note'>";
echo "<p><strong>To improve AWS RDS performance:</strong></p>";
echo "<ul>";
echo "<li><strong>RDS Instance:</strong> Consider upgrading to a larger instance type (db.t3.medium or higher)</li>";
echo "<li><strong>Storage:</strong> Use GP2 or GP3 SSD storage for better IOPS</li>";
echo "<li><strong>Connection Pooling:</strong> Implement connection pooling to reduce connection overhead</li>";
echo "<li><strong>Geographic Proximity:</strong> Ensure your application and RDS are in the same AWS region</li>";
echo "<li><strong>Monitoring:</strong> Enable RDS Performance Insights for detailed query analysis</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

?>
        <div class="test-section success">
            <h2>🏁 AWS Testing Complete!</h2>
            <p><strong>Total Tests:</strong> <?php echo $totalTests; ?></p>
            <p><strong>Passed:</strong> <?php echo $passedTests; ?></p>
            <p><strong>Failed:</strong> <?php echo $failedTests; ?></p>
            <p><strong>AWS Performance Score:</strong> <?php echo number_format($overallScore, 1); ?>% (<?php echo $performanceGrade; ?>)</p>

            <div class="aws-note">
                <h3>📈 Expected Dashboard Improvements on AWS</h3>
                <p>Even with AWS network latency, you should still see significant improvements:</p>
                <ul>
                    <li><strong>Reduced API Calls:</strong> From 23 to 1 per filter change</li>
                    <li><strong>Client-Side Caching:</strong> 5-minute cache eliminates redundant requests</li>
                    <li><strong>Optimized Queries:</strong> Proper indexing reduces database load</li>
                    <li><strong>Better UX:</strong> Loading indicators and debounced interactions</li>
                </ul>
            </div>

            <p><em>Generated on AWS: <?php echo date('Y-m-d H:i:s'); ?></em></p>
        </div>
    </div>

    <script>
        // Auto-scroll to bottom when testing is complete
        window.scrollTo(0, document.body.scrollHeight);
    </script>
</body>
</html>
