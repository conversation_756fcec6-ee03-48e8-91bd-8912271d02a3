<?php
class PythonAnalysis {
    private $pythonScript;
    
    public function __construct() {
        $this->pythonScript = __DIR__ . '/ols_analysis.py';
    }
    
    public function runAnalysis($params, $metric = 'csat', $user_id = null, $darkMode = false) {
        $input = array_merge($params, [
            'metric' => $metric,
            'user_id' => $user_id,
            'dark_mode' => $darkMode // Pass dark mode status to Python
        ]);
        $inputJson = json_encode($input);
        
        $descriptors = [
            0 => ['pipe', 'r'],  // stdin
            1 => ['pipe', 'w'],  // stdout
            2 => ['pipe', 'w']   // stderr
        ];
        
        // Determine the correct Python executable based on environment
        $pythonExecutable = 'python3.12';
        // Check if python3.12 is available, otherwise fall back to python
        // This check is for local Windows environments where python3.12 might not be directly in PATH
        // On Linux (AWS), python3.12 is expected to be available.
        $checkCommand = (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') ? 'where python3.12' : 'command -v python3.12';
        $output = shell_exec($checkCommand);
        if (empty($output) || strpos($output, 'not found') !== false || strpos($output, 'Could not find') !== false) {
            $pythonExecutable = 'python';
        }

        $command = "{$pythonExecutable} {$this->pythonScript} analyze";
        $process = proc_open($command, $descriptors, $pipes);
        
        if (!is_resource($process)) {
            throw new Exception("Failed to start Python process");
        }
        
        // Send input to Python
        fwrite($pipes[0], $inputJson);
        fclose($pipes[0]);
        
        // Read output
        $output = stream_get_contents($pipes[1]);
        $error = stream_get_contents($pipes[2]);
        
        fclose($pipes[1]);
        fclose($pipes[2]);
        
        $returnCode = proc_close($process);
        
        if ($returnCode !== 0) {
            throw new Exception("Python script failed: " . $error);
        }
        
        return json_decode($output, true);
    }
}
?>