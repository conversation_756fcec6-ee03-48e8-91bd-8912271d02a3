<?php
// Test script for the specific MCP query
// Query: SELECT * from analyzed_comments WHERE data_id ='686793923253e'

ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Testing MCP Query: SELECT * from analyzed_comments WHERE data_id ='686793923253e'\n";
echo "==============================================================\n";

$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $data_id = '686793923253e';
    
    // Count records
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = :data_id");
    $stmt->bindParam(':data_id', $data_id);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "✅ RESULT: Found {$count} record(s) for data_id '{$data_id}'\n\n";
    
    if ($count > 0) {
        // Show all records
        $stmt = $conn->prepare("SELECT * FROM analyzed_comments WHERE data_id = :data_id");
        $stmt->bindParam(':data_id', $data_id);
        $stmt->execute();
        
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($records as $index => $record) {
            echo "Record #" . ($index + 1) . ":\n";
            echo "  ID: {$record['id']}\n";
            echo "  Sentiment: {$record['sentiment']}\n";
            echo "  Main Driver: {$record['main_driver']}\n";
            echo "  CSAT: {$record['csat']}, NPS: {$record['nps']}\n";
            echo "  Created: {$record['created_at']}\n";
            echo "  Comment: " . substr($record['comment'], 0, 150) . "...\n";
            echo "  LOB: {$record['lob']}, Vendor: {$record['vendor']}\n";
            echo "  Location: {$record['location']}, Partner: {$record['partner']}\n";
            echo "  ----------------------------------------\n";
        }
    } else {
        echo "❌ No records found for data_id '{$data_id}'\n";
        echo "This means either:\n";
        echo "1. The data_id doesn't exist in the database\n";
        echo "2. There might be a typo in the data_id\n";
        echo "3. The records haven't been processed yet\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\nMCP Query Test Completed.\n";
?> 