<?php
session_start();

// Redirect to login if username session not set
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}
require_once 'DatabaseInteraction.php';
require_once 'vendor/autoload.php';
require_once __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__); //important .env key
$dotenv->load(); //important .env key

$db = new DatabaseInteraction();
$conn = $db->connect();

if (!isset($_GET['data_id'])) {
    die("Error: No data_id provided.");
}

$data_id = $_GET['data_id'];
$feedback_data = $db->getFeedbackDataByDataId($data_id);

if (!$feedback_data) {
    die("Error: Feedback data not found.");
}



// Retrieve user_id associated with the data_id
$user_id = $feedback_data[0]['user_id'];

// Use all records for summary generation
$all_feedback = $feedback_data;
$text_to_analyze = implode("\n", array_column($all_feedback, 'feedback_data'));

// Keep first 10 records for display purposes
$limited_feedback = array_slice($feedback_data, 0, 10);

// Get domain category from feedback_data
$domain_category = !empty($feedback_data[0]['domain_category']) ? $feedback_data[0]['domain_category'] : 'Collections';

// If domain category is not set, use Collections as default
if (empty($domain_category)) {
    $domain_category = 'Collections';
}

$_SESSION['domain_cat'] = $domain_category;

// Generate Summary
$summary = summarize_feedback($text_to_analyze, $domain_category);

// Save summary to database
save_summary_to_database($data_id, $summary, $domain_category, $user_id);

// Format Summary
$formatted_summary = format_summary($summary);

// Enqueue comments for processing if they haven't been analyzed and are not already in the queue
foreach ($feedback_data as $row) {
    $comment = $row['feedback_data'];
    $csat = $row['csat'];
    $nps = $row['nps'];
    $pid = $row['pid'];
    if (!$db->isCommentAnalyzed($comment, $data_id) && !$db->isCommentInQueue($comment, $data_id)) {
        $db->enqueueComment($comment, $data_id, $user_id, $csat, $nps, $pid);
    }
}

// Check if the user wants to export the PDF
if (isset($_GET['export_pdf'])) {
    generate_pdf($data_id, $domain_category, $summary, $limited_feedback);
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Analysis</title>
    <link rel="icon" href="assets/images/hricon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts-wordcloud@2.1.0/dist/echarts-wordcloud.min.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">
</head>

<style>
    /* Navbar Styles */
    nav {
        background-color: #b98b04; /* Dark brown */
        color: white;
        padding: 5px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
	.small-list {
    font-size: 12px;
}
.small-list li {
    font-size: 12px;
}
</style>

<body class="bg-gray-100">
    <!-- Navbar -->
    <nav>
        <div class="logo">
            <img src="assets/images/logo.png" alt="Logo" style="max-height: 60px; width: auto; display: block;">
        </div>
        <div class="ml-10 flex items-baseline space-x-4 nav-right">
            <span class="text-white">Hello, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
            <a href="upload.php" class="text-white hover:text-indigo-300">Upload Feedback</a>
            <a href="logout.php" class="text-white hover:text-indigo-300">Logout</a>
        </div>
    </nav>

    <div class="container mx-auto flex items-center justify-center min-h-screen p-2 py-2">
        <div class="bg-yellow-100 p-2 rounded-lg shadow-md max-w-8xl mx-auto border border-black">
            <h1 class="text-3xl font-bold text-gray-800 text-center mb-6 ">📊 Feedback Analysis</h1>

            <h3 class="text-lg font-semibold text-gray-700">📂 Data ID: <span class="text-indigo-600"><?php echo htmlspecialchars($data_id); ?></span></h3>

            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-700">📂 Domain Category: <span class="text-xl font-medium text-indigo-600"><?php echo htmlspecialchars($domain_category); ?></span></h3>

                <?php
                // Get and display the selected main drivers
                $selected_main_drivers = $db->getDataMainDrivers($data_id);
                if (!empty($selected_main_drivers)):
                ?>
                <h3 class="text-lg font-semibold text-gray-700 mt-2">🔍 Selected Primary Issues (L1):</h3>
                <ul class="list-disc pl-6 text-indigo-600" style="margin:0; padding-left:20px; line-height:1;">
                    <?php foreach ($selected_main_drivers as $driver): ?>
                        <li style="margin:0; padding:0; line-height:1;"><?php echo htmlspecialchars($driver); ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>
            </div>

            <div class="bg-yellow-50 p-2 rounded-lg shadow-md max-w-8xl mx-auto border border-black">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">📝 Overall Summary:</h3>
                <?php echo $formatted_summary; ?>
            </div>

            <br>

            <div class="bg-yellow-50 p-2 rounded-lg shadow-md border border-black">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">💬 Sample Comments (Showing First 10):</h3>
                <ul class="list-disc pl-6 space-y-1 text-gray-600">
                    <?php foreach ($limited_feedback as $row): ?>
                        <li class="small-list border-l-4 border-indigo-600 pl-3"><?php echo htmlspecialchars($row['feedback_data']); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="window.location.href='dashboard.php'" class="bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-indigo-700 transition">
                    🔙 Dashboard
                </button>
                <button onclick="window.location.href='?data_id=<?php echo $data_id; ?>&export_pdf=1'" class="bg-green-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-green-700 transition">
                    📄 Export as PDF
                </button>
            </div>
        </div>
    </div>

    <footer class="bg-yellow-300 py-2 shadow-md mt-auto">
        <div class="container mx-auto px-9 flex justify-between items-center">
            <div>
                <p class="text-gray-700 text-xs"><b>&#169 <?php echo date("Y"); ?> Bill Gosling Outsourcing. All rights reserved.</b></p>
                <p class="text-gray-700 text-xs">This system is for the use of authorized personnel only and by accessing this system you hereby consent to the system being monitored by the Company. Any unauthorized use will be considered a breach of the Company's Information Security policies and may also be unlawful under law. Bill Gosling Outsourcing reserves the right to take any action including disciplinary action or legal proceedings in a court of law against persons involved in the violation of the access restrictions herein. The Information Is 'Internal, Restricted'.</p>
            </div>
        </div>
    </footer>
</body>
</html>

<?php
function generate_pdf($data_id, $domain_category, $summary, $limited_feedback) {
    global $db;
    require_once __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php'; // Include TCPDF

    // Create new PDF document
    $pdf = new TCPDF();
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetTitle("Feedback Analysis Report - Data ID: $data_id");
    $pdf->SetHeaderData('', 0, "Feedback Analysis Report", "Data ID: $data_id");
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetAutoPageBreak(TRUE, 15);
    $pdf->AddPage();

    // Set default font (Arial Unicode for better character support)
    $pdf->SetFont('dejavusans', '', 10);

    // Fix special character issue by decoding HTML entities properly
    $formatted_summary = nl2br(html_entity_decode($summary, ENT_QUOTES, 'UTF-8'));

    // PDF Content with inline styles
    $html = "
    <style>
        h1 { text-align: center; font-size: 18px; font-weight: bold; color: #2d3748; margin-bottom: 15px; }
        h3 { font-size: 14px; font-weight: bold; color: #4a5568; margin-bottom: 5px; }
        p { font-size: 12px; color: #333; margin-bottom: 8px; }
        span { color: #6574cd; font-weight: bold; }
        ul { padding-left: 15px; font-size: 12px; }
        li { margin-bottom: 5px; padding-left: 5px; border-left: 3px solid #6574cd; padding-left: 10px; }
        .section { background-color: #f1f8ff; padding: 10px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #ddd; }
    </style>

    <div class='section'>
        <h1> Feedback Analysis Report</h1>
        <h3>Data ID: <span>$data_id</span></h3>
    </div>

    <div class='section'>
        <h3>Domain Category:</h3>
        <p><span>$domain_category</span></p>
    </div>";

    // Add selected main drivers to PDF if available
    $selected_main_drivers = $db->getDataMainDrivers($data_id);
    if (!empty($selected_main_drivers)) {
        $main_drivers_list = implode(", ", $selected_main_drivers);
        $html .= "
    <div class='section'>
        <h3>Selected Primary Issues (L1):</h3>
        <p><span>$main_drivers_list</span></p>
    </div>";
    }

    $html .= "
    <div class='section'>
        <h3>Overall Summary:</h3>
        <p>$formatted_summary</p>
    </div>

    <div class='section'>
        <h3>Sample Comments (Showing First 10):</h3>
        <ul>";

    // Add feedback comments to PDF
    foreach ($limited_feedback as $row) {
        $comment = html_entity_decode($row['feedback_data'], ENT_QUOTES, 'UTF-8'); // Fix encoding
        $html .= "<li>$comment</li>";
    }

    $html .= "</ul></div>";

    // Write HTML to PDF
    $pdf->writeHTML($html, true, false, true, false, '');
    $pdf->Output("Feedback_Analysis_$data_id.pdf", 'D'); // Download PDF
    exit();
}

// Function to generate summary

function summarize_feedback($text, $domain_category) {
    try {
        global $db, $data_id;

        // Get the user-selected main drivers for this data_id
        $selected_main_drivers = $db->getDataMainDrivers($data_id);

        // Format the main drivers as a comma-separated list
        $main_drivers_list = !empty($selected_main_drivers) ? implode(", ", $selected_main_drivers) : "";

        // Create a prompt that includes the user-selected domain category and main drivers
        $prompt = "**Domain Category:** $domain_category\n\n$text\n\n";

        // Include the user-selected domain category
        $prompt .= "- **Domain Category**: $domain_category (User selected this domain category)\n";

        // Include the user-selected main drivers if available
        if (!empty($main_drivers_list)) {
            $prompt .= "- **Primary Issues (L1)**: $main_drivers_list (User selected these main drivers)\n";
            $prompt .= "- **Important Note**: Please ONLY use these user-selected Primary Issues (L1) in your analysis. Do not introduce any other main drivers.\n\n";

            // Provide explicit structure for the response
            $prompt .= "REQUIRED RESPONSE FORMAT:\n";
            $prompt .= "Your response MUST follow this exact structure:\n\n";

            // Example structure for domain category and primary issue
            $prompt .= "Domain Category: [Domain Category Name]\n\n";
            $prompt .= "Primary Issue (L1): [Primary Issue Name]\n\n";
            $prompt .= "Subcategory (L2 & L3): [L2 Category] - [L3 Specific Issue], [L2 Category] - [L3 Specific Issue], [L2 Category] - [L3 Specific Issue]\n\n";
            $prompt .= "Summary of Customer Feedback:\n[Concise summary of key concerns and sentiments]\n\n";
            $prompt .= "Pain Points & Customer Frustrations:\n1. [First pain point with description]\n2. [Second pain point with description]\n3. [Third pain point with description]\n\n";
            $prompt .= "Suggested Improvements:\n1. [First suggestion with description]\n2. [Second suggestion with description]\n3. [Third suggestion with description]\n\n";

            $prompt .= "IMPORTANT: For each Primary Issue (L1), list ALL subcategories (L2 & L3) ONLY ONCE at the TOP of your response, as shown in the example above. DO NOT repeat the subcategories in each section.\n\n";

            // Add examples of subcategories for each selected main driver
            $prompt .= "Examples of subcategories for each Primary Issue:\n";
            foreach ($selected_main_drivers as $driver) {
                if (strpos($driver, 'Billing & Payment') !== false) {
                    $prompt .= "* Billing & Payment Issues:\n";
                    $prompt .= "  Subcategory (L2 & L3): Payment Problems - Payment processing delays, Unexpected Fees - Hidden charges, Billing Discrepancies - Incorrect amounts\n";
                } elseif (strpos($driver, 'Customer Service') !== false) {
                    $prompt .= "* Customer Service & Resolution Issues:\n";
                    $prompt .= "  Subcategory (L2 & L3): Poor Communication - Unclear information, Lack of Follow-up - Unresolved tickets, Unprofessional Behavior - Rude representatives\n";
                } elseif (strpos($driver, 'Policy & Procedures') !== false) {
                    $prompt .= "* Policy & Procedures:\n";
                    $prompt .= "  Subcategory (L2 & L3): Unclear Policies - Confusing terms, Inconsistent Application - Different rules for different customers, Excessive Bureaucracy - Too many steps\n";
                } elseif (strpos($driver, 'Transfer & Process') !== false) {
                    $prompt .= "* Transfer & Process Issues:\n";
                    $prompt .= "  Subcategory (L2 & L3): Service Delays - Long wait times, Lack of Transparency - No status updates, Equipment Problems - Missing or damaged items\n";
                } elseif (strpos($driver, 'Tools & Technology') !== false) {
                    $prompt .= "* Tools & Technology:\n";
                    $prompt .= "  Subcategory (L2 & L3): System Errors - Application crashes, Website Issues - Navigation problems, App Functionality - Missing features\n";
                }
            }
            $prompt .= "\nYou MUST follow this structure and include subcategories (L2 & L3) for each Primary Issue in your analysis.\n";
        } else {
            $prompt .= "- **Primary Issue (L1)**: (Categorize the main issue like Billing & Payment Issues, Customer Service and Resolution Issues, Policy & Procedures, Tools & Technology, etc.)\n";
            $prompt .= "- **Subcategory (L2 & L3)**: (Further classify the issue, e.g., Payment Problems - Payment issues, Process Failures - Cancellation not processed, System Errors - Technical failures, etc.)\n";
        }

        $prompt .= "\n";
        $prompt .= "###\n**Summary of Customer Feedback:**\n(Provide a concise summary highlighting the key concerns and sentiments expressed by the customer.)\n\n";
        $prompt .= "###\n**Pain Points & Customer Frustrations:**\n(List specific pain points based on the feedback, including unmet expectations, inefficiencies, or negative experiences.)\n\n";
        $prompt .= "###\n**Suggested Improvements:**\n(Provide actionable recommendations to enhance customer experience, resolve issues, or improve processes.)";

        $client = OpenAI::client($_ENV['OPENAI_API_KEY']);
        $response = $client->chat()->create([
            "model" => "gpt-4o-mini",
            "messages" => [
                ["role" => "system", "content" => "You are an assistant that categorizes customer feedback based on domain-specific insights. Your role is to analyze the provided customer comments and classify them into appropriate categories. You will summarize key concerns, identify pain points, and suggest actionable insights based on the feedback provided. You must ONLY use the user-selected domain category and primary issues provided in your analysis.

CRITICAL FORMATTING REQUIREMENT: You MUST list ALL subcategories (L2 & L3) ONLY ONCE at the TOP of your response, immediately after the Primary Issue (L1) section. DO NOT repeat subcategories in each section.

Example format:
Domain Category: [Domain Category Name]

Primary Issue (L1): [Primary Issue Name]

Subcategory (L2 & L3): [L2 Category] - [L3 Specific Issue], [L2 Category] - [L3 Specific Issue], [L2 Category] - [L3 Specific Issue]

Summary of Customer Feedback:
[Concise summary of key concerns and sentiments]

Pain Points & Customer Frustrations:
1. [First pain point with description]
2. [Second pain point with description]
3. [Third pain point with description]

Suggested Improvements:
1. [First suggestion with description]
2. [Second suggestion with description]
3. [Third suggestion with description]

You MUST follow this exact structure for your analysis."],
                ["role" => "user", "content" => $prompt]
            ],
            "max_tokens" => 4000,
            "temperature" => 0.2
        ]);
        return trim($response->choices[0]->message->content);
    } catch (\Exception $e) {
        error_log("Error in summarize_feedback: " . $e->getMessage());
        return 'Summary generation failed';
    }
}


// Function to format summary
function format_summary($summary) {
    // Bold text in **bold**
    $summary = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $summary);

    // Format Domain Category
    $summary = preg_replace('/Domain Category:\s*(.*?)(?=\n|$)/', '<div class="domain-category"><span class="label">Domain Category:</span> <span class="value">$1</span></div>', $summary);

    // Format Primary Issue
    $summary = preg_replace('/Primary Issue \(L1\):\s*(.*?)(?=\n|$)/', '<div class="primary-issue"><span class="label">Primary Issue (L1):</span> <span class="value">$1</span></div>', $summary);

    // Format Subcategory line
    $summary = preg_replace('/Subcategory \(L2 & L3\):\s*(.*?)(?=\n|$)/', '<div class="subcategory-line"><span class="label">Subcategory (L2 & L3):</span> <span class="value">$1</span></div>', $summary);

    // Add Overall Summary section
    $summary = preg_replace('/Summary of Customer Feedback:/', '<div class="overall-summary">Overall Summary:</div><h3 class="section-header">Summary of Customer Feedback:</h3>', $summary);
    $summary = preg_replace('/Pain Points & Customer Frustrations:/', '<h3 class="section-header">Pain Points & Customer Frustrations:</h3>', $summary);
    $summary = preg_replace('/Suggested Improvements:/', '<h3 class="section-header">Suggested Improvements:</h3>', $summary);

    // Add separator between primary issues
    $summary = preg_replace('/--\s*\n/', '<div class="issue-separator"></div>', $summary);

    // Format numbered lists in Pain Points and Suggested Improvements
    $summary = preg_replace('/(\d+)\.\s+(.*?)(?=\n|$)/', '<div class="numbered-item"><span class="number">$1.</span> <span class="content">$2</span></div>', $summary);

    // Add comprehensive CSS for better formatting
    $css = <<<CSS
    <style>
        /* Base styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.2;
            margin: 0;
            padding: 0;
        }

        /* Data ID and Domain Category */
        .data-id, .domain-category {
            background-color: #fffbeb; /* Yellow background */
            padding: 2px 5px;
            margin: 0 0 2px 0;
            font-size: 12px;
            line-height: 1.2;
            font-weight: bold;
        }

        /* Selected Primary Issues */
        .selected-issues {
            background-color: #ffffff;
            padding: 2px 5px;
            margin: 0 0 2px 0;
            font-size: 12px;
            line-height: 1.2;
        }

        /* Primary Issue */
        .primary-issue {
            background-color: #fffbeb;
            padding: 2px 5px;
            margin: 0 0 2px 0;
            font-size: 12px;
            line-height: 1.2;
            color: #0000FF; /* Blue text */
            font-weight: bold;
        }

        /* Subcategory line */
        .subcategory-line {
            font-weight: bold;
            background-color: #fffbeb;
            padding: 2px 5px;
            margin: 0 0 2px 0;
            font-size: 12px;
            line-height: 1.2;
            color: #0000FF; /* Blue text */
        }

        /* Labels */
        .label {
            font-weight: bold;
            color: #000000;
            font-size: 12px;
        }

        /* Values */
        .value {
            color: #0000FF;
            font-size: 12px;
        }

        /* Section headers */
        .section-header {
            color: #0000FF; /* Blue text */
            font-size: 12px;
            font-weight: bold;
            margin-top: 1px;
            margin-bottom: 0;
            border-bottom: 1px solid #000000;
            padding-bottom: 0;
            line-height: 1.2;
        }

        /* Numbered items */
        .numbered-item {
            margin: 0;
            padding: 0;
            line-height: 1.1;
            font-size: 12px;
        }

        .number {
            font-weight: bold;
            color: #000000;
            margin-right: 2px;
            font-size: 12px;
        }

        .content {
            color: #000000;
            font-size: 11px;
        }

        /* Overall Summary */
        .overall-summary {
            font-weight: bold;
            margin: 5px 0 2px 0;
            padding: 2px 0;
            font-size: 12px;
            line-height: 1.2;
        }

        /* Issue separator */
        .issue-separator {
            border-top: 1px dashed #cbd5e0;
            margin: 1px 0;
        }
    </style>
CSS;

    // Replace newlines with <br> but also add a style to reduce line height
    $formatted = nl2br($summary);
    // Replace <br> tags with ones that have minimal or no line height
    $formatted = str_replace('<br />', '<br style="line-height:0.1; margin:0; padding:0; display:inline;" />', $formatted);
    // Add additional style to make content more compact
    $formatted = "<div style=\"line-height:1.0; font-size:12px;\">$formatted</div>";
    return "$css$formatted";
}

// Function to save summary to database
function save_summary_to_database($data_id, $summary, $domain_category, $user_id) {
    global $db;

    try {
        // Check if a summary already exists for this data_id
        $check_query = "SELECT id FROM feedback_summaries WHERE data_id = :data_id";
        $check_stmt = $db->connect()->prepare($check_query);
        $check_stmt->bindParam(':data_id', $data_id);
        $check_stmt->execute();

        if ($check_stmt->rowCount() > 0) {
            // Update existing summary
            $query = "UPDATE feedback_summaries
                     SET summary = :summary,
                         domain_category = :domain_category,
                         user_id = :user_id,
                         created_at = CURRENT_TIMESTAMP
                     WHERE data_id = :data_id";
        } else {
            // Insert new summary
            $query = "INSERT INTO feedback_summaries
                     (data_id, summary, domain_category, user_id)
                     VALUES
                     (:data_id, :summary, :domain_category, :user_id)";
        }

        $stmt = $db->connect()->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':summary', $summary);
        $stmt->bindParam(':domain_category', $domain_category);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        error_log("Error saving summary to database: " . $e->getMessage());
        return false;
    }
}
?>