<?php
/**
 * <PERSON><PERSON>t to find missing records and enqueue them in comment_queue for normal AI processing
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set high memory and execution time limits
ini_set('memory_limit', '1G');
ini_set('max_execution_time', 3600); // 1 hour

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to process
$data_id = isset($argv[1]) ? $argv[1] : '682b54974bab6';

// Log file
$log_file = 'enqueue_missing.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    
    // Also output to console
    echo $log_entry;
}

// Connect to database
try {
    log_message("Starting to find and enqueue missing records for data_id: $data_id");
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count,
            (SELECT COUNT(*) FROM comment_queue WHERE data_id = :data_id) as queue_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $queue_count = $counts['queue_count'];
    $missing_count = $feedback_count - $analyzed_count - $queue_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, In queue: $queue_count, Missing: $missing_count");
    
    if ($missing_count <= 0) {
        log_message("No missing records for data_id: $data_id");
        exit(0);
    }
    
    // Find records that are not in analyzed_comments or comment_queue
    log_message("Finding records that are not in analyzed_comments or comment_queue...");
    
    $missingQuery = "
        SELECT fd.*
        FROM feedback_data fd
        WHERE fd.data_id = :data_id
        AND NOT EXISTS (
            SELECT 1 FROM analyzed_comments ac 
            WHERE ac.data_id = fd.data_id AND ac.comment = fd.feedback_data
        )
        AND NOT EXISTS (
            SELECT 1 FROM comment_queue cq 
            WHERE cq.data_id = fd.data_id AND cq.comment = fd.feedback_data
        )
        LIMIT 2000
    ";
    
    $missingStmt = $conn->prepare($missingQuery);
    $missingStmt->bindParam(':data_id', $data_id);
    $missingStmt->execute();
    $missingRecords = $missingStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Found " . count($missingRecords) . " missing records");
    
    if (count($missingRecords) == 0) {
        log_message("No specific missing records found despite count discrepancy. This suggests duplicate records or a counting issue.");
        exit(0);
    }
    
    // Process missing records by enqueueing them
    $enqueued = 0;
    $failed = 0;
    $total = count($missingRecords);
    
    log_message("Enqueueing $total missing records for normal AI processing...");
    
    foreach ($missingRecords as $index => $record) {
        log_message("Processing record ID: " . $record['id'] . " (" . ($index + 1) . " of $total)");
        
        try {
            // Insert into comment_queue
            $insertQuery = "INSERT INTO comment_queue (
                comment, data_id, user_id, csat, nps, pid, status,
                domain_category, resolution_comment, internal_scores, feedback_submit_date,
                feedback_month, feedback_time, lob, vendor, location, partner,
                dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending',
                :domain_category, :resolution_comment, :internal_scores, :feedback_submit_date,
                :feedback_month, :feedback_time, :lob, :vendor, :location, :partner,
                :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
            )";
            
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindParam(':comment', $record['feedback_data']);
            $insertStmt->bindParam(':data_id', $record['data_id']);
            $insertStmt->bindParam(':user_id', $record['user_id']);
            $insertStmt->bindParam(':csat', $record['csat']);
            $insertStmt->bindParam(':nps', $record['nps']);
            $insertStmt->bindParam(':pid', $record['pid']);
            $insertStmt->bindParam(':domain_category', $record['domain_category']);
            $insertStmt->bindParam(':resolution_comment', $record['resolution_comment']);
            $insertStmt->bindParam(':internal_scores', $record['internal_scores']);
            $insertStmt->bindParam(':feedback_submit_date', $record['feedback_submit_date']);
            $insertStmt->bindParam(':feedback_month', $record['feedback_month']);
            $insertStmt->bindParam(':feedback_time', $record['feedback_time']);
            $insertStmt->bindParam(':lob', $record['lob']);
            $insertStmt->bindParam(':vendor', $record['vendor']);
            $insertStmt->bindParam(':location', $record['location']);
            $insertStmt->bindParam(':partner', $record['partner']);
            $insertStmt->bindParam(':dummy_1', $record['dummy_1']);
            $insertStmt->bindParam(':dummy_2', $record['dummy_2']);
            $insertStmt->bindParam(':dummy_3', $record['dummy_3']);
            $insertStmt->bindParam(':dummy_4', $record['dummy_4']);
            $insertStmt->bindParam(':dummy_5', $record['dummy_5']);
            
            $result = $insertStmt->execute();
            
            if ($result) {
                $enqueued++;
                if ($enqueued % 10 == 0 || $enqueued == $total) {
                    log_message("Enqueued $enqueued of $total records");
                }
            } else {
                $failed++;
                log_message("Failed to enqueue record ID: " . $record['id'] . " - Error: " . json_encode($insertStmt->errorInfo()));
            }
        } catch (PDOException $e) {
            $failed++;
            log_message("ERROR: Failed to enqueue record ID: " . $record['id'] . " - " . $e->getMessage());
            
            // If it's a duplicate entry error, log it but don't retry
            if ($e->getCode() == '23000') {
                log_message("Record ID: " . $record['id'] . " appears to be a duplicate");
            }
        }
        
        // Sleep briefly every 100 records to avoid overloading the database
        if ($index % 100 == 99) {
            log_message("Sleeping briefly to avoid overloading the database...");
            usleep(500000); // 0.5 seconds
        }
    }
    
    log_message("Processing completed - Enqueued: $enqueued, Failed: $failed");
    
    // Get updated counts
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $queue_count = $counts['queue_count'];
    $missing_count = $feedback_count - $analyzed_count - $queue_count;
    
    log_message("Updated counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, In queue: $queue_count, Missing: $missing_count");
    
    if ($missing_count > 0) {
        log_message("There are still $missing_count missing records. You may need to run this script again.");
    } else {
        log_message("All records have been enqueued successfully! They will be processed by the AI system normally.");
    }
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
