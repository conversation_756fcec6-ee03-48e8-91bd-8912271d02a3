files:
  "/opt/elasticbeanstalk/hooks/appdeploy/post/99-setup-record-monitor.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      set -euxo pipefail

      LOG_FILE="/var/log/eb-record-monitor-setup.log"
      exec > >(tee -a ${LOG_FILE}) 2>&1

      echo "Starting record monitor setup..."

      # Copy the run-monitor.sh script to the appropriate location
      cp /var/app/current/run-monitor.sh /usr/local/bin/run-monitor.sh
      chmod +x /usr/local/bin/run-monitor.sh

      # Create the cron job for the record monitor
      cat <<EOF > /etc/cron.d/record-monitor
      # Run the record monitor every 15 minutes
      */15 * * * * root /usr/local/bin/run-monitor.sh
      EOF
      chmod 644 /etc/cron.d/record-monitor

      # Create log directories if they don't exist
      mkdir -p /var/log/app_logs
      chmod 755 /var/log/app_logs

      # Create log files with appropriate permissions
      touch /var/log/record_monitor.log
      chmod 666 /var/log/record_monitor.log

      touch /var/log/record_monitor_detail.log
      chmod 666 /var/log/record_monitor_detail.log

      touch /var/app/current/monitor_errors.log
      chmod 666 /var/app/current/monitor_errors.log

      # Create symlinks to make logs accessible to the web interface
      ln -sf /var/log/record_monitor.log /var/app/current/monitor_system.log || true
      ln -sf /var/log/record_monitor_detail.log /var/app/current/monitor_detail.log || true

      # Ensure web server can read the log files
      chmod 644 /var/app/current/monitor_system.log || true
      chmod 644 /var/app/current/monitor_detail.log || true

      # Add log rotation for the monitor logs
      cat <<EOF > /etc/logrotate.d/record-monitor
      /var/log/record_monitor.log {
          daily
          rotate 7
          missingok
          notifempty
          compress
          delaycompress
          copytruncate
      }

      /var/log/record_monitor_detail.log {
          daily
          rotate 7
          missingok
          notifempty
          compress
          delaycompress
          copytruncate
      }

      /var/app/current/monitor_errors.log {
          daily
          rotate 7
          missingok
          notifempty
          compress
          delaycompress
          copytruncate
      }
      EOF

      # Restart cron service
      systemctl restart crond || true

      echo "Record monitor setup completed."
