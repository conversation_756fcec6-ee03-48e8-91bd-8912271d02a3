{"name": "openai-php/client", "description": "OpenAI PHP is a supercharged PHP API client that allows you to interact with the Open AI API", "keywords": ["php", "openai", "sdk", "codex", "GPT-3", "DALL-E", "api", "client", "natural", "language", "processing"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>"}], "require": {"php": "^8.1.0", "php-http/discovery": "^1.20.0", "php-http/multipart-stream-builder": "^1.4.2", "psr/http-client": "^1.0.3", "psr/http-client-implementation": "^1.0.1", "psr/http-factory-implementation": "*", "psr/http-message": "^1.1.0|^2.0.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.9.2", "guzzlehttp/psr7": "^2.7.0", "laravel/pint": "^1.18.1", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^7.11.0|^8.5.0", "pestphp/pest": "^2.36.0|^3.5.0", "pestphp/pest-plugin-arch": "^2.7|^3.0", "pestphp/pest-plugin-type-coverage": "^2.8.7|^3.1.0", "phpstan/phpstan": "^1.12.7", "symfony/var-dumper": "^6.4.11|^7.1.5"}, "autoload": {"psr-4": {"OpenAI\\": "src/"}, "files": ["src/OpenAI.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": false}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:type-coverage": "pest --type-coverage --min=100", "test:unit": "pest --colors=always", "test": ["@test:lint", "@test:types", "@test:type-coverage", "@test:unit"]}}