# Record Monitor Web Interface

This document explains how to use the web interface for monitoring the record processing system.

## Overview

The Record Monitor Web Interface provides a convenient way to:

1. View processing statistics for all data_ids
2. Identify data_ids with missing records
3. View logs from the record monitor
4. Manually trigger the record monitor if needed

## Accessing the Interface

The interface is available at:

```
http://vocapplicationphp-env.eba-uciq7fpp.us-east-1.elasticbeanstalk.com/monitor_status.php
```

## Security

The interface is protected by basic authentication:

- **Username**: admin
- **Password**: monitor@2025

**IMPORTANT**: You should change the default password in the `monitor_status.php` file for security reasons.

## Features

### 1. Processing Statistics

The interface displays:

- Total number of feedback records
- Total number of analyzed comments
- Number of records currently in the queue
- Data IDs with missing records (showing completion percentage)
- Recent processing activity

### 2. Monitor Logs

The interface provides access to various log files:

- **System Log**: High-level log of monitor runs (from /var/log/record_monitor.log)
- **Detailed Log**: Detailed log of all operations (from /var/log/record_monitor_detail.log)
- **Error Log**: Errors encountered during monitoring
- **Manual Trigger Output**: Output from manually triggered monitor runs

### 3. Manual Trigger

You can manually trigger the record monitor by clicking the "Manually Trigger Monitor" button. This is useful if you want to:

- Process records immediately without waiting for the scheduled run
- Test that the monitor is working correctly
- Force a re-check of a specific data_id

## Troubleshooting

If you encounter issues with the interface:

1. **No logs appear**: 
   - Check that the monitor has run at least once
   - Verify that the symlinks to log files are correctly set up
   - Check file permissions on the log files

2. **Cannot trigger monitor manually**:
   - Ensure PHP has permission to execute background processes
   - Check the error logs for any issues

3. **Statistics not showing**:
   - Verify database connection settings
   - Check for errors in the PHP error log

## How It Works

The web interface:

1. Connects to the same database as the main application
2. Runs queries to gather statistics about record processing
3. Reads log files to display monitoring information
4. Can trigger the record monitor script in the background

## Customization

You can customize the interface by:

1. Changing the authentication credentials in `monitor_status.php`
2. Modifying the queries in the `getProcessingStats()` function
3. Adjusting the refresh rate or adding auto-refresh functionality
4. Adding additional statistics or visualizations

## Integration with AWS Elastic Beanstalk

The interface is automatically set up during deployment through the `.ebextensions/99-record-monitor.config` file, which:

1. Creates symlinks to make log files accessible to the web interface
2. Sets appropriate permissions on the log files
3. Configures the cron job to run the monitor regularly
