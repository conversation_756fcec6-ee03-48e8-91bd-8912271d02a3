# Feedback Upload Templates

This directory contains several template files that you can use for uploading feedback data to the system.

## Available Templates

1. **sample_upload_template.csv**
   - CSV format template with sample data
   - Can be opened in Excel or any spreadsheet software

2. **sample_upload_template.html**
   - HTML format template that can be opened in a web browser
   - Can be copied to Excel or saved directly as an Excel file

3. **generate_excel_template.php**
   - PHP script that generates an Excel template file
   - Access this file through your web browser to download the Excel template

## How to Use the Templates

### Using the CSV Template
1. Open `sample_upload_template.xlsx` in Excel or another spreadsheet program
2. Replace the sample data with your actual feedback data
3. Save the file (preferably as .xlsx format)
4. Upload the file through the feedback upload page

### Using the HTML Template
1. Open `sample_upload_template.html` in your web browser
2. Follow one of these methods:
   - Method 1: Select all content (Ctrl+A), copy (Ctrl+C), open Excel, and paste (Ctrl+V)
   - Method 2: Use your browser's "Save As" feature and select "Webpage, Complete", then open the saved file in Excel
3. Replace the sample data with your actual feedback data
4. Save as an Excel file (.xlsx)
5. Upload the file through the feedback upload page

### Using the PHP Generator
1. Access `generate_excel_template.php` through your web browser
2. Your browser will download an Excel file with the template
3. Open the downloaded file in Excel
4. Replace the sample data with your actual feedback data
5. Save the file
6. Upload the file through the feedback upload page

## Required Fields
The following fields are required in your upload file:
- `pid` - Participant/Customer ID
- `comments` - Feedback text
- `CSAT` - Customer Satisfaction score
- `NPS` - Net Promoter Score

## Optional Fields
The following fields are optional but can provide more detailed analysis:
- `resolution_comment` - Comments about how the issue was resolved
- `internal_scores` - Internal scoring metrics
- `feedback_submit_date` - Date and time when feedback was submitted (format: YYYY-MM-DD HH:MM:SS)
- `lob` - Line of Business
- `vendor` - Vendor information
- `location` - Location information
- `partner` - Partner information
- `dummy-1` through `dummy-5` - Additional custom fields for your specific needs

For more detailed instructions on the upload process, please refer to the `UPLOAD_INSTRUCTIONS.md` file.
