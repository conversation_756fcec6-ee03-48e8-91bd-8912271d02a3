/**
 * Filter Coordinator
 * Manages bidirectional cascading filter updates and validation
 */

class FilterCoordinator {
    constructor() {
        this.filters = {
            domain_category: 'all',
            data_id: 'all',
            sentiment: 'all',
            start_date: '',
            end_date: '',
            product_type: 'all',
            channel_type: 'all',
            team: 'all',
            resolution_status: 'all'
        };
        
        this.filterElements = {};
        this.isUpdating = false;
        this.updateQueue = [];
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        
        this.initializeFilterElements();
        this.attachEventListeners();
        this.loadInitialFilterOptions();
    }
    
    /**
     * Initialize filter element references
     */
    initializeFilterElements() {
        this.filterElements = {
            domain_category: document.getElementById('domainCategoryDropdown'),
            data_id: document.getElementById('dataIdDropdown'),
            sentiment: document.getElementById('sentimentDropdown'),
            start_date: document.getElementById('startDateFilter'),
            end_date: document.getElementById('endDateFilter'),
            product_type: document.getElementById('productTypeDropdown'),
            channel_type: document.getElementById('channelTypeDropdown'),
            team: document.getElementById('teamDropdown'),
            resolution_status: document.getElementById('resolutionStatusDropdown')
        };
        
        // Remove any null elements
        Object.keys(this.filterElements).forEach(key => {
            if (!this.filterElements[key]) {
                delete this.filterElements[key];
            }
        });
    }
    
    /**
     * Attach event listeners to filter elements
     */
    attachEventListeners() {
        Object.keys(this.filterElements).forEach(filterType => {
            const element = this.filterElements[filterType];
            if (element) {
                element.addEventListener('change', (event) => {
                    this.handleFilterChange(filterType, event.target.value);
                });
            }
        });
    }
    
    /**
     * Handle filter change events
     */
    async handleFilterChange(filterType, newValue) {
        if (this.isUpdating) {
            this.updateQueue.push({ filterType, newValue });
            return;
        }
        
        this.isUpdating = true;
        this.showLoadingState(filterType);
        
        try {
            // Update internal filter state
            const oldValue = this.filters[filterType];
            this.filters[filterType] = newValue;
            
            // Log the filter change
            console.log(`Filter changed: ${filterType} from "${oldValue}" to "${newValue}"`);
            
            // Update other filter options based on new state
            await this.updateDependentFilters(filterType);
            
            // Validate the new combination
            const isValid = await this.validateFilterCombination();
            
            if (!isValid) {
                this.showNoDataWarning();
            } else {
                this.hideNoDataWarning();
            }
            
            // Trigger dashboard data refresh
            this.triggerDataRefresh();
            
        } catch (error) {
            console.error('Error handling filter change:', error);
            this.showErrorMessage('Failed to update filters. Please try again.');
        } finally {
            this.hideLoadingState(filterType);
            this.isUpdating = false;
            
            // Process queued updates
            if (this.updateQueue.length > 0) {
                const nextUpdate = this.updateQueue.shift();
                setTimeout(() => {
                    this.handleFilterChange(nextUpdate.filterType, nextUpdate.newValue);
                }, 100);
            }
        }
    }
    
    /**
     * Update dependent filters based on current state
     */
    async updateDependentFilters(changedFilterType) {
        // Exclude the changed filter and date filters from updates
        const otherFilterTypes = Object.keys(this.filterElements).filter(type =>
            type !== changedFilterType && !['start_date', 'end_date'].includes(type)
        );

        if (otherFilterTypes.length > 0) {
            try {
                const response = await this.fetchFilterOptions(otherFilterTypes);

                if (response.success) {
                    this.updateFilterDropdowns(response.options);
                }
            } catch (error) {
                console.error('Error updating dependent filters:', error);
            }
        }
    }
    
    /**
     * Fetch filter options from server
     */
    async fetchFilterOptions(filterTypes) {
        const cacheKey = this.generateCacheKey(filterTypes, this.filters);
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }
        
        const params = new URLSearchParams();
        params.append('action', 'batch_options');
        params.append('filter_types', filterTypes.join(','));
        
        // Add current filter values
        Object.keys(this.filters).forEach(key => {
            if (this.filters[key] && this.filters[key] !== 'all' && this.filters[key] !== '') {
                params.append(key, this.filters[key]);
            }
        });
        
        const response = await fetch(`filter_options.php?${params.toString()}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Cache the response
        this.cache.set(cacheKey, {
            data: data,
            timestamp: Date.now()
        });
        
        return data;
    }
    
    /**
     * Update filter dropdown options
     */
    updateFilterDropdowns(optionsData) {
        Object.keys(optionsData).forEach(filterType => {
            const element = this.filterElements[filterType];
            if (!element) return;
            
            const currentValue = element.value;
            const options = optionsData[filterType];
            
            // Clear existing options
            element.innerHTML = '';
            
            // Add "All" option for non-date filters
            if (!['start_date', 'end_date'].includes(filterType)) {
                const allOption = document.createElement('option');
                allOption.value = 'all';
                allOption.textContent = `All ${this.getFilterDisplayName(filterType)}`;
                element.appendChild(allOption);
            }
            
            // Add available options
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                element.appendChild(optionElement);
            });
            
            // Restore current value if it's still available
            if (options.includes(currentValue) || currentValue === 'all') {
                element.value = currentValue;
            } else {
                // If current value is no longer available, reset to 'all'
                element.value = 'all';
                this.filters[filterType] = 'all';
            }
            
            // Add visual indicator if options were filtered
            this.updateFilterIndicator(filterType, options.length);
        });
    }
    
    /**
     * Validate current filter combination
     */
    async validateFilterCombination() {
        try {
            const params = new URLSearchParams();
            params.append('action', 'validate_combination');
            
            // Add current filter values
            Object.keys(this.filters).forEach(key => {
                if (this.filters[key] && this.filters[key] !== 'all' && this.filters[key] !== '') {
                    params.append(key, this.filters[key]);
                }
            });
            
            const response = await fetch(`filter_options.php?${params.toString()}`);
            const data = await response.json();
            
            return data.success && data.has_data;
        } catch (error) {
            console.error('Error validating filter combination:', error);
            return true; // Assume valid on error to avoid blocking UI
        }
    }
    
    /**
     * Load initial filter options
     */
    async loadInitialFilterOptions() {
        try {
            // Exclude date filters from initial load as they don't have predefined options
            const allFilterTypes = Object.keys(this.filterElements).filter(type =>
                !['start_date', 'end_date'].includes(type)
            );

            if (allFilterTypes.length > 0) {
                const response = await this.fetchFilterOptions(allFilterTypes);

                if (response.success) {
                    this.updateFilterDropdowns(response.options);
                }
            }
        } catch (error) {
            console.error('Error loading initial filter options:', error);
        }
    }
    
    /**
     * Show loading state for a filter
     */
    showLoadingState(filterType) {
        const element = this.filterElements[filterType];
        if (element) {
            element.disabled = true;
            element.style.opacity = '0.6';
            
            // Add loading spinner if not already present
            const loadingSpinner = element.parentNode.querySelector('.filter-loading');
            if (!loadingSpinner) {
                const spinner = document.createElement('div');
                spinner.className = 'filter-loading';
                spinner.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                spinner.style.cssText = 'position: absolute; right: 10px; top: 50%; transform: translateY(-50%); pointer-events: none;';
                element.parentNode.style.position = 'relative';
                element.parentNode.appendChild(spinner);
            }
        }
    }
    
    /**
     * Hide loading state for a filter
     */
    hideLoadingState(filterType) {
        const element = this.filterElements[filterType];
        if (element) {
            element.disabled = false;
            element.style.opacity = '1';
            
            // Remove loading spinner
            const loadingSpinner = element.parentNode.querySelector('.filter-loading');
            if (loadingSpinner) {
                loadingSpinner.remove();
            }
        }
    }
    
    /**
     * Show no data warning
     */
    showNoDataWarning() {
        let warning = document.getElementById('no-data-warning');
        if (!warning) {
            warning = document.createElement('div');
            warning.id = 'no-data-warning';
            warning.className = 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4';
            warning.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm">
                            The current filter combination returns no data. Please adjust your filters.
                        </p>
                    </div>
                </div>
            `;
            
            // Insert after filter section
            const filterSection = document.querySelector('.filter-section');
            if (filterSection) {
                filterSection.parentNode.insertBefore(warning, filterSection.nextSibling);
            }
        }
        warning.style.display = 'block';
    }
    
    /**
     * Hide no data warning
     */
    hideNoDataWarning() {
        const warning = document.getElementById('no-data-warning');
        if (warning) {
            warning.style.display = 'none';
        }
    }
    
    /**
     * Show error message
     */
    showErrorMessage(message) {
        // Create or update error message element
        let errorDiv = document.getElementById('filter-error-message');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'filter-error-message';
            errorDiv.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4';
            
            const filterSection = document.querySelector('.filter-section');
            if (filterSection) {
                filterSection.parentNode.insertBefore(errorDiv, filterSection.nextSibling);
            }
        }
        
        errorDiv.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm">${message}</p>
                </div>
            </div>
        `;
        errorDiv.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }
    
    /**
     * Update filter indicator
     */
    updateFilterIndicator(filterType, optionCount) {
        const element = this.filterElements[filterType];
        if (!element) return;

        // Remove existing indicator
        const existingIndicator = element.parentNode.querySelector('.filter-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Only show indicator if options are significantly limited AND other filters are active
        const activeFiltersCount = Object.values(this.filters).filter(value =>
            value && value !== 'all' && value !== ''
        ).length;

        // Show indicator only if:
        // 1. There are active filters (not initial load)
        // 2. Options are significantly limited (less than 5)
        // 3. Not for date filters
        if (activeFiltersCount > 0 && optionCount < 5 && !['start_date', 'end_date'].includes(filterType)) {
            const indicator = document.createElement('span');
            indicator.className = 'filter-indicator';
            indicator.innerHTML = `<i class="fas fa-filter text-blue-500" title="${optionCount} options available"></i>`;
            indicator.style.cssText = 'position: absolute; right: 30px; top: 50%; transform: translateY(-50%); pointer-events: none; font-size: 12px;';
            element.parentNode.appendChild(indicator);
        }
    }
    
    /**
     * Trigger dashboard data refresh
     */
    triggerDataRefresh() {
        // Call existing dashboard refresh function if available
        if (typeof fetchData === 'function') {
            fetchData();
        } else if (typeof debouncedFetchData === 'function') {
            debouncedFetchData();
        }
        
        // Also update main drivers and table if functions exist
        if (typeof fetchMainDrivers === 'function') {
            fetchMainDrivers();
        }
        
        if (typeof updateTable === 'function') {
            updateTable();
        }
    }
    
    /**
     * Generate cache key
     */
    generateCacheKey(filterTypes, filters) {
        return JSON.stringify({ filterTypes: filterTypes.sort(), filters });
    }
    
    /**
     * Get filter display name
     */
    getFilterDisplayName(filterType) {
        const displayNames = {
            domain_category: 'Domain Categories',
            data_id: 'Data IDs',
            sentiment: 'Sentiments',
            product_type: 'Product Types',
            channel_type: 'Channel Types',
            team: 'Teams',
            resolution_status: 'Resolution Status'
        };
        
        return displayNames[filterType] || filterType.replace('_', ' ');
    }
    
    /**
     * Get current filter state
     */
    getCurrentFilters() {
        return { ...this.filters };
    }
    
    /**
     * Set filter programmatically
     */
    setFilter(filterType, value) {
        if (this.filterElements[filterType]) {
            this.filterElements[filterType].value = value;
            this.handleFilterChange(filterType, value);
        }
    }
    
    /**
     * Clear all filters
     */
    async clearAllFilters() {
        console.log('Clearing all filters...');

        // Temporarily disable updating to prevent cascading events
        this.isUpdating = true;

        try {
            // Reset internal filter state
            Object.keys(this.filters).forEach(filterType => {
                if (['start_date', 'end_date'].includes(filterType)) {
                    this.filters[filterType] = '';
                } else {
                    this.filters[filterType] = 'all';
                }
            });

            // Reset DOM elements without triggering change events
            Object.keys(this.filterElements).forEach(filterType => {
                const element = this.filterElements[filterType];
                if (element) {
                    if (['start_date', 'end_date'].includes(filterType)) {
                        element.value = '';
                    } else {
                        element.value = 'all';
                    }

                    // Remove any filter indicators
                    const indicator = element.parentNode.querySelector('.filter-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });

            // Clear cache
            this.clearCache();

            // Reload initial filter options
            await this.loadInitialFilterOptions();

            // Hide any warning messages
            this.hideNoDataWarning();

            // Trigger data refresh after clearing filters
            this.triggerDataRefresh();

            console.log('All filters cleared successfully');

        } catch (error) {
            console.error('Error clearing filters:', error);
        } finally {
            this.isUpdating = false;
        }
    }
    
    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }
}

// Initialize filter coordinator when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.filterCoordinator = new FilterCoordinator();
    console.log('Filter Coordinator initialized');
});
