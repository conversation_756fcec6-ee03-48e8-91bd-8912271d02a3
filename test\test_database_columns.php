<?php
// Test script to verify database column names and sample data
require_once 'config.php';
require_once 'DatabaseInteraction.php';

$db = new DatabaseInteraction();
$conn = $db->connect();

if (!$conn) {
    die("Database connection failed");
}

echo "<h2>Database Column Verification</h2>";

// Test 1: Verify column names exist
echo "<h3>1. Column Names Verification</h3>";
$columns_to_check = ['csat', 'nps', 'verbitm', 'internal_scores', 'pid', 'feedback_submit_date'];

foreach ($columns_to_check as $column) {
    try {
        $query = "SELECT $column FROM analyzed_comments LIMIT 1";
        $result = $conn->query($query);
        echo "✅ Column '$column' exists<br>";
    } catch (Exception $e) {
        echo "❌ Column '$column' does not exist: " . $e->getMessage() . "<br>";
    }
}

// Test 2: Sample data from key columns
echo "<h3>2. Sample Data from Key Columns</h3>";
try {
    $query = "SELECT data_id, sentiment, csat, nps, verbitm, domain_category, user_id 
              FROM analyzed_comments 
              WHERE csat IS NOT NULL AND nps IS NOT NULL 
              LIMIT 5";
    $result = $conn->query($query);
    
    if ($result && $result->rowCount() > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>data_id</th><th>sentiment</th><th>csat</th><th>nps</th><th>verbitm (first 50 chars)</th><th>domain_category</th><th>user_id</th></tr>";
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['data_id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['sentiment']) . "</td>";
            echo "<td>" . htmlspecialchars($row['csat']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nps']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['verbitm'], 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($row['domain_category']) . "</td>";
            echo "<td>" . htmlspecialchars($row['user_id']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No sample data found with CSAT and NPS values.";
    }
} catch (Exception $e) {
    echo "Error fetching sample data: " . $e->getMessage();
}

// Test 3: Check data distribution
echo "<h3>3. Data Distribution Check</h3>";
try {
    // Count records with CSAT values
    $query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE csat IS NOT NULL";
    $result = $conn->query($query)->fetch(PDO::FETCH_ASSOC);
    echo "Records with CSAT values: " . $result['count'] . "<br>";
    
    // Count records with NPS values
    $query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE nps IS NOT NULL";
    $result = $conn->query($query)->fetch(PDO::FETCH_ASSOC);
    echo "Records with NPS values: " . $result['count'] . "<br>";
    
    // Count records with verbatim text
    $query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE verbitm IS NOT NULL AND verbitm != ''";
    $result = $conn->query($query)->fetch(PDO::FETCH_ASSOC);
    echo "Records with verbatim text: " . $result['count'] . "<br>";
    
    // Count records by sentiment
    $query = "SELECT sentiment, COUNT(*) as count FROM analyzed_comments GROUP BY sentiment";
    $result = $conn->query($query);
    echo "<br><strong>Sentiment Distribution:</strong><br>";
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo $row['sentiment'] . ": " . $row['count'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "Error checking data distribution: " . $e->getMessage();
}

echo "<h3>4. Index Creation Commands (Run these manually)</h3>";
echo "<p>The corrected database_performance_indexes.sql file now contains the proper column names:</p>";
echo "<ul>";
echo "<li><code>csat</code> and <code>nps</code> (not csat_score/nps_score)</li>";
echo "<li><code>verbitm</code> (not verbatim)</li>";
echo "<li>Added indexes for <code>pid</code>, <code>internal_scores</code>, and <code>feedback_submit_date</code></li>";
echo "</ul>";

echo "<p><strong>To apply the indexes, run this command in your MySQL console:</strong></p>";
echo "<code>mysql -u username -p feedback_final_10 < database_performance_indexes.sql</code>";

?>
