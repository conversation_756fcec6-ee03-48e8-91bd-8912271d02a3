<?php
/**
 * <PERSON><PERSON>t to export all missing records for data_id 682b54974bab6 to a CSV file
 * This will help identify why these records aren't being processed
 */

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

// Data ID to analyze
$data_id = '682b54974bab6';
$csv_file = 'missing_records.csv';

// Function to log messages
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
    // Force output buffer flush
    if (ob_get_level() > 0) {
        ob_flush();
    }
    flush();
}

// Connect to database
try {
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    log_message("Connected to database");
    
    // Get counts
    $countQuery = "
        SELECT 
            (SELECT COUNT(*) FROM feedback_data WHERE data_id = :data_id) as feedback_count,
            (SELECT COUNT(*) FROM analyzed_comments WHERE data_id = :data_id) as analyzed_count
    ";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindParam(':data_id', $data_id);
    $countStmt->execute();
    $counts = $countStmt->fetch(PDO::FETCH_ASSOC);
    
    $feedback_count = $counts['feedback_count'];
    $analyzed_count = $counts['analyzed_count'];
    $missing_count = $feedback_count - $analyzed_count;
    
    log_message("Counts - Feedback data: $feedback_count, Analyzed comments: $analyzed_count, Missing: $missing_count");
    
    // Get all feedback data records
    log_message("Retrieving all feedback data records...");
    $allFeedbackQuery = "SELECT * FROM feedback_data WHERE data_id = :data_id";
    $allFeedbackStmt = $conn->prepare($allFeedbackQuery);
    $allFeedbackStmt->bindParam(':data_id', $data_id);
    $allFeedbackStmt->execute();
    $allFeedback = $allFeedbackStmt->fetchAll(PDO::FETCH_ASSOC);
    
    log_message("Retrieved " . count($allFeedback) . " feedback records");
    
    // Get all analyzed comments
    log_message("Retrieving all analyzed comments...");
    $allAnalyzedQuery = "SELECT comment FROM analyzed_comments WHERE data_id = :data_id";
    $allAnalyzedStmt = $conn->prepare($allAnalyzedQuery);
    $allAnalyzedStmt->bindParam(':data_id', $data_id);
    $allAnalyzedStmt->execute();
    $analyzedComments = $allAnalyzedStmt->fetchAll(PDO::FETCH_COLUMN);
    
    log_message("Retrieved " . count($analyzedComments) . " analyzed comments");
    
    // Find missing records
    log_message("Identifying missing records...");
    $missingRecords = [];
    
    foreach ($allFeedback as $record) {
        if (!in_array($record['feedback_data'], $analyzedComments)) {
            $missingRecords[] = $record;
        }
    }
    
    log_message("Found " . count($missingRecords) . " missing records");
    
    // Export missing records to CSV
    if (count($missingRecords) > 0) {
        log_message("Exporting missing records to $csv_file...");
        
        $fp = fopen($csv_file, 'w');
        
        // Write header
        $header = array_keys($missingRecords[0]);
        fputcsv($fp, $header);
        
        // Write data
        foreach ($missingRecords as $record) {
            // Add a field for feedback_data length
            $record['feedback_data_length'] = strlen($record['feedback_data']);
            
            // Remove the actual feedback_data to keep the CSV manageable
            $truncatedData = substr($record['feedback_data'], 0, 100);
            if (strlen($record['feedback_data']) > 100) {
                $truncatedData .= "... [truncated]";
            }
            $record['feedback_data_sample'] = $truncatedData;
            
            // Write to CSV
            fputcsv($fp, $record);
        }
        
        fclose($fp);
        log_message("Exported " . count($missingRecords) . " records to $csv_file");
    }
    
    // Try to process a few missing records
    if (count($missingRecords) > 0) {
        log_message("\nAttempting to process 5 missing records...");
        $sampleSize = min(5, count($missingRecords));
        
        for ($i = 0; $i < $sampleSize; $i++) {
            $record = $missingRecords[$i];
            log_message("\nProcessing record ID: " . $record['id']);
            
            // First try to insert into comment_queue
            try {
                $insertQuery = "INSERT INTO comment_queue (
                    comment, data_id, user_id, csat, nps, pid, status
                ) VALUES (
                    :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending'
                )";
                
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bindParam(':comment', $record['feedback_data']);
                $insertStmt->bindParam(':data_id', $data_id);
                $insertStmt->bindParam(':user_id', $record['user_id']);
                $insertStmt->bindParam(':csat', $record['csat']);
                $insertStmt->bindParam(':nps', $record['nps']);
                $insertStmt->bindParam(':pid', $record['pid']);
                
                $result = $insertStmt->execute();
                
                if ($result) {
                    log_message("Successfully inserted record ID: " . $record['id'] . " into comment_queue");
                    
                    // Verify the record was inserted
                    $verifyQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE data_id = :data_id AND comment = :comment";
                    $verifyStmt = $conn->prepare($verifyQuery);
                    $verifyStmt->bindParam(':data_id', $data_id);
                    $verifyStmt->bindParam(':comment', $record['feedback_data']);
                    $verifyStmt->execute();
                    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($verifyResult['count'] > 0) {
                        log_message("Verified record ID: " . $record['id'] . " was successfully inserted into comment_queue");
                    } else {
                        log_message("WARNING: Record ID: " . $record['id'] . " was not found in comment_queue after insertion");
                    }
                } else {
                    log_message("Failed to insert record ID: " . $record['id'] . " into comment_queue - Error: " . json_encode($insertStmt->errorInfo()));
                }
            } catch (PDOException $e) {
                log_message("ERROR: Failed to insert record ID: " . $record['id'] . " into comment_queue - " . $e->getMessage());
                
                // Try direct insertion into analyzed_comments as fallback
                try {
                    log_message("Attempting direct insertion into analyzed_comments...");
                    
                    $directQuery = "INSERT INTO analyzed_comments (
                        comment, data_id, user_id, csat, nps, pid,
                        main_driver, sub_driver, sentiment
                    ) VALUES (
                        :comment, :data_id, :user_id, :csat, :nps, :pid,
                        'Auto-Generated', 'Auto-Generated', 'Neutral'
                    )";
                    
                    $directStmt = $conn->prepare($directQuery);
                    $directStmt->bindParam(':comment', $record['feedback_data']);
                    $directStmt->bindParam(':data_id', $data_id);
                    $directStmt->bindParam(':user_id', $record['user_id']);
                    $directStmt->bindParam(':csat', $record['csat']);
                    $directStmt->bindParam(':nps', $record['nps']);
                    $directStmt->bindParam(':pid', $record['pid']);
                    
                    $directResult = $directStmt->execute();
                    
                    if ($directResult) {
                        log_message("Successfully inserted record ID: " . $record['id'] . " directly into analyzed_comments");
                    } else {
                        log_message("Failed to insert record ID: " . $record['id'] . " directly into analyzed_comments - Error: " . json_encode($directStmt->errorInfo()));
                    }
                } catch (PDOException $e2) {
                    log_message("ERROR: Failed to insert record ID: " . $record['id'] . " directly into analyzed_comments - " . $e2->getMessage());
                }
            }
        }
    }
    
    log_message("\nExport and analysis completed");
    
} catch (PDOException $e) {
    log_message("Database error: " . $e->getMessage());
}
