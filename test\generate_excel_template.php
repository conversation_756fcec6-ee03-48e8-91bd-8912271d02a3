<?php
// Set headers for Excel file download
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="feedback_upload_template.xlsx"');
header('Cache-Control: max-age=0');

// Create the data
$data = [
    ['pid', 'comments', 'CSAT', 'NPS', 'resolution_comment', 'internal_scores', 'feedback_submit_date', 'lob', 'vendor', 'location', 'partner', 'dummy-1', 'dummy-2', 'dummy-3', 'dummy-4', 'dummy-5'],
    ['1', 'This is a sample comment about billing issues. I was charged twice for the same service.', '8', '7', 'Issue resolved by refunding the duplicate charge', '85', '2023-05-15 14:30:00', 'Finance', 'ABC Corp', 'New York', 'Partner A', 'Additional Info 1', 'Additional Info 2', 'Additional Info 3', 'Additional Info 4', 'Additional Info 5'],
    ['2', 'The customer service representative was very helpful and resolved my issue quickly.', '9', '9', 'Customer was satisfied with the resolution', '95', '2023-05-16 10:15:00', 'Retail', 'XYZ Inc', 'Chicago', 'Partner B', 'Additional Info 1', 'Additional Info 2', 'Additional Info 3', 'Additional Info 4', 'Additional Info 5'],
    ['3', 'I had trouble accessing my account online. The website kept showing an error.', '5', '4', 'IT team fixed the login issue', '70', '2023-05-17 09:45:00', 'Banking', 'DEF Ltd', 'Los Angeles', 'Partner C', 'Additional Info 1', 'Additional Info 2', 'Additional Info 3', 'Additional Info 4', 'Additional Info 5'],
    ['4', 'The delivery was late by three days and the package was damaged.', '3', '2', 'Replacement product sent with expedited shipping', '60', '2023-05-18 16:20:00', 'E-commerce', 'GHI Corp', 'Dallas', 'Partner D', 'Additional Info 1', 'Additional Info 2', 'Additional Info 3', 'Additional Info 4', 'Additional Info 5'],
    ['5', 'Your marketing team promised a discount but it wasn\'t applied to my purchase.', '4', '3', 'Discount applied retroactively', '65', '2023-05-19 11:30:00', 'Marketing', 'JKL Inc', 'Miami', 'Partner E', 'Additional Info 1', 'Additional Info 2', 'Additional Info 3', 'Additional Info 4', 'Additional Info 5']
];

// Output the data as a tab-separated file (Excel can open this)
foreach ($data as $row) {
    echo implode("\t", $row) . "\n";
}
?>
