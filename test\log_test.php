<?php
// Simple script to test logging

// Define log file
$logFile = 'test_log.txt';

// Function to log messages
function logToFile($message, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Start logging
logToFile("Starting log test", $logFile);

// Test database connection
logToFile("Testing database connection", $logFile);

// Database credentials
$host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
$dbname = 'goslabsprojectwave2_1';
$username = 'deploy';
$password = 'fNas2{7T8oBj';

try {
    // Create PDO connection
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
    $conn = new PDO($dsn, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    logToFile("Connected successfully to database: $dbname on host: $host", $logFile);
    
    // Test SELECT on feedback_data
    $testQuery = "SELECT COUNT(*) as count FROM feedback_data WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    logToFile("SELECT test on feedback_data: Found " . $testResult['count'] . " records for data_id 682b54974bab6", $logFile);
    
    // Test SELECT on analyzed_comments
    $testQuery = "SELECT COUNT(*) as count FROM analyzed_comments WHERE data_id = '682b54974bab6'";
    $testStmt = $conn->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    logToFile("SELECT test on analyzed_comments: Found " . $testResult['count'] . " records for data_id 682b54974bab6", $logFile);
    
    // Test INSERT on comment_queue with a transaction (will be rolled back)
    $conn->beginTransaction();
    try {
        $testInsertQuery = "INSERT INTO comment_queue (comment, data_id, user_id, csat, nps, pid, status) 
                           VALUES ('TEST_PERMISSION_CHECK', '682b54974bab6', 1, 0, 0, '0', 'pending')";
        $testInsertStmt = $conn->prepare($testInsertQuery);
        $testInsertResult = $testInsertStmt->execute();
        
        if ($testInsertResult) {
            logToFile("INSERT test on comment_queue: Successful", $logFile);
            
            // Verify the record was inserted
            $verifyQuery = "SELECT COUNT(*) as count FROM comment_queue WHERE comment = 'TEST_PERMISSION_CHECK'";
            $verifyStmt = $conn->prepare($verifyQuery);
            $verifyStmt->execute();
            $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            logToFile("Verification: Found " . $verifyResult['count'] . " test records in comment_queue", $logFile);
        } else {
            logToFile("INSERT test on comment_queue failed: " . json_encode($testInsertStmt->errorInfo()), $logFile);
        }
        
        // Always rollback the test insert
        $conn->rollBack();
        logToFile("Test transaction rolled back successfully", $logFile);
    } catch (PDOException $e) {
        $conn->rollBack();
        logToFile("INSERT test on comment_queue failed: " . $e->getMessage(), $logFile);
    }
    
    logToFile("Database test completed successfully", $logFile);
    
} catch (PDOException $e) {
    logToFile("Connection failed: " . $e->getMessage(), $logFile);
}

logToFile("Log test completed", $logFile);

// Output the log file path
echo "Log file created at: " . realpath($logFile) . "\n";
