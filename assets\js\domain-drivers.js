document.addEventListener('DOMContentLoaded', function() {
    const domainCategorySelect = document.getElementById('domain_category');
    const mainDriversContainer = document.getElementById('main_drivers_container');
    const domainCategoryInput = document.getElementById('domain_category_input');
    const mainDriversHiddenInputs = document.getElementById('main_drivers_hidden_inputs');
    const uploadForm = document.getElementById('uploadForm');

    if (!domainCategorySelect || !mainDriversContainer) {
        console.error('Required elements not found');
        return;
    }

    // Function to fetch main drivers for a domain category
    function fetchMainDrivers(domainCategory) {
        if (!domainCategory) {
            mainDriversContainer.innerHTML = '<p class="text-gray-500 col-span-full">Please select a domain category first</p>';
            return;
        }

        // Make an AJAX request to get main drivers
        console.log('Fetching main drivers for domain category:', domainCategory);
        fetch('get_main_drivers.php?domain_category=' + encodeURIComponent(domainCategory))
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Main drivers received:', data);
                displayMainDrivers(data);
            })
            .catch(error => {
                console.error('Error fetching main drivers:', error);
                mainDriversContainer.innerHTML = '<p class="text-red-500 col-span-full">Error loading main drivers: ' + error.message + '</p>';

                // Try to load main drivers directly from JavaScript as a fallback
                console.log('Attempting to load main drivers from fallback data');
                const fallbackDrivers = getFallbackMainDrivers(domainCategory);
                if (fallbackDrivers && fallbackDrivers.length > 0) {
                    console.log('Using fallback main drivers:', fallbackDrivers);
                    displayMainDrivers(fallbackDrivers);
                }
            });
    }

    // Fallback function to get main drivers if the AJAX request fails
    function getFallbackMainDrivers(domainCategory) {
        const drivers = {
            'Collections': [
                'Billing & Payment Issues',
                'Customer Service & Resolution Issues',
                'Customer Support & Service',
                'Policy & Procedures',
                'Tools & Technology',
                'Transfer & Process Issues'
            ],
            'Customer Service': [
                'Agent Behavior',
                'Agent Driven',
                'Call Quality Issues',
                'Communication Clarity',
                'Customer Driven',
                'First Call Resolution',
                'Knowledge Management',
                'Policy & Procedures',
                'Resolution Efficiency',
                'Tools & Technology'
            ],
            'Finance & Banking': [
                'Account Access Issues',
                'Account Application',
                'Billing & Payment Issues',
                'Customer Service & Resolution Issues',
                'Dispute Handling',
                'Loan & Credit Issues',
                'Policy & Procedures',
                'Refund Delays',
                'Refund Policies & Eligibility',
                'Tools & Technology',
                'Transaction Errors',
                'Verification Issues',
				'Agent Driven',
				'Customer Driven'
            ],
            'Retail & E-commerce': [
                'Customer Service',
                'Delivery Experience',
                'Order Fulfillment',
                'Product Quality',
                'Returns & Refunds',
                'Website Experience'
            ],
            'Sales & Marketing': [
                'Brand Representation',
                'Customer Service & Resolution Issues',
                'Customer Support & Service',
                'Follow-up Communication',
                'Lack of Clear Communication',
                'Lead Handling',
                'Marketing',
                'Perceived Company Integrity',
                'Product Knowledge Gaps',
                'Promotions & Discounts'
            ],
            'Technical Support': [
                'Hardware Issues',
                'Self-Service Portal Issues',
                'Software Problems',
                'System Downtime',
                'Tool Integration',
                'User Accessibility'
            ],
			'Finance & Banking-New' : [
                'Account Access Activities',
                'Account Application Processes',
                'Agent Interaction Elements',
                'Billing & Payment-Related Matters',
                'Customer-Initiated Factors',
                'Service Experience & Interaction Outcomes',
                'Dispute Management Activities',
                'Loan & Credit Considerations',
                'Operational Policies & Procedures',
                'Processing Timelines for Refunds',	
                'Refund Eligibility & Guideline Aspects',
                'System Tools & Platform Use',
                'Transaction-Related Items',					
                'Verification & Identity Confirmation Steps'
            ]
        };

        return drivers[domainCategory] || [];
    }

    // Function to display main drivers
    function displayMainDrivers(drivers) {
        mainDriversContainer.innerHTML = '';

        if (drivers.length === 0) {
            mainDriversContainer.innerHTML = '<p class="text-gray-500 col-span-full">No main drivers found for this domain category</p>';
            return;
        }

        // Create checkboxes for each main driver
        drivers.forEach(driver => {
            const driverDiv = document.createElement('div');
            driverDiv.className = 'flex items-center';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = 'main_drivers[]';
            checkbox.value = driver;
            checkbox.id = 'driver_' + driver.replace(/\s+/g, '_').toLowerCase();
            checkbox.className = 'mr-2';
            checkbox.checked = true; // Select all checkboxes by default

            // Add event listener to trigger scale configuration visibility check
            checkbox.addEventListener('change', function() {
                // Trigger the scale configuration visibility check if the function exists
                if (typeof window.checkScaleConfigVisibility === 'function') {
                    window.checkScaleConfigVisibility();
                }
                // Also trigger form validation if the function exists
                if (typeof window.validateForm === 'function') {
                    window.validateForm();
                }
            });

            const label = document.createElement('label');
            label.htmlFor = checkbox.id;
            label.textContent = driver;
            label.className = 'text-sm';

            driverDiv.appendChild(checkbox);
            driverDiv.appendChild(label);
            mainDriversContainer.appendChild(driverDiv);
        });

        // Add "Select All" checkbox
        const selectAllDiv = document.createElement('div');
        selectAllDiv.className = 'flex items-center col-span-full mt-2';

        const selectAllCheckbox = document.createElement('input');
        selectAllCheckbox.type = 'checkbox';
        selectAllCheckbox.id = 'select_all_drivers';
        selectAllCheckbox.className = 'mr-2';
        selectAllCheckbox.checked = true; // Select "Select All" checkbox by default

        const selectAllLabel = document.createElement('label');
        selectAllLabel.htmlFor = 'select_all_drivers';
        selectAllLabel.textContent = 'Select All';
        selectAllLabel.className = 'text-sm font-semibold';

        selectAllDiv.appendChild(selectAllCheckbox);
        selectAllDiv.appendChild(selectAllLabel);
        mainDriversContainer.appendChild(selectAllDiv);

        // Add event listener for "Select All" checkbox
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = mainDriversContainer.querySelectorAll('input[type="checkbox"][name="main_drivers[]"]');
            checkboxes.forEach(cb => {
                cb.checked = selectAllCheckbox.checked;
            });

            // Trigger scale configuration visibility check
            if (typeof window.checkScaleConfigVisibility === 'function') {
                window.checkScaleConfigVisibility();
            }
        });

        // Trigger scale configuration check after drivers are loaded (since all are selected by default)
        setTimeout(function() {
            if (typeof window.checkScaleConfigVisibility === 'function') {
                window.checkScaleConfigVisibility();
            }
        }, 500);
    }

    // Event listener for domain category change
    domainCategorySelect.addEventListener('change', function() {
        const selectedCategory = this.value;
        fetchMainDrivers(selectedCategory);

        // Update the hidden input field with the selected domain category
        if (domainCategoryInput) {
            domainCategoryInput.value = selectedCategory;
            console.log('Updated hidden domain category input with:', selectedCategory);
        }
    });

    // If a domain category is already selected, fetch its main drivers
    if (domainCategorySelect.value) {
        console.log("Domain category already selected:", domainCategorySelect.value);
        fetchMainDrivers(domainCategorySelect.value);
    } else {
        console.log("No domain category selected on page load");
    }

    // Force trigger a change event to load main drivers if needed
    setTimeout(function() {
        if (domainCategorySelect.value) {
            console.log("Triggering change event for domain category");
            domainCategorySelect.dispatchEvent(new Event('change'));
        }
    }, 500);

    // Add event listener for form submission to ensure domain category and main drivers are included
    if (uploadForm) {
        uploadForm.addEventListener('submit', function() {
            // Update the hidden domain category input
            if (domainCategoryInput && domainCategorySelect) {
                domainCategoryInput.value = domainCategorySelect.value;
            }

            // Get selected main drivers
            const selectedDrivers = [];
            const driverCheckboxes = document.querySelectorAll('input[type="checkbox"][name="main_drivers[]"]:checked');
            driverCheckboxes.forEach(function(checkbox) {
                selectedDrivers.push(checkbox.value);
            });

            // Clear previous hidden inputs
            if (mainDriversHiddenInputs) {
                mainDriversHiddenInputs.innerHTML = '';

                // Create hidden inputs for each selected main driver
                selectedDrivers.forEach(function(driver) {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'main_drivers[]';
                    hiddenInput.value = driver;
                    mainDriversHiddenInputs.appendChild(hiddenInput);
                });
            }

            console.log('Form submission with domain category:', domainCategoryInput.value);
            console.log('Selected main drivers:', selectedDrivers);
        });
    }
});
