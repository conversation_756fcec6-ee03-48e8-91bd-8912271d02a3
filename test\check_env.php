<?php
// Simple script to check environment variables

// Enable error display
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Load environment variables directly
if (file_exists(__DIR__ . '/.env')) {
    echo "Found .env file\n";
    
    // Read the .env file manually
    $envFile = file_get_contents(__DIR__ . '/.env');
    echo "Contents of .env file:\n";
    echo "----------------------------------------\n";
    
    // Mask sensitive information
    $lines = explode("\n", $envFile);
    foreach ($lines as $line) {
        if (empty(trim($line)) || strpos($line, '#') === 0) {
            echo $line . "\n";
            continue;
        }
        
        $parts = explode('=', $line, 2);
        if (count($parts) === 2) {
            $key = trim($parts[0]);
            $value = trim($parts[1]);
            
            if (strpos($key, 'PASS') !== false || strpos($key, 'KEY') !== false || strpos($key, 'SECRET') !== false) {
                echo "$key=[HIDDEN]\n";
            } else {
                echo "$key=$value\n";
            }
        } else {
            echo $line . "\n";
        }
    }
    
    echo "----------------------------------------\n";
} else {
    echo ".env file not found\n";
}

// Try to load environment variables using Dotenv
echo "\nTrying to load environment variables using Dotenv...\n";
require_once __DIR__ . '/vendor/autoload.php';

try {
    if (class_exists('Dotenv\\Dotenv')) {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
        $dotenv->load();
        echo "Dotenv loaded successfully\n";
    } else {
        echo "Dotenv class not found\n";
    }
} catch (Exception $e) {
    echo "Error loading Dotenv: " . $e->getMessage() . "\n";
}

// Check if environment variables are set
echo "\nChecking environment variables:\n";
$envVars = [
    'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS',
    'DB_HOST_ALT', 'DB_NAME_ALT', 'DB_USER_ALT', 'DB_PASS_ALT'
];

foreach ($envVars as $var) {
    if (isset($_ENV[$var])) {
        $value = $_ENV[$var];
        if (strpos($var, 'PASS') !== false) {
            $value = '[HIDDEN]';
        }
        echo "$var: $value\n";
    } else {
        echo "$var: Not set\n";
    }
}

// Check if getenv() returns the variables
echo "\nChecking getenv() function:\n";
foreach ($envVars as $var) {
    $value = getenv($var);
    if ($value !== false) {
        if (strpos($var, 'PASS') !== false) {
            $value = '[HIDDEN]';
        }
        echo "$var: $value\n";
    } else {
        echo "$var: Not set\n";
    }
}

echo "\nDone checking environment variables\n";
