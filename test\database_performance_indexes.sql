-- Performance optimization indexes for dashboard.php
-- Run these queries manually in your MySQL database to improve dashboard performance

-- Main performance index for most dashboard queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_performance
ON analyzed_comments (data_id, sentiment, domain_category, user_id);

-- Index for time-series queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_created_at
ON analyzed_comments (created_at, data_id, user_id);

-- Index for main driver queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_main_driver
ON analyzed_comments (main_driver, data_id, user_id);

-- Index for sub driver queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_sub_driver
ON analyzed_comments (sub_driver, data_id, user_id);

-- Index for sentiment analysis
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_sentiment_analysis
ON analyzed_comments (sentiment, data_id, user_id, created_at);

-- Index for LOB sentiment queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_lob
ON analyzed_comments (lob, sentiment, data_id, user_id);

-- Index for Vendor sentiment queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_vendor
ON analyzed_comments (vendor, sentiment, data_id, user_id);

-- Index for Location sentiment queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_location
ON analyzed_comments (location, sentiment, data_id, user_id);

-- Index for Partner sentiment queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_partner
ON analyzed_comments (partner, sentiment, data_id, user_id);

-- Index for CSAT and NPS impact queries (corrected column names)
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_scores
ON analyzed_comments (csat, nps, data_id, user_id);

-- Index for internal scores
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_internal_scores
ON analyzed_comments (internal_scores, data_id, user_id);

-- Composite index for word cloud queries (corrected column name)
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_verbatim
ON analyzed_comments (data_id, user_id, sentiment, verbitm(100));

-- Index for PID queries (for duplicate checking)
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_pid
ON analyzed_comments (pid, data_id, user_id);

-- Index for feedback date queries
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_feedback_date
ON analyzed_comments (feedback_submit_date, data_id, user_id);

-- Index for domain category filtering
CREATE INDEX IF NOT EXISTS idx_analyzed_comments_domain_filter
ON analyzed_comments (domain_category, data_id, user_id, sentiment);

-- Show current indexes after creation
SHOW INDEX FROM analyzed_comments;
